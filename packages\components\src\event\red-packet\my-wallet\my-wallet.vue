<!--
* @Description: 我的钱包
-->
<template>
  <Resizable
    v-if="showMyWallet"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    @click="handleOpenWallet"
  >
    <!-- 入口 -->
    <div
      class="my-wallet"
      :class="{ 'my-wallet-affix': setting.affix && !adminStore.editable }"
      :style="entryStyle"
    />

    <!-- 弹窗 -->
    <Popup
      z-index="99"
      :show="openWallet"
      :lock-scroll="false"
      v-model:setting="setting.modal"
      @close="handleCloseWallet"
      @ok="handleCashOut"
    >
      <!-- 红包余额 -->
      <Resizable
        v-model:x="setting.modal.balance.x"
        v-model:y="setting.modal.balance.y"
        v-model:width="setting.modal.balance.width"
        v-model:height="setting.modal.balance.height"
        class="my-wallet-balance"
        :style="{
          backgroundImage: `url(${setting.modal.balance.bgImage})`,
        }"
      >
        <ResizableProvider>
          <!-- 金额 -->
          <UiText v-model:setting="setting.modal.balance.num" :interpolation="interpolation" />
        </ResizableProvider>
      </Resizable>

      <!-- 提现列表，或条件只是为了兼容旧数据 -->
      <template v-if="setting.modal.amount">
        <Resizable
          v-model:x="setting.modal.amount.list.x"
          v-model:y="setting.modal.amount.list.y"
          v-model:width="setting.modal.amount.list.width"
          v-model:height="setting.modal.amount.list.height"
          class="my-wallet-list"
        >
          <Resizable
            v-for="item in setting.modal.amount.items"
            v-model:x="setting.modal.amount.item.x"
            v-model:y="setting.modal.amount.item.y"
            v-model:width="setting.modal.amount.item.width"
            v-model:height="setting.modal.amount.item.height"
            :movable="false"
            class="my-wallet-list-item"
            :class="{ 'my-wallet-list-item__active': item.num === amount }"
            :style="{
              backgroundImage: `url(${setting.modal.amount.item.bgImage})`,
            }"
            @click="() => handleSelectAmount(item.num)"
          >
            <ResizableProvider>
              <UiText v-model:setting="setting.modal.amount.itemNum" :editableContent="false">
                {{ item.num }}元
              </UiText>
            </ResizableProvider>
          </Resizable>
        </Resizable>
      </template>

      <!-- 全部提现 -->
      <template v-if="setting.modal.all">
        <!-- 当前提现金额 -->
        <UiText
          v-model:setting="setting.modal.all.num"
          :interpolation="allNumInterpolation"
        />
        <!-- 全部提现按钮 -->
        <UiImage
          v-if="setting.modal.all.actionBtn?.imgLink"
          v-model:setting="setting.modal.all.actionBtn"
          @click="handleWithdrawAll"
        />
      </template>

      <!-- 提现记录-入口 -->
      <Resizable
        v-if="setting.modal.records.enabled"
        v-model:x="setting.modal.records.x"
        v-model:y="setting.modal.records.y"
        v-model:width="setting.modal.records.width"
        v-model:height="setting.modal.records.height"
        class="my-wallet-records"
        :style="{
          backgroundImage: `url(${setting.modal.records.bgImage})`,
        }"
        @click="handleShowRecords"
      />

      <!-- 刷新按钮 -->
      <UiImage
        v-if="setting.modal.refreshBtn.enabled"
        v-model:setting="setting.modal.refreshBtn"
        @click="handleRefresh"
      />
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'my-wallet',
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, computed, watch } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast, showLoadingToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import usePopupStore from '@bish/store/src/modules/popup';
import useAdminStore from '@bish/store/src/modules/admin';
import { useLog } from '@bish/hooks/src/useLog';
import { withdrawRedPacket } from '@bish/api/src/redEnvelope';
import type { WithdrawRedPacketParams } from '@bish/api/src/redEnvelope';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { useAccountBindWx } from '@bish/hooks/src/business/useAccountBindWx';
import { pxTransform } from '@bish/utils/src/viewport';
import { getUserInfo } from '@bish/utils/src/storage/modules/login';
import { userAgent, UserAgent } from '@bish/utils/src/utils';
import showTencentCaptcha from '../../../__utils/tencentCaptcha'
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../ui/image/index.vue';
import UiText from '../../../ui/text/index.vue';
import Popup from '../../../common/popup.vue';
import type { MyWalletSetting } from './my-wallet';
import { defaultConfig } from './index';

export interface TaskRewardProps {
  /**
   * 配置
   */
  setting: MyWalletSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
  /**
   * 打开钱包
   */
  openWallet?: () => boolean
}

const props = withDefaults(defineProps<TaskRewardProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: MyWalletSetting): void;
}>();

const amount = ref(0);
const refreshing = ref(false);

const setting = useVModel(props, 'setting', emits);

const [openWallet, toggleWallet] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showWallet' });

const activityStore = useActivityStore();
const popupStore = usePopupStore();
const adminStore = useAdminStore();

const { handleBindGuide } = useAccountBindWx();
const { uploadLog } = useLog();

// 用户可提现余额
const canWithdrawMoney = computed(() => {
  const res = activityStore.componentWithUserInfo?.component_with_user_info?.red_envelope?.can_withdraw_money || 0;
  return Number(res) || 0;
});

// 用户累计获得金额
const totalAmount = computed(() => {
  const res = activityStore.componentWithUserInfo?.component_with_user_info?.red_envelope?.total_amount || 0;
  return Number(res) || 0;
});

// 是否第一次提现
const isFirstWithdrawal = computed(() => {
  return activityStore.componentWithUserInfo?.component_with_user_info.red_envelope.draw_list.length === 0;
})
// 最少第一次提现金额
const firstPurseWithdrawAmount = computed(() => {
  const res = activityStore.activityInfo?.config.red_envelope.first_purse_withdraw_amount;
  return Number(res) || 0;
})

const minWithdrawMoney = computed(() => {
  const res = activityStore.activityInfo?.config.red_envelope.withdraw_min_amount;
  return Number(res) || 0;
});

const interpolation = computed(() => {
  return {
    num: canWithdrawMoney.value,
  };
});

const allNumInterpolation = computed(() => {
  return {
    amount: amount.value,
  };
});

const entryStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.x!),
    top: pxTransform(setting.value.y!),
    width: pxTransform(setting.value.width),
    height: pxTransform(setting.value.height),
    backgroundImage: `url(${setting.value.bgImage})`,
  };
});

const showMyWallet = computed(() => {
  // 兼容旧数据：setting.value.alwaysShow === undefined
  return adminStore.editable
    || setting.value.alwaysShow === undefined
    || setting.value.alwaysShow === 1
    || (
      setting.value.alwaysShow === 0
      && totalAmount.value > 0
    );
});

watch(
  () => popupStore.showWallet,
  (newVal) => {
    toggleWallet(newVal);
  },
);

const handleShowRecords = () => {
  popupStore.setShowEnvelopeRecords(true);
};

const handleSelectAmount = (num: number) => {
  amount.value = num;
};

const handleOpenWallet = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }
  const pass = activityStore._checkIn(false);
  // 1、校验登录
  if (!pass) {
    return;
  }
  // 先清空提现金额
  amount.value = 0;
  
  if (props.openWallet && !props.openWallet()) {
    return;
  }

  popupStore.setShowWallet(true);
};

const handleCloseWallet = () => {
  // 同步数据
  if (openWallet && !popupStore.showWallet) {
    toggleWallet(false);
  }
  popupStore.setShowWallet(false);
};

const drawRedPacket = async (randstr?: string, ticket?: string) => {
  const toast = showLoadingToast({
    duration: 0,
    message: '提现中...',
    forbidClick: true,
  });
  try {
    const params: WithdrawRedPacketParams = {
      act_id: activityStore.activityInfo?.init?.id,
      act_acc_id: activityStore.activityAccountInfo?.act_acc_id,
      amount: amount.value,
    }
    randstr && (params.rand_str = randstr)
    ticket && (params.ticket = ticket)
    const { code } = await withdrawRedPacket(params);
    toast.close();
    setTimeout(() => {
      activityStore.getComponentWithUserInfo();
      if (code === 0) {
        showToast('提现成功~');
      }
      popupStore.setShowWallet(false);
      toggleWallet(false);
    }, 800);
  } catch (error) {
    toast.close();
    console.warn('提现出错', error);
  }
}

const handleCashOut = async () => {
  // 1、校验提现开启状态
  const nowTime = Date.now();
  const withdrawTime = activityStore.activityInfo?.config?.red_envelope.withdraw_time;
  if (withdrawTime) {
    const timestamp = new Date(withdrawTime).getTime();
    if (timestamp > nowTime) {
      showToast(`活动将于${withdrawTime}开放提现`);
      return;
    };
  }
  // 2、校验提现金额
  if (amount.value === 0) {
    showToast('可提现金额不足');
    return;
  }
  // 首次提现金额配置存在，且提现金额不等于首次提现金额
  if (isFirstWithdrawal.value && (firstPurseWithdrawAmount.value && amount.value !== firstPurseWithdrawAmount.value) ) {
    showToast(`首次只能提现 ${firstPurseWithdrawAmount.value} 元`);
    return;
  }
  if (!isFirstWithdrawal.value && (amount.value < minWithdrawMoney.value)) {
    showToast(`当前额度不足，最低提现额度 ${minWithdrawMoney.value} 元`);
    return;
  }
  if (amount.value > canWithdrawMoney.value) {
    showToast(`提现金额不足，请选择其他提现额度`);
    return;
  }

  const userInfo = getUserInfo();
  if (!userInfo?.is_real) {
    popupStore.setShowRealNameAuth(true);
    return;
  }

  // 3、校验微信绑定
  if (!activityStore.activityAccountInfo?.wx_bind_status) {
    handleBindGuide();
    toggleWallet(false);
    popupStore.setShowWallet(false);
    return;
  }

  // 4、黑产校验人机验证
  const agentType = userAgent()
  if (
    agentType.isUserAgentType === UserAgent.WX_MINI ||
    agentType.isUserAgentType === UserAgent.WX_WEBVIEW
  ) {
    drawRedPacket();
  } else {
    showTencentCaptcha('redPacket', async (res) => {
      drawRedPacket(res.randstr, res.ticket)
    });
  }
};

const handleRefresh = async () => {
  if (refreshing.value) {
    return;
  }
  refreshing.value = true;
  await activityStore.getComponentWithUserInfo();
  showToast('已刷新');
  refreshing.value = false;
};

const handleWithdrawAll = () => {
    // 事件上报：点击提现按钮
  uploadLog({
    event_name: 'click',
    click_id: 148,
    click_type: 3,
  });
  amount.value = Math.min(canWithdrawMoney.value, props.setting.modal?.all?.max || 200);
};

// 暴露 open 方法给外部使用
const open = () => {
  if (adminStore.editable) {
    return;
  }
  const pass = activityStore._checkIn(false);
  // 1、校验登录
  if (!pass) {
    return;
  }
  // 先清空提现金额
  amount.value = 0;

  popupStore.setShowWallet(true);
};

// 暴露方法给外部使用
defineExpose({
  open,
});
</script>

<style>
.my-wallet {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.my-wallet-affix {
  position: fixed;
  left: auto !important;
  z-index: 96;
}

.my-wallet-balance {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.my-wallet-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}

.my-wallet-list-item {
  position: relative;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.my-wallet-list-item__active {
  filter: brightness(140%);
}

.my-wallet-records {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>