import { commonTaskConfig } from '../index';
import type { TaskMallRechargeSetting } from './mall-recharge';

export * from './mall-recharge';

export const defaultWeappEmbedConfig = (): TaskMallRechargeSetting['weappEmbedSetting'] => {
  return {
    modalSetting: {
      x: 0,
      y: 0,
      width: 375, // 这里的宽度没啥用，在用户端会被覆盖成 100vw
      height: 667, // 这里的高度没啥用，在用户端会被覆盖成 100vh
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250326/1742953725064-recharge-m-bg.png',
      okBtn: {
        x: 115,
        y: 540,
        width: 145,
        height: 46,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250326/1742953735176-recharge-m-btn.png',
      },
    },
  };
};

export const defaultConfig = (): TaskMallRechargeSetting => {
  return {
    ...commonTaskConfig(),
    width: 375,
    height: 56,
    taskId: 0,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250325/1742906214921-task-2.png',
    process: {
      x: 124,
      y: 28,
      width: 48,
      height: 20,
      fontSize: 10,
      color: '#706198',
      content: '{{process}}',
      alignItems: 'center',
    },
    doBtn: {
      x: 248,
      y: 10,
      width: 90,
      height: 36,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250325/1742897769928-task-2-do.png',
      enabled: true,
    },
    achievedBtn: {
      x: 248,
      y: 10,
      width: 90,
      height: 36,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250123/1737612696102-achieved-btn.png',
      enabled: false,
    },
    claimBtn: {
      x: 248,
      y: 10,
      width: 90,
      height: 36,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250120/1737375094345-join-claim.png',
      enabled: false,
    },
    weappWebview: 0,
  }
}; 