import type { CommonSetting } from '@bish/types/src'
import type { PopupSetting } from '../../common/popup.vue';
import type { ImageSetting } from '../../ui/image/index.vue';
import type { TextSetting } from '../../ui/text/index.vue';
import type { ResizableProps } from '../../ui/resizable/index.vue';

export interface ServerRoleSetting extends CommonSetting {
  /**
   * 弹窗配置
   */
  modalSetting: ModalSetting & {
    /**
     * 取消按钮
     */
    cancelBtn?: ImageSetting
    /**
     * 表单配置
     */
    form: {
      /**
       * 位置
       */
      position: ResizableProps
      /**
       * 输入框配置
       */
      input: {
        /**
         * 高度
         */
        height?: number
        /**
         * 背景
         */
        background?: string
        /**
         * 颜色
         */
        color?: string
      },
      /**
       * label 配置
       */
      label: {
        /**
         * 是否显示
         */
        show: boolean
        /**
         * 字体颜色
         */
        color: string
      },
    }
    /**
     * 提示文字
     */
    tipText: TextSetting
  }
  /**
   * 主题色
   */
  themeColor?: string
  /**
   * 对齐方式
   */
  align?: 'left' | 'right'
  /**
   * 是否限制换绑
   */
  limitSwitch: boolean
  /**
   * 指定区服
   */
  limitServer: boolean
  /**
   * 是否限制是受邀者，勾选是，限制换绑时需要同时满足账号是受邀者
   */
  invitee: boolean
}

export interface ModalSetting extends PopupSetting {}

export type ServerRoleValue = {
  /**
   * 区服
   */
  server: string[]
  /**
   * 角色
   */
  role: number[]
}

export type ServerRoleOptions = {
  /**
   * 选项名称
   */
  text: string
  /**
   * 选项值
   */
  value: string | number
}[]