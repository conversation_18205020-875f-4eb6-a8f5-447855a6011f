<!--
 * @Description: 私域窄口径-积分礼包领取
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :position="setting.position"
    class="points-gift-pack"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 道具展示 -->
      <Items v-model:setting="setting.items" />
  
      <!-- 区服-角色选择器 -->
      <template v-if="!props.setting.bindRole">
        <ServerRolePicker
          v-model:setting="setting.serverRolePicker"
          v-model:open="showServerRolePicker"
          v-model="serverRole"
          placeholder="请选择领取角色"
          @click="handleShowServerRolePicker"
          @confirm="handleServerRoleConfirm"
        >
          <template #default="{ selectedServer, selectedRole }">
            {{ `领取角色：${selectedServer?.text || ''}-${selectedRole?.text || ''}` }}
          </template>
        </ServerRolePicker>
      </template>
  
      <!-- 立即领取按钮 -->
      <template v-if="integralUserState?.receive_status !== 2">
        <UiImage
          v-if="setting.receiveBtnImage.imgLink"
          v-model:setting="setting.receiveBtnImage"
          :confined="false"
          @click="handleReceive"
        />
      </template>
      <template v-else>
        <!-- 已领取按钮 -->
        <UiImage
          v-if="setting.receivedBtnImage.imgLink"
          :setting="receivedBtnSetting"
        />
      </template>
  
      <!-- 下载按钮 -->
      <DownloadGame
        v-if="showDownloadBtn"
        v-model:setting="setting.downloadBtn"
        :confined="false"
      />
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'narrow-caliber-points-gift-pack',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, ref, computed } from 'vue';
import { useToggle, useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import usePopupStore from '@bish/store/src/modules/popup';
import useBindRole from '@bish/hooks/src/business/useBindRole';
import { useLog } from '@bish/hooks/src/useLog';
import { postAwardIntegral } from '@bish/api/src/activity';
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../ui/image/index.vue';
import ServerRolePicker from '../../../business/server-role-picker.vue';
import type { ServerRolePickerValue } from '../../../business/server-role-picker.vue';
import DownloadGame from '../../../business/download-game.vue';
import Items from '../../../business/items.vue';
import { defaultConfig } from './index';
import type { PointsGiftPackSetting } from './points-gift-pack';

export interface PointsGiftPackProps {
  /**
   * 配置
   */
  setting: PointsGiftPackSetting
}

const props = withDefaults(defineProps<PointsGiftPackProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: PointsGiftPackSetting): void
}>();

const serverRole = ref<ServerRolePickerValue>([]);
const receiving = ref(false);

const activityStore = useActivityStore();
const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);

const [showServerRolePicker, toggleShowServerRolePicker] = useToggle();

const { bindRole } = useBindRole();
const { uploadLog } = useLog();

const integralConfig = computed(() => activityStore.activityInfo?.config?.integral || []);

const currentIntegralConfig = computed(() => {
  const { setting } = props;
  return integralConfig.value.find((item) => item.id === +setting.gifPackId);
});

const integralUserState = computed(() => {
  const { setting } = props;
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.integral?.award_list?.find((item) => item.id === +setting.gifPackId);
});

const showDownloadBtn = computed(() => {
  // 兼容旧数据
  return setting.value.downloadBtn?.enabled
    || (
      setting.value.downloadBtn.enabled === undefined
      && setting.value.downloadBtn
      && setting.value.downloadBtn.imgLink
    );
});

const receivedBtnSetting = computed(() => {
  return {
    imgLink: setting.value.receivedBtnImage.imgLink,
    x: setting.value.receiveBtnImage.x,
    y: setting.value.receiveBtnImage.y,
    width: setting.value.receiveBtnImage.width,
    height: setting.value.receiveBtnImage.height,
  };
});

const handleShowServerRolePicker = () => {
  const pass = activityStore._checkIn(false);
  if (!pass) {
    return;
  }
  toggleShowServerRolePicker(true);
};

const handleServerRoleConfirm = () => {
  const [server_id, role_id] = serverRole.value;

  bindRole({
    server: [`${server_id}`],
    role: [+role_id],
  });
};

const handleReceive = async () => {
  // 数据上报
  uploadLog({
    event_name: 'click',
    click_id: 9,
    click_type: 3,
  });
  if (!props.setting.gifPackId) {
    showToast('缺少礼包id配置，请稍后重试~');
    return;
  }
  // 登录态拦截
  const pass = activityStore._checkIn(!!props.setting.bindRole);
  if (!pass) {
    return;
  }
  const { role_level, role_id } = activityStore.bindRoleInfo;
  // 未选择领取角色
  if (!role_id) {
    toggleShowServerRolePicker(true);
    return;
  }
  const { draw_role_level_limit = 0 } = currentIntegralConfig.value || {};

  if (+role_level < draw_role_level_limit) {
    showToast('角色等级未满足条件');
    return;
  }
  if (receiving.value) {
    return;
  }
  try {
    receiving.value = true;
    const res = await postAwardIntegral({
      act_id: activityStore.activityInfo.init?.id,
      act_acc_id: activityStore.activityAccountInfo.act_acc_id,
      integral_cfg_id: +props.setting.gifPackId,
    });
    if (res.code === 0) {
      // 打开下载游戏弹窗
      popupStore.setDownloadGameModal(true);

      if (!showDownloadBtn.value) {
        showToast('领取成功');
      }
    }
  } catch (error) {
    console.warn('领取积分奖励失败', error);
  } finally {
    receiving.value = false;
    // 重新获取用户与组件的数据
    activityStore.getComponentWithUserInfo();
  }
};
</script>

<style>
.points-gift-pack {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.points-gift-pack :deep(.server-role-switch) {
  text-decoration: none;
}
</style>
