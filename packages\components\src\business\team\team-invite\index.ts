import type { TeamInviteSetting } from './team-invite';
import { defaultConfig as inviteModalProDefaultConfig } from '../../invite-modal-pro';
import { defaultConfig as invitationModalDefaultConfig } from '../../invitation-modal.vue';
import { defaultConfig as defaultShareGuideConfig } from '../../share-guide';

export * from './team-invite';

const inviteModalProConfig = inviteModalProDefaultConfig();
const invitationModalConfig = invitationModalDefaultConfig();

export const defaultConfig = (): TeamInviteSetting => {
  const shareGuideConfig = defaultShareGuideConfig();
  return {
    x: 0,
    y: 0,
    width: 137,
    height: 72,
    teamId: 0,
    inviteBtn: {
      x: 20,
      y: 20,
      width: 97,
      height: 32,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250116/1737012934467-invite-btn.png',
      enabled: true,
    },
    inviteCard: inviteModalProConfig,
    invitationCard: invitationModalConfig,
    fullBtn: {
      x: 20,
      y: 20,
      width: 97,
      height: 32,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250419/1745057197363-team-fully.png',
      enabled: false,
    },
    shareGuide: {
      ...shareGuideConfig,
      copyLinkBtn: {
        ...shareGuideConfig.copyLinkBtn,
        enabled: false,
      },
      title: {
        x: 34,
        y: 45,
        width: 301,
        height: 140,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250419/1745058710220-share-guide-title.png',
      },
      closeBtn: {
        x: 140,
        y: 149,
        width: 88,
        height: 74,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250419/1745058712819-share-guide-close.png',
        enabled: true,
      },
    },
    joinFailedModal: {
      x: 0,
      y: 0,
      width: 310,
      height: 253,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250419/1745057883231-team-create-bg.png',
      enabled: false,
      closeBtn: {
        x: 272,
        y: 33,
        width: 23,
        height: 23,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250419/1745057981067-close.png',
      },
      okBtn: {
        x: 85,
        y: 184,
        width: 140,
        height: 37,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250419/1745061855849-team-create-btn.png',
      },
      content: {
        x: 0,
        y: 100,
        width: 310,
        height: 70,
        fontSize: 13,
        color: '#428368',
        align: 'center',
        alignItems: 'center',
        content: '<div style="line-height: 1.5;">当前身份不符合加入队伍要求，创建队伍寻找<br>新用户或回归用户一起组队完成任务领奖励吧~</div>',
        enabled: true,
      },
    },
  }
};
