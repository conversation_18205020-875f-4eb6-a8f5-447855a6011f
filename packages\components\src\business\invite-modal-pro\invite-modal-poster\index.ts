import type { InviteModalPosterSetting } from './invite-modal-poster';

export * from './invite-modal-poster';

export const defaultConfig = (): InviteModalPosterSetting => {
  return {
    x: 0,
    y: 0,
    width: 360,
    height: 583,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250116/1737012953062-poster-modal-bg.png',
    closeBtn: {
      x: 310,
      y: 8,
      width: 32,
      height: 30,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250114/1736863567849-close.png',
    },
    posterBg: {
      x: 31,
      y: 55,
      width: 299,
      height: 503,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250116/1737012975140-poster-bg.png',
    },
    qrCode: {
      x: 189,
      y: 418,
      width: 66,
      height: 66,
    },
    saveBtn: {
      x: 94,
      y: 612,
      width: 172,
      height: 32,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250116/1737012983393-poster-save.png',
    },
    tip: {
      x: 66,
      y: 584,
      width: 228,
      height: 24,
      fontSize: 12,
      content: '*长按转发给朋友，也可保存并分享',
      color: '#FFFFFF',
      alignItems: 'center',
      align: 'center',
      enabled: true,
    },
  };
};
