import axios from 'axios'
import type { AxiosInstance, AxiosError, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { allowMultipleToast, showToast } from 'vant'
import { useEnv } from '@bish/hooks/src/useEnv'
// @ts-expect-error
import sign from '@bish/utils/src/sign'
import useUserStore from '@bish/store/src/modules/user'
import { getRealName } from '@bish/utils/src/storage/modules/real-name'
import { getSimilar } from '@bish/utils/src/storage/modules/similar'
// 国际化
import i18n from '@bish/lang/src'
import { filterObject } from './__utils'

// const router = useRouter()

const {
  VITE_BASE_URL,
  VITE_BASE_SIGN_KEY,
  VITE_ACTIVITY_URL,
  VITE_ACTIVITY_SIGN_KEY,
  VITE_SDK_URL,
  VITE_SDK_SIGN_KEY,
  VITE_COMMON_URL,
  VITE_COMMON_SIGN_KEY,
  VITE_PAY_URL,
  VITE_PAY_SIGN_KEY,
  VITE_MALL_URL,
  VITE_MALL_SECRET,
  VITE_WECHAT_URL,
  VITE_WECHAT_SECRET,
} = useEnv()

/* 服务器返回数据的的类型，根据接口文档确定 */
export interface Result<T> {
  code: number
  message: string
  data: T
}

export type CustomAxiosConfig = InternalAxiosRequestConfig & {
  /**
   * 是否显示错误信息
   */
  errToast?: boolean
  /**
   * 是否需要 token，默认 true
   */
  needToken?: boolean
}

const service: AxiosInstance = axios.create({
  baseURL: '',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
  },
})

const signKeyMap: Record<string, string> = {
  [VITE_BASE_URL]: VITE_BASE_SIGN_KEY,
  [VITE_ACTIVITY_URL]: VITE_ACTIVITY_SIGN_KEY,
  [VITE_SDK_URL]: VITE_SDK_SIGN_KEY,
  [VITE_COMMON_URL]: VITE_COMMON_SIGN_KEY,
  [VITE_PAY_URL]: VITE_PAY_SIGN_KEY,
  [VITE_MALL_URL]: VITE_MALL_SECRET,
  [VITE_WECHAT_URL]: VITE_WECHAT_SECRET,
}

/**
 * 获取签名
 */
export function getSignKey(url: string) {
  const regex = /(https?:\/\/[^/]+)/
  const match = url.match(regex)
  if (match) {
    const domain = match[1] // => https://test.shiyue.com
    return signKeyMap[domain]
  }
  return ''
}

/* 请求拦截器 */
service.interceptors.request.use(
  (config: CustomAxiosConfig) => {
    const { data, needToken = true } = config
    // 获取用户实名状态
    const realName = getRealName() ?? 1
    // 获取是否参与过相似活动
    const similar = getSimilar() ?? 0

    if (similar && data && 'act_acc_id' in data && !data.act_acc_id) {
      return Promise.reject(new Error('SIMILAR_ACTIVITY'))
    }

    if (!realName && data && 'act_acc_id' in data && !data.act_acc_id) {
      return Promise.reject(new Error('NEED_REAL_NAME'))
    }

    const signKey = getSignKey(config.url as string)
    const ts = Math.round(+new Date() / 1000).toString()
    const userStore = useUserStore()

    config.headers['Accept-Language'] = i18n.global.locale

    const mergedData: Record<string, any> = {
      ...filterObject(data),
      ts,
    };
    
    if (needToken && userStore.userData.token) {
      mergedData.token = userStore.userData.token
    }
    mergedData.sign = sign(mergedData, signKey)
    config.data = mergedData
    return config
  },
  (error: AxiosError | undefined) => {
    allowMultipleToast()
    showToast({
      message: error as unknown as undefined,
      duration: 2 * 1000,
    })
    return Promise.reject(error)
  }
)

/* 响应拦截器 */
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { code, message } = response.data
    const {
      errToast = true, // 是否显示错误提示，默认 true
    } = response.config as CustomAxiosConfig

    // 根据自定义错误码判断请求是否成功
    if (code === 0) {
      // 将组件用的数据返回
      return response.data
    } else if (code === 1003 || code === 1006 || code === 1008) {
      // Token 过期、Token 非法、Token 信息获取异常
      const userStore = useUserStore()
      userStore.resetUserData()
      // router.go(0)
      
      allowMultipleToast()
      showToast({
        message: i18n.global.t('login-expired', '登录过期~'),
        duration: 3 * 1000,
      })
      return response.data
    } else {
      // 处理业务错误。
      if (errToast) {
        allowMultipleToast()
        showToast({
          message: message,
          duration: 3 * 1000,
        })
      }
      return Promise.resolve(response.data)
    }
  },
  (error: AxiosError) => {
    // 没有实名认证
    if (String(error) === 'Error: NEED_REAL_NAME' || String(error) === 'Error: SIMILAR_ACTIVITY') {
      return Promise.reject(error)
    }

    // 处理 HTTP 网络错误
    let Message = ''
    // HTTP 状态码
    const status = error.response?.status
    switch (status) {
      case 401:
        Message = 'token 失效，请重新登录'
        // 这里可以触发退出的 action
        break
      case 403:
        Message = '拒绝访问'
        break
      case 404:
        Message = '请求地址错误'
        break
      case 500:
        Message = '服务器故障'
        break
      default:
        Message = '网络连接故障'
    }
    // 取消请求不需要提示
    if (error.code !== 'ERR_CANCELED') {
      allowMultipleToast()
      showToast({
        message: Message,
        duration: 3 * 1000,
      })
    }
    return Promise.reject(error)
  }
)

/* 导出封装的请求方法 */
export const http = {
  get<T = any>(url: string, config?: Partial<CustomAxiosConfig>): Promise<Result<T>> {
    return service.get(url, config)
  },

  post<T = any>(url: string, data?: object, config?: Partial<CustomAxiosConfig>): Promise<Result<T>> {
    return service.post(url, data, config)
  },

  put<T = any>(url: string, data?: object, config?: Partial<CustomAxiosConfig>): Promise<Result<T>> {
    return service.put(url, data, config)
  },

  delete<T = any>(url: string, config?: Partial<CustomAxiosConfig>): Promise<Result<T>> {
    return service.delete(url, config)
  },
}

/* 导出 axios 实例 */
export default service
