import type { QuestionSetting } from './question';

export * from './question';

export const defaultConfig = (): QuestionSetting => {
  return {
    x: 0,
    y: 0,
    width: 20,
    height: 20,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250313/1741866879559-instruction.png',
    modal: {
      x: 0,
      y: 0,
      width: 372,
      height: 524,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250313/1741866876485-instruction-m-bg.png',
      closeBtn: {
        x: 336,
        y: 0,
        width: 27,
        height: 25,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250312/1741751471690-x.png',
      },
      okBtn: {
        x: 0,
        y: 0,
        width: 0,
        height: 0,
        imgLink: '',
      },
      content: {
        x: 44,
        y: 112,
        width: 294,
        height: 361,
      },
      instruction: '',
    },
  };
}; 