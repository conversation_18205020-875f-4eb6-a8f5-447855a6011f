import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { showToast } from '@bish/ui/src/toast'
import {
  postActivityPreBindAccRole,
  postSubmitReservation,
} from '@bish/api/src/preCreateRole'
import type { ActivityPreBindAccRoleParams } from '@bish/api/src/preCreateRole'
import {
  isTimeInRange,
  getCurrentEnvironment,
  replaceQueryProperties,
  additionalLinkParameters,
  userAgent,
} from '@bish/utils/src/utils'
import useActivityPageStore from '@bish/store/src/modules/activityPage'
import useActivityStore from '@bish/store/src/modules/activity'
import useUserStore from '@bish/store/src/modules/user'
import useReservationStore from '@bish/store/src/modules/reservation'
import usePopupStore from '@bish/store/src/modules/popup'
import useAdminStore from '@bish/store/src/modules/admin'
import { useLog } from '@bish/hooks/src/useLog'
import { getUserInfo } from '@bish/utils/src/storage/modules/login'
import { removeCosUrl } from '@bish/utils/src/cos'

export default function usePreCreateRole() {
  const activityPageStore = useActivityPageStore()
  const activityStore = useActivityStore()
  const userStore = useUserStore()
  const reservationStore = useReservationStore()
  const popupStore = usePopupStore()
  const adminStore = useAdminStore()

  const route = useRoute()
  const query = replaceQueryProperties(route)

  const { uploadLog } = useLog()

  const { isUserAgentType } = userAgent()

  const activityInfo = computed(() => activityStore.activityInfo)

  const activityAccountInfo = computed(() => activityStore.activityAccountInfo)

  /**
   * 跳转到小程序内嵌消息订阅页面
   */
  const navigateWeappSubscribe = () => {
    const { componentSettings, themeColor } = activityPageStore.mergedPageConfig
    const componentSetting = componentSettings.find(item => item.componentName === 'ServerReservationBook')?.setting
    
    let cfg = ''
    if (componentSetting?.weappEmbedSubscribeSetting) {
      const config = removeCosUrl({
        ...componentSetting?.weappEmbedSubscribeSetting,
        tipModal: componentSetting?.wxSubscribeModal,
        theme: themeColor, // 附加主题色配置
      })
      cfg = JSON.stringify(config)
    }
    
    // TODO: 把小程序登录页面提到环境变量
    const weappBishSubscribeUrl = '/subpkg/bish/subscribe'
    const weappBishWebviewUrl = '/subpkg/bish/webview'
    const url = additionalLinkParameters(
      {
        cfg: encodeURIComponent(cfg),
        act_id: activityInfo.value.init.id,
        act_acc_id: activityAccountInfo.value.act_acc_id,
        redirect: encodeURIComponent(`${weappBishWebviewUrl}?url=${encodeURIComponent(window.location.href)}`),
      },
      weappBishSubscribeUrl
    )

    wx?.miniProgram.redirectTo({
      url,
    });
  }

  // 提交预约
  const submitReservation = async () => {
    try {
      const currentEnvironment = getCurrentEnvironment()
      const userInfo = getUserInfo()
      const params = {
        act_id: activityInfo.value.init.id,
        act_acc_id: activityAccountInfo.value.act_acc_id,
        system_type: currentEnvironment.isIOS ? 2 : 1,
        src_channel: query.c ? +query.c : 0, // 来源不详，参考外包代码先加上
        src_platform: 2,
        location: 1,
        invite_code: (query.invite_code as string) || '',
        phone_number: userInfo?.phone_number ? +userInfo?.phone_number : 0,
      }
      const res = await postSubmitReservation(params)
      if (res.code === 0) {
        // 重新获取组件信息
        activityStore.getComponentWithUserInfo({ actAccId: activityAccountInfo.value.act_acc_id })
        
        // 微信小程序内嵌环境：跳转指定的登录页面
        if (isUserAgentType === 'WX_WEBVIEW') {
          setTimeout(() => {
            navigateWeappSubscribe()
          }, 2000)
          return
        }
  
        // 显示预约成功弹窗
        reservationStore.setShowBookSuccessfully(true)
      }
    } catch (error) {
      console.warn('提交预约失败', error)
    }
  }

  // 预约创角
  const handleReserve = async (extraParams: Partial<ActivityPreBindAccRoleParams>) => {
    if (reservationStore.preCreateRoleStatus === 'notStarted') {
      showToast('活动还没有开始哦')
      return
    }
    if (reservationStore.preCreateRoleStatus === 'end') {
      showToast('活动已结束，感谢您的参与~')
      return
    }
    if (!extraParams.role_name) {
      showToast('请输入角色名')
      return
    }
    if (extraParams.role_name.length > 6) {
      showToast('角色名不可超过6个字符')
      return
    }
    const userInfo = getUserInfo()
    if (!userInfo?.is_real) {
      popupStore.setShowRealNameAuth(true)
      return
    }
    try {
      const params = {
        act_id: activityInfo.value.init.id,
        role_name: extraParams?.role_name as string,
        gender: extraParams?.gender as number,
        career: extraParams?.career as number,
        invite_code: query.invite_code || '',
      }
      const res = await postActivityPreBindAccRole(params)
      if (res.code === 0) {
        // 事件上报--预创角后即标识预约成功
        uploadLog({
          event_name: 'click',
          click_id: 17,
          click_type: 3,
        })
        // 预创角成功返回指定活动
        if (query.redirect_url) {
          window.location.href = query.redirect_url
          return
        }
        // 重新获取活动相关数据，因为创建成功后台会自动绑定当前角色
        activityStore.initActivityInfo(false)
        // 重新获取预创角角色列表
        reservationStore.fetchPreRoleList()
        showToast('创建角色成功~')
        popupStore.setShowPreCreateRole(false)
        // TODO: 接收组队
        submitReservation()
        userStore.workBindPreRoleTask()
      } else {
        let msg = res.message
        switch (res.code) {
          case 2019:
            msg = '角色名不能包含特殊符号'
            break
          case 2020:
            msg = '角色名称已存在'
            break
          case 2214:
            msg = '需要实名认证'
            popupStore.setShowRealNameAuth(true)
            break
          default:
            break
        }
        showToast(msg)
      }
    } catch (error) {
      console.warn('预约创角失败', error)
    }
  }
  
  return {
    handleReserve,
    navigateWeappSubscribe,
  }
}
