export interface AppLauncherOptions {
  /**
   * Android 应用包名
   */
  // androidPackage: string
  /**
   * iOS App Store ID
   */
  // iosAppStoreId: string
  /**
   * android scheme
   */
  androidScheme: string
  /**
   * ios scheme
   */
  iosScheme: string
  /**
   * 超时时间，默认 2500ms
   */
  timeout?: number
}

export function useAppLauncher(options: AppLauncherOptions) {
  const {
    // androidPackage,
    // iosAppStoreId,
    androidScheme,
    iosScheme,
    timeout = 2500,
  } = options;

  const u = navigator.userAgent;
  const isWeixin = u.toLowerCase().indexOf('micromessenger') !== -1; // 微信内
  const isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //android终端
  const isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端

  // 获取唤起链接
  const getLaunchUrl = () => {
    // if (isAndroid) {
    //   return `intent://${scheme}#Intent;package=${androidPackage};scheme=https;end`;
    // }
    if (isAndroid) {
      return `${androidScheme}://`;
    }
    return `${iosScheme}://`;
  };
  
  const isHidden = () => {
    let hidden =
      window.document.hidden ||
      window.document.mozHidden ||
      window.document.msHidden ||
      window.document.webkitHidden;
    return typeof hidden === 'undefined' || hidden === false;
  };

  // 唤起 App
  const launchApp = (option: {
    downloadUrl: string
  }) => {
    const { downloadUrl } = option;
    const launchUrl = getLaunchUrl();

    // 微信环境提示在浏览器打开
    if (isWeixin) {
      window.location.href = downloadUrl;
      return;
    }

    // 记录当前时间
    const startTime = Date.now();

    // 尝试唤起 App
    if (isIOS) {
      // iOS 通过 location.href 唤起
      window.location.href = launchUrl;

      // 设置超时跳转到下载页

      setTimeout(() => {
        const endTime = Date.now();
        // 如果时间差小于设定的超时时间，说明唤起失败
        if (endTime - startTime < timeout + 200) {
          if (isHidden()) {
            window.location.href = downloadUrl;
          }
        }
      }, timeout);
    }
    if (isAndroid) {
      // Android 通过 iframe 唤起
      window.top.location.href = launchUrl;

      setTimeout(() => {
        const endTime = Date.now();
        // 如果时间差小于设定的超时时间，说明唤起失败
        if (endTime - startTime < timeout + 200) {
          if (isHidden()) {
            window.location.href = downloadUrl;
          }
        }
      }, timeout);
    }
  };

  return {
    launchApp,
  };
} 