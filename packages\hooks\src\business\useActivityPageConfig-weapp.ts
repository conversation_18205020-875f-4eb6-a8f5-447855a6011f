import { useRoute } from 'vue-router';
import { showLoadingToast } from '@bish/ui/src/toast';
import { postActivityPageConfig } from '@bish/api/src/activityPage'
import useActivityStore from '@bish/store/src/modules/activity';
import useActivityPageStore from '@bish/store/src/modules/activityPage'
import localStorage from '@bish/utils/src/storage/localStorage'
import useRouteQuery from '../useRouteQuery';

type EventPageQuery = {
  /**
   * 活动页id
   */
  event_page: string
  /**
   * 更多的查询参数
   */
  [key: string]: string
}

export function useActivityPageConfig() {
  const { query } = useRouteQuery<EventPageQuery>();
  
  const activityStore = useActivityStore()
  const activityPageStore = useActivityPageStore()

  /**
   * 初始全局的活动页配置信息、活动数据
   */
  const initActivityConfigInfo = async () => {
    const toast = showLoadingToast({
      duration: 0,
      forbidClick: true,
    })
    try {
      initChannelId()
      await fetchActivityPageConfig()
      await activityStore.initActivityInfo(false)
    } catch (error) {
      console.warn('初始化活动数据失败', error)
    } finally {
      toast.close()
    }
  }

  // 内嵌小程序：初始微信小程序分享数据
  const initPageShare = (extra?: Record<string, any>) => {
    // if (isUserAgentType !== UserAgent.WECHATWEB) {
    //   return
    // }
    // // 删除原先登录成功附加到 url 的相关参数
    // const pureUrl = deleteLinkParameters(blackQuery, window.location.href)
    // sendShareMessage({
    //   // 注意这里的 path 是要求小程序的 webview 页面也是这个路径
    //   path: `/subpkg/webView/webView?url=${encodeURIComponent(pureUrl)}`,
    //   ...extra,
    // })
  }

  const fetchActivityPageConfig = async () => {
    try {
      if (!query.value.event_page) {
        return
      }
      const params = {
        id: +(query.value.event_page as string),
      }
      const res = await postActivityPageConfig(params)
      if (res.code === 0) {
        activityPageStore.setActivityPageConfig({
          ...res.data,
          area_type: res.data.area_type ?? 1, // 默认国内
        })
      }
    } catch (error) {
      console.warn('获取活动页装修信息出错', error)
    }
  }

  /**
   * 初始渠道id参数，用户数据上报
   */
  const initChannelId = () => {
    if (query.value.c) {
      localStorage.setLocalStorage('SY_LOG_DATA_c', 1)
      localStorage.setLocalStorage('SY_LOG_DATA_scene_id', query.value.c)
    }
  }

  return {
    initActivityConfigInfo,
    fetchActivityPageConfig,
    initPageShare,
  }
}
