import { showToast } from '@bish/ui/src/toast';
import { useEnv } from '@bish/hooks/src/useEnv';
// #ifdef H5
import copy from 'copy-to-clipboard';
// #endif

const { VITE_APP_TYPE } = useEnv();

/**
 * 解析插值
 * @param str 
 * @param map 
 * @returns 
 */
export function interpolateString(str: string, map: Record<string, (string | number)>) {
  if (!str) {
    return '';
  }
  if (!map || typeof map !== 'object') {
    return str;
  }
  return str.replace(/\{\{(\w+)\}\}/g, (match, p1) => {
    return `${map[p1] ?? match}`;
  });
}

/**
 * @description 复制文本
 * @param text 文本
 * @param message 配置
 */
export const copyText = (
  text: string,
  message = {
    success: '复制成功',
    error: '复制失败',
  }
) => {
  if (VITE_APP_TYPE === 'wx') {
    uni.setClipboardData({
      data: text,
      showToast: false,
      success() {
        showToast(message.success)
      },
      fail() {
        showToast(message.error)
      },
    })
  } else {
    const result = copy(text)
    if (result) {
      showToast(message.success)
    } else {
      showToast(message.error)
    }
  }
}