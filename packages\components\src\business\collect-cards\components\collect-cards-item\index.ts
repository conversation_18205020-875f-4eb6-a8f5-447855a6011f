import type { CollectCardsItemSetting } from './collect-cards-item';

export * from './collect-cards-item';

export const defaultConfig = (): CollectCardsItemSetting => {
  return {
    x: 0,
    y: 0,
    width: 195,
    height: 276,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744270407680-item-box.png',
    inactiveBgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744272018324-item-box-inactive.png',
    card: {
      x: 12,
      y: 16,
      width: 173,
      height: 253,
    },
    gift: {
      x: -12,
      y: -16,
      width: 65,
      height: 66,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744268807286-gift.png',
      enabled: true,
    },
    scratched: {
      x: -12,
      y: -16,
      width: 65,
      height: 66,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744268812416-scratched.png',
      enabled: true,
    },
    scratch: {
      x: 44,
      y: 224,
      width: 108,
      height: 31,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744268809457-scratch.png',
      enabled: true,
    },
  };
}; 
