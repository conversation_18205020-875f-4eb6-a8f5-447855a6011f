import type { CollectCardsGroupSetting } from './collect-cards-group';
import { defaultConfig as collectCardsItemDefaultConfig } from '../collect-cards-item';

export * from './collect-cards-group';

export const defaultConfig = (): CollectCardsGroupSetting => {
  return {
    x: 24,
    y: 0,
    width: 327,
    height: 324,
    item: collectCardsItemDefaultConfig(),
    indicator: {
      x: 0,
      y: 298,
      width: 327,
      height: 20,
      fontSize: 11,
      color: '#FAF3DE',
      align: 'center',
      content: '({{current}}/{{total}})',
      enabled: true,
    },
    prev: {
      x: 11,
      y: 120,
      width: 43,
      height: 59,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744287109518-prev.png',
      enabled: true,
    },
    next: {
      x: 275,
      y: 120,
      width: 43,
      height: 59,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744287112760-next.png',
      enabled: true,
    },
  };
}; 
