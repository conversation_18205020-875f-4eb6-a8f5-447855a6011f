import type { CommonBackgroundSetting } from '@bish/types/src';
import type { PopupSetting } from '../../../common/popup.vue'
import type { ImageSetting } from '../../../ui/image/index.vue'
import type { TextSetting } from '../../../ui/text/index.vue'

export interface TeamCreateSetting extends CommonBackgroundSetting {
  /**
   * 队伍id
   */
  teamId: number
  /**
   * 是否固定在页面，1是 0否，默认 1，设置后将使用 fixed 布局
   */
  affix: number
  /**
   * 弹窗基础配置
   */
  modal: PopupSetting & {
    /**
     * 取消按钮
     */
    cancelBtn: ImageSetting
    /**
     * 提示文案
     */
    content: TextSetting
  }
}