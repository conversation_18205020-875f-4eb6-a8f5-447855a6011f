<!--
* @Description: 邀请记录
-->
<template>
  <Records
    v-model:setting="setting"
    :open="open"
    :columns="mergedColumns"
    :dataSource="dataSource"
    @open="() => handleShow()"
    @close="() => toggle(false)"
  >
    <!-- 入口文案 -->
    <UiText
      v-if="setting.text?.content"
      v-model:setting="setting.text"
    />

    <template #bodyCell="{ column, record }">
      <!-- 角色名 -->
      <template v-if="column.dataIndex === 'role_name'">
        {{ record.role_info?.role_name || record.invited_account_extra?.phone_number || record.invited_account_id }}
      </template>
      <!-- 手机号 -->
      <template v-if="column.dataIndex === 'phone_number'">
        {{ record.invited_account_extra?.phone_number }}
      </template>
      <!-- 账号id -->
      <template v-if="column.dataIndex === 'invited_account_id'">
        {{ record.invited_account_id }}
      </template>
      <!-- 邀请类型 -->
      <template v-if="column.dataIndex === 'invited_account_type'">
        {{ invitedType(record.invited_account_type) }}
      </template>
      <!-- 邀请时间 -->
      <template v-if="column.dataIndex === 'created_at'">
        {{ record.updated_at || record.created_at }}
      </template>
    </template>
  </Records>
</template>

<script lang="ts">
export default {
  name: 'invitation-record',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { computed } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import useUserStore from '@bish/store/src/modules/user';
import useAdminStore from '@bish/store/src/modules/admin';
import usePopupStore from '@bish/store/src/modules/popup';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import type { StatefulComponent } from '@bish/types/src/admin';
import Records from '../records/records.vue';
import UiText from '../../ui/text/index.vue';
import { defaultConfig } from './index';
import type { InvitationRecordSetting } from './invitation-record';

export interface InvitationRecordProps {
  /**
   * 配置
   */
  setting: InvitationRecordSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<InvitationRecordProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: InvitationRecordSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const userStore = useUserStore();
const activityStore = useActivityStore();
const adminStore = useAdminStore();
const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);
const [open, toggle] = useControllableStatus(props, emits, { fieldName: 'showRewardRecords' });

const dataSource = computed(() => {
  if (adminStore.editable) {
    return [
      {
        created_at: '2024.09.09 16:01:33',
        role_info: {
          role_name: '角色1',
        },
        invited_account_type: 1,
        invited_account_id: '********',
        invited_account_extra: {
          phone_number: '199****1117',
        },
      },
      {
        created_at: '2024.09.09 16:01:33',
        role_info: {
          role_name: '角色1',
        },
        invited_account_type: 2,
        invited_account_id: '********',
        invited_account_extra: {
          phone_number: '199****1117',
        },
      },
      {
        created_at: '2024.09.09 16:01:33',
        role_info: {
          role_name: '角色1',
        },
        invited_account_type: 3,
        invited_account_id: '********',
        invited_account_extra: {
          phone_number: '199****1117',
        },
      },
    ];
  }
  return activityStore.componentWithUserInfo?.invite_list || [];
});

const mergedColumns = computed(() => {
  return props.setting.columns.map(column => {
    return {
      ...column,
      // ellipsis: column.dataIndex === 'created_at',
      // width: column.dataIndex === 'created_at' ? undefined : '30%',
    };
  })
});

const invitedType = (type: number) => {
  let text = '';
  switch (type) {
    case 1:
      text = '活跃玩家';
      break;
    case 2:;
      text = '回归用户';
      break;
    case 3:
      text = '新用户';
      break;
  }
  return text;
};

const handleShow = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return
  }

  if (!userStore.isLogined) {
    popupStore.setShowLoginModal(true)
    userStore.scheduler.add(() => {
      toggle(true)
      return Promise.resolve()
    })
  } else {
    toggle(true)
  }
}
</script>

<style lang="less">
.invitation-record {}
</style>
