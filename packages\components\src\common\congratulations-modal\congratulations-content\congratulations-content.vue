<!--
* @Description: 恭喜获得-内容
-->
<template>
  <ResizableProvider class="congratulations-content">
    <!-- 奖品图片 -->
    <Resizable
      v-if="!isEmpty && (setting.prizeImg.show ?? true)"
      v-model:x="setting.prizeImg.x"
      v-model:y="setting.prizeImg.y"
      v-model:width="setting.prizeImg.width"
      v-model:height="setting.prizeImg.height"
      :class="{ 'congratulations-img-wrapper': !prize?.icon }"
    >
      <img v-if="prize?.icon" :src="prize?.icon" class="congratulations-img"  />
    </Resizable>

    <!-- 谢谢参与 -->
    <div v-if="isEmpty" class="congratulations-empty">
      <!-- 这里用的临时路径，todo：放在组件目录下 -->
      <img
        :style="emptyImgStyle"
        class="congratulations-empty-img"
        src="https://pingtai-img.shiyue.com/cdn-miniprogram-member-static/activity/bish/congratulations-empty.png"
      />
      <div :style="emptyTipStyle" class="congratulations-empty-tip">
        差一点就中奖了
      </div>
    </div>

    <!-- 操作按钮 -->
    <div>
      <!-- 默认按钮：游戏道具奖励 -->
      <template v-if="adminStore.editable || prize?.type === PRIZE_TYPE_GAME_ITEMS">
        <UiImage
          v-if="setting.okBtn?.imgLink"
          v-model:setting="setting.okBtn"
          @click="handleClose"
        />
      </template>
      <!-- 实物奖励 -->
      <template v-if="prize?.type === PRIZE_TYPE_PHYSICAL">
        <UiImage
          v-if="setting.addressBtn?.enabled"
          v-model:setting="setting.addressBtn"
          @click="handleShowAddressForm"
        />
        <!-- 兼容展示默认按钮，有些活动存在道具是实物，但是不需要填写地址，通过联系客服发放 -->
        <template v-else>
          <UiImage
            v-if="setting.okBtn?.imgLink"
            v-model:setting="setting.okBtn"
            @click="handleClose"
          />
        </template>
      </template>
      <!-- 现金奖励 -->
      <template v-if="prize?.type === PRIZE_TYPE_RED_PACKET">
        <UiImage
          v-if="setting.withdrawBtn?.enabled"
          v-model:setting="setting.withdrawBtn"
          @click="handleWithdraw"
        />
        <!-- 兼容展示默认按钮，有些活动存在道具是红包，但是不需要展示立即提现按钮 -->
        <template v-else>
          <UiImage
            v-if="setting.okBtn?.imgLink"
            v-model:setting="setting.okBtn"
            @click="handleClose"
          />
        </template>
      </template>
    </div>

    <!-- 奖品名称 -->
    <UiText
      v-if="!isEmpty"
      v-model:setting="setting.prizeName"
      :scrollspy="false"
      :interpolation="prizeNameInterpolation"
    />
    <!-- 提示 -->
    <UiText
      v-if="showTip"
      v-model:setting="setting.tip"
      :scrollspy="false"
      :editableContent="false"
    >
      <div v-if="adminStore.editable || prize?.type === PRIZE_TYPE_GAME_ITEMS" v-html="setting.gameItemTip" />
      <div v-else-if="prize?.type === PRIZE_TYPE_RED_PACKET" v-html="setting.redPacketTip" />
      <div v-else v-html="setting.otherTip" />
    </UiText>
  </ResizableProvider>
</template>

<script lang="ts">
import type { LotteryPrizeDrawData } from '@bish/api/src/lottery';
import type { CongratulationsContentSetting } from './congratulations-content';

export interface CongratulationsContentProps {
  /**
   * 配置
   */
  setting: CongratulationsContentSetting
  /**
   * 奖品
   */
  prize: LotteryPrizeDrawData
}

export default {
  name: 'congratulations-modal',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import usePopupStore from '@bish/store/src/modules/popup';
import useAdminStore from '@bish/store/src/modules/admin';
import { pxTransform } from '@bish/utils/src/viewport';
import { PRIZE_TYPE_PHYSICAL, PRIZE_TYPE_GAME_ITEMS, PRIZE_TYPE_EMPTY, PRIZE_TYPE_RED_PACKET } from '../../../__constants/prize';
import UiText from '../../../ui/text/index.vue';
import UiImage from '../../../ui/image/index.vue';
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import { defaultConfig } from './index';

const props = withDefaults(defineProps<CongratulationsContentProps>(), {
  setting: defaultConfig,
  show: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: CongratulationsContentSetting): void;
  (event: 'ok'): void;
  (event: 'close'): void;
}>();

const popupStore = usePopupStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const prizeNameInterpolation = computed(() => {
  return {
    prizeName: props.prize?.name || '--',
  }
});

const isEmpty = computed(() => {
  return props.prize?.type === PRIZE_TYPE_EMPTY;
});

const emptyImgStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(props.setting.prizeImg.x!),
    top: pxTransform(props.setting.prizeImg.y!),
    width: pxTransform(props.setting.prizeImg.width),
    height: pxTransform(props.setting.prizeImg.width),
  };
});

const emptyTipStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(props.setting.prizeName.x!),
    top: pxTransform(props.setting.prizeName.y!),
    width: pxTransform(props.setting.prizeName.width),
    height: pxTransform(props.setting.prizeName.height),
    fontSize: pxTransform(props.setting.prizeName.fontSize!),
    fontWeight: props.setting.prizeName.fontWeight ? '700' : '400',
    color: '#C9A892',
  };
});

const showTip = computed(() => {
  return adminStore.editable || (props.prize?.type !== PRIZE_TYPE_EMPTY);
});

const handleClose = () => {
  emits('close');
};

const handleShowAddressForm = () => {
  handleClose();
  popupStore.setShowAddressForm(true);
};

const handleWithdraw = () => {
  handleClose();
  popupStore.setShowEnvelopeRecords(true);
};
</script>

<style>
.congratulations-img-wrapper {
  background-color: #f4f4f4;
}

.congratulations-img {
  width: 100%;
  height: 100%;
}

.congratulations-empty {}

.congratulations-empty-img {
  position: absolute;
}

.congratulations-empty-tip {
  position: absolute;
  text-align: center;
}

.congratulations-content {}
</style>