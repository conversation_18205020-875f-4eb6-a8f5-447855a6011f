<template>
  <div class="resizable-provider" ref="container">
    <slot></slot>
    <div class="baseline baseline-y" :style="{ top: pxTransform(baselineY) }" v-if="baselineY" />
    <div class="baseline baseline-x" :style="{ left: pxTransform(baselineX) }" v-if="baselineX" />
  </div>
</template>

<script setup lang="ts">
import { shallowReactive, ref, provide, computed, type ComponentInternalInstance } from "vue"
import { contextKey, type PositionLimitType } from './config';
import type { ComputedRef } from "vue";
import { pxTransform } from "@bish/utils/src/viewport";

export interface NearPositionType {
  top: number;
  middle: number;
  bottom: number;
  left: number;
  center: number;
  right: number;
}
export interface ResizableContainerInjectType {
  registerValue: typeof registerValue;
  cancelValue: typeof cancelValue;
  registerBaseline: typeof registerBaseline;
  cancelBaseline: typeof cancelBaseline;
  getContainerPositionLimit: typeof getContainerPositionLimit;
  childMap: typeof childMap;
}

const container = ref<HTMLDivElement>()
const childMap = shallowReactive<Map<ComponentInternalInstance , ComputedRef<NearPositionType>>>(new Map());
const baselineX = ref<number>()
const baselineY = ref<number>()

const getContainerPositionLimit = () => {
  const rect = container.value?.getBoundingClientRect();
  if (rect) {
    return {
      left: 0,
      right: rect.width,
      top: 0,
      bottom: rect.height,
    } as PositionLimitType
  }
}

const registerValue = (key: ComponentInternalInstance, value: ComputedRef<NearPositionType>) => {
  childMap.set(key, value);
};

const cancelValue = (key: ComponentInternalInstance) => {
  childMap.delete(key);
}

const registerBaseline = (type: 'x' | 'y', v: number | undefined) => {
  if (type === 'x') {
    baselineX.value = v;
  } else {
    baselineY.value = v;
  }
}
const cancelBaseline = (type: 'x' | 'y') => {
  if (type === 'x') {
    baselineX.value = void 0;
  } else {
    baselineY.value = void 0;
  }
}

provide<ResizableContainerInjectType>(contextKey, {
  registerValue,
  cancelValue,
  registerBaseline,
  cancelBaseline,
  getContainerPositionLimit,
  childMap,
})

</script>

<style>
.resizable-provider {
  position: relative;
  height: 100%;
}
.baseline {
  position: absolute;
  background-color: red;
}
.baseline-y {
  width: 100%;
  height: 1px;
}
.baseline-x {
  width: 1px;
  height: 100%;
  top: 0;
}
</style>
