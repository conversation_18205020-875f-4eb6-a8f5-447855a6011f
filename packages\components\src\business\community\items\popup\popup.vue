<!--
* @Description: 社区链接-弹出层
-->
<template>
  <div class="community-popup">
    <UiImage
      v-if="setting?.imgLink"
      v-model:setting="setting"
      @click="handleOpen"
    />

    <!-- 弹出层内容 -->
    <Popup
      z-index="99"
      :show="openPopup"
      :lock-scroll="false"
      v-model:setting="setting.popup"
      @close="handleClose"
    >
      <UiImage
        v-if="imgSetting.imgLink"
        :setting="imgSetting"
        :movable="false"
        :resizable="false"
        show-menu-by-longpress
      />
    </Popup>
  </div>
</template>

<script lang="ts">
import type { StatefulComponent } from '@bish/types/src/admin';
import type { CommunityPopupSetting } from './popup';

export interface CommunityProps {
  /**
   * 配置
   */
  setting: CommunityPopupSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export default {
  name: 'community-popup',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { useLog } from '@bish/hooks/src/useLog';
import UiImage from '../../../../ui/image/index.vue';
import Popup from '../../../../common/popup.vue';
import { defaultConfig } from './index';
import { COMMUNITY_COMMON_NOOP } from '../const';

const props = withDefaults(defineProps<CommunityProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: CommunityPopupSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const setting = useVModel(props, 'setting', emits);
const [openPopup, togglePopup] = useControllableStatus(props, emits, { fieldName: 'showPopup' });

const { uploadLog } = useLog();

const imgSetting = computed(() => {
  return {
    x: setting.value.popup?.x || 0,
    y: setting.value.popup?.y || 0,
    width: setting.value.popup?.width || 0,
    height: setting.value.popup?.height || 0,
    imgLink: setting.value.popup?.bgImage || 0,
  };
})

const handleOpen = () => {
  togglePopup(true);
  // 数据上报
  if (setting.value.type !== COMMUNITY_COMMON_NOOP) {
    uploadLog({
      event_name: 'click',
      click_id: setting.value.type,
      click_type: 3,
    });
  }
};

const handleClose = () => {
  togglePopup(false);
};
</script>

<style>
.community-popup {
}

.community-popup-affix {
  position: fixed;
  z-index: 98;
}
</style>