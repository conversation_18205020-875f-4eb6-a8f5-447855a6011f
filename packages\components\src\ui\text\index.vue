<template>
  <Resizable
    v-bind="attrs"
    v-model:x="vSetting.x"
    v-model:y="vSetting.y"
    v-model:width="vSetting.width"
    v-model:height="vSetting.height"
    :position="vSetting.position"
  >
    <div class="ui-text-container">
      <div class="ui-text" :class="{ 'ui-text-scrollspy': scrollspy }" :style="textStyle">
        <Link
          :href="vSetting.href"
          :target="vSetting.target"
        >
          <slot>
            <div v-html="contentText"></div>
          </slot>
        </Link>
      </div>
      
      <div v-if="adminStore.editable" class="ui-text-quick" @click.stop="handleQuickEdit">
        <div class="ui-text-quick-content">
          <IconEdit />
        </div>
      </div>
    </div>
  </Resizable>
</template>

<script lang="ts">
import type { LinkProps } from '../link/index.vue';
import type { ResizableProps } from '../resizable/index.vue';
import type { CSSProperties } from 'vue';

export interface TextOwnerSetting {
  /**
   * 文案
   */
  content?: string
  /**
   * 字号
   */
  fontSize?: number
  /**
   * 字重
   */
  fontWeight?: boolean
  /**
   * 斜体
   */
  italic?: boolean
  /**
   * text-decoration
   */
  textDecoration?: string
  /**
   * 颜色
   */
  color?: string
  /**
   * 水平对齐
   */
  align?: keyof typeof justifyContentMap
  /**
   * 垂直对齐
   */
  alignItems?: string
  /**
   * 背景
   */
  background?: string
}
export type TextSetting = ResizableProps & LinkProps & TextOwnerSetting

export interface TextProps {
  /**
   * 配置
   */
  setting: TextSetting
  /**
   * 插值
   */
  interpolation?: Record<string, (string | number)>
  /**
   * 文本样式
   */
  textStyle?: CSSProperties
  /**
   * 内容是否可滚动，默认 true
   */
  scrollspy?: boolean
  /**
   * 是否可编辑内容的，默认 true
   */
  editableContent?: boolean

  [key: string]: any
}

export const defaultConfig = (): TextSetting => {
  return {
    x: 0,
    y: 0,
    width: 100,
    height: 36,
    content: '文本',
    fontSize: 14,
    align: 'left',
    alignItems: 'flex-start',
    color: 'rgb(0, 0, 0)',
  };
};

const justifyContentMap = {
  left: 'flex-start' as const,
  center: 'center' as const,
  right: 'flex-end' as const,
}

export default {
  name: 'ui-text',
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  }
};
</script>
<script lang="ts" setup>
import { computed, readonly, useAttrs } from 'vue';
import Resizable from '../resizable/index.vue';
import Link from '../link/index.vue';
import IconEdit from './icons/icon-edit.vue'
import { QUICK_EVENT, quickEvent } from '../../__utils/quick-event';
import { useVModel } from '@vueuse/core';
import { pxTransform } from '@bish/utils/src/viewport';
import useAdminStore from '@bish/store/src/modules/admin';
import { interpolateString } from '../../__utils/text';

const props = withDefaults(defineProps<TextProps>(), {
  setting: defaultConfig,
  scrollspy: true,
  editableContent: true,
})

const emits = defineEmits<{
  (event: 'update:setting', value: TextSetting): void;
}>();

const attrs = useAttrs();

const vSetting = useVModel(props, 'setting', emits);

const adminStore = useAdminStore();

const textStyle = computed(() => {
  return {
    width: `${pxTransform(props.setting.width as number)}`,
    height: `${pxTransform(props.setting.height as number)}`,
    'font-size': `${props.setting.fontSize}px`,
    'font-weight': props.setting.fontWeight ? 'bold' : void 0,
    'font-style': props.setting.italic ? 'italic' : void 0,
    'text-decoration': props.setting.textDecoration,
    color: props.setting.color,
    'text-align': props.setting.align,
    'justify-content': justifyContentMap[props.setting.align!],
    'align-items': props.setting.alignItems,
    background: props.setting.background,
    ...props.textStyle,
  } as CSSProperties;
});

const contentText = computed(() => {
  return interpolateString(props.setting.content!, props.interpolation!);
});

const handleQuickEdit = () => {
  const event = quickEvent(
    QUICK_EVENT.TEXT,
    {
      callback(setting) {
        vSetting.value = {
          ...vSetting.value,
          ...setting
        }
      },
      value: readonly(vSetting.value),
      editableContent: props.editableContent,
    },
  );
  document.dispatchEvent(event);
};
</script>

<style>
.ui-text {
  height: 100%;
  width: 100%;
  display: flex;
  white-space: pre-wrap;
  word-break: break-all;
}
.ui-text-container:hover .ui-text-quick {
  display: block;
}

.ui-text-quick {
  display: none;
  padding: 6px;
  position: absolute;
  left: -6px;
  top: -34px;
}

.ui-text-quick-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  cursor: pointer;
}

.ui-text-quick-content > .svg-icon {
  width: 60%;
  height: auto;
}

.ui-text-scrollspy {
  overflow-y: auto;
}
</style>