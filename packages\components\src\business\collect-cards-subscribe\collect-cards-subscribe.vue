<template>
  <Resizable class="reward" v-model:x="setting.x" v-model:y="setting.y" v-model:width="setting.width"
    v-model:height="setting.height" :style="{
      backgroundImage: `url(${setting.bgImage})`,
      backgroundSize: '100% 100%',
    }">
    <Resizable v-if="setting?.award" v-model:x="setting.award.x" v-model:y="setting.award.y"
      v-model:width="setting.award.width" v-model:height="setting.award.height" :style="{
        backgroundImage: `url(${setting.award.bgImage})`,
        backgroundSize: '100% 100%',
      }">

    </Resizable>
    <div v-if="isOpenAward === 1">
      <UiImage v-if="!subscribed" v-model:setting="setting.optionBtn.unsubscribe" @click="handleBook"/>
      <UiImage v-else v-model:setting="setting.optionBtn.subscribe" />
    </div>
    <div v-else>
      <UiImage v-model:setting="setting.optionBtn.award" @click="openRewardPopup" />
    </div>

    <!-- 微信小程序内嵌订阅消息弹窗 -->
    <WeSubscribeModal 
      v-if="setting.weappEmbedSubscribeSetting" 
      :show="openWeappEmbedSubscribe"
      v-model:setting="setting.weappEmbedSubscribeSetting" 
      @close="() => toggleWeappEmbedSubscribe(false)" />

    <!-- 微信小程序订阅提示弹窗 -->
    <WxSubscribeGuide 
      v-if="setting.wxSubscribeModal" 
      :show="showOpenSubscribeMessage"
      v-model:setting="setting.wxSubscribeModal"
      @close="() => toggleOpenSubscribeMessage(false)" />

    <Popup z-index="99" :show="showReward" :lock-scroll="false" v-model:setting="setting.modalSetting"
      @close="() => toggleReward(false)">
      <!-- 列表table -->
      <Resizable 
        v-if="setting.modalSetting?.table"
        v-model:x="setting.modalSetting.table.x" 
        v-model:y="setting.modalSetting.table.y" 
        v-model:width="setting.modalSetting.table.width"
        v-model:height="setting.modalSetting.table.height" class="content-box">
          <div :style="listItemStyle" v-for="item in dataList" :key="item.id">
            <div class="item-content">
              <div class="item-text">{{ item.created_at }}</div>
              <div class="item-text">{{ item.phone }}</div>
            </div>
          </div>
          <div :style="listEmpteStyle" v-if="dataList.length === 0" >暂无记录</div>
        </Resizable>
      <!-- 内容文案 -->
      <UiText v-if="setting.modalSetting?.content" v-model:setting="setting.modalSetting.content" />
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'collect-cards-subscribe',
}
</script>

<script setup lang="ts">
import { computed, watch, ref } from 'vue';
import type { CSSProperties } from 'vue';
import Resizable from '../../ui/resizable/index.vue';
import UiImage from '../../ui/image/index.vue';
import Popup from '../../common/popup.vue';
import UiText from '../../ui/text/index.vue';
import { useVModel } from '@vueuse/core';
import type { CollectCardsSubscribeSetting } from './collect-cards-subscribe';
import type { StatefulComponent } from '@bish/types/src/admin';
import { defaultConfig } from './index';
import useActivityStore from '@bish/store/src/modules/activity';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import WeSubscribeModal from '../we-subscribe-modal/we-subscribe-modal.vue';
import WxSubscribeGuide from '../wx-subscribe-guide/wx-subscribe-guide.vue';
import useWxSubscribeMessage from '@bish/hooks/src/business/useWxSubscribeMessage';
import { useLog } from '@bish/hooks/src/useLog';
import { additionalLinkParameters, userAgent } from '@bish/utils/src/utils';
import { removeCosUrl } from '@bish/utils/src/cos'
import { pxTransform } from '@bish/utils/src/viewport';
import { postGetWinnerList } from '@bish/api/src/collect-cards';

export interface CollectCardsSubscribeProps {
  setting: CollectCardsSubscribeSetting,
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<CollectCardsSubscribeProps>(), {
  setting: () => defaultConfig(),
});
const emits = defineEmits<{
  (event: 'update:setting', value: CollectCardsSubscribeSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const setting = useVModel(props, 'setting', emits);
const [showReward, toggleReward] = useControllableStatus(props, emits, { fieldName: 'showGiftRecord' });
const [openWeappEmbedSubscribe, toggleWeappEmbedSubscribe] = useControllableStatus(props, emits, { fieldName: 'showWeappEmbedSubscribe' });

const {
  showOpenSubscribeMessage,
  subscribed,
  fetchRemindStatus,
  resetSubscribeState,
  toggleOpenSubscribeMessage,
} = useWxSubscribeMessage(
  () => { },
  [props, emits, { fieldName: 'showOpenSubscribeMessage' }],
);
const activityStore = useActivityStore();
const activityPageStore = useActivityPageStore();
const { isUserAgentType } = userAgent()
const { uploadLog } = useLog();

const dataList = ref<any[]>([]);

// 是否已经开奖
const isOpenAward = computed(() => Number(activityStore.activityInfo.config?.collect_draw?.is_open_award)); // 1为否 2为是

const listItemStyle = computed(() => {
  return {
    color: setting.value.modalSetting?.table?.color,
    fontSize: pxTransform(setting.value.modalSetting?.table?.fontSize || 12),
    borderBottom: `1px solid ${setting.value.modalSetting?.table?.color}`,
    padding: `${pxTransform(10)} ${pxTransform(5)}`,
  }
})
const listEmpteStyle = computed<CSSProperties>(() => {
  return {
    textAlign: 'center',
    color: setting.value.modalSetting?.table?.color,
    fontSize: pxTransform(setting.value.modalSetting?.table?.fontSize || 12),
    marginTop: pxTransform(40),
  }
})

watch(
  () => activityStore.activityAccountInfo.act_acc_id,
  (newVal) => {
    if (newVal) {
      fetchMiniRemindStatus();
    } else {
      resetSubscribeState();
    }
  },
);

const fetchMiniRemindStatus = async () => {
  const { activityAccountInfo } = activityStore;
  const templateList = setting.value.weappEmbedSubscribeSetting?.templateList || [];
  const remind_template_id = templateList.map(item => item.remindTemplateId).join(',');
  fetchRemindStatus({
    act_id: activityPageStore.activityPageConfig.activity_id!,
    act_acc_id: activityAccountInfo.act_acc_id,
    remind_template_id,
  });
};

const handleBook = () => {
  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }
   // 事件上报--点击订阅
   uploadLog({
    event_name: 'click',
    click_id: 20,
    click_type: 3,
  });
   // 微信小程序内嵌环境：跳转指定的登录页面
   if (isUserAgentType === 'WX_WEBVIEW') {
    navigateWeappSubscribe();
    return
  }
}

/**
 * 跳转到小程序内嵌消息订阅页面
  */
const navigateWeappSubscribe = () => {
  const { themeColor } = activityPageStore.mergedPageConfig
  let cfg = ''
  if (setting.value?.weappEmbedSubscribeSetting) {
    const config = removeCosUrl({
      ...setting.value?.weappEmbedSubscribeSetting,
      tipModal: setting.value?.wxSubscribeModal,
      theme: themeColor, // 附加主题色配置
    })
    cfg = JSON.stringify(config)
  }

  // TODO: 把小程序登录页面提到环境变量
  const weappBishSubscribeUrl = '/subpkg/bish/subscribe'
  const weappBishWebviewUrl = '/subpkg/bish/webview'
  const { activityAccountInfo } = activityStore;
  const url = additionalLinkParameters(
    {
      cfg: encodeURIComponent(cfg),
      act_id: activityPageStore.activityPageConfig.activity_id!,
      act_acc_id: activityAccountInfo.act_acc_id,
      redirect: encodeURIComponent(`${weappBishWebviewUrl}?url=${encodeURIComponent(window.location.href)}`),
    },
    weappBishSubscribeUrl
  )

  wx?.miniProgram.redirectTo({
    url,
  });
}

const openRewardPopup = () => {
  toggleReward(true);
  getWinnerList();
}

const getWinnerList = () => {
  postGetWinnerList({
    act_id: activityStore.activityInfo?.init?.id,
  }).then((res) => {
    if (res.code === 0) {
      dataList.value = (res.data as unknown) as any[];
    }
  })
}

</script>

<style scoped lang="less">
.content-box {
  overflow-y: auto;
  overflow-x: hidden;
  .item-content {
    display: flex;
    justify-content: space-around;
    .item-text {
      font-weight: bold;
    }
  }
}
</style>
