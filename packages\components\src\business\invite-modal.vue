<!--
* @Description: 邀请好友/组队-弹窗
-->
<template>
  <Popup
    z-index="99"
    :show="show"
    :lock-scroll="false"
    v-model:setting="setting"
    @close="handleClose"
    @ok="handleCopy"
  >
    <!-- 邀请文案 -->
    <UiText v-if="setting.content?.content" v-model:setting="setting.content" class="invite-modal-content" />
    <!-- 链接 -->
    <UiText
      v-if="setting.linkText?.content"
      v-model:setting="setting.linkText"
      :interpolation="linkInterpolation"
      class="invite-modal-link"
      :textStyle="linkTextStyle"
    />
    <!-- 复制按钮 -->
    <UiImage
      v-if="setting.copyLinkBtn?.imgLink"
      v-model:setting="setting.copyLinkBtn"
      @click.stop="handleCopy"
    />
    <!-- 去邀请：微信小程序环境 -->
    <button
      v-if="showShareBtn"
      :style="shareBtnStyle"
      class="invite-modal-wx-btn"
      open-type="share"
      @click="uploadLogInvite"
    />
  </Popup>
</template>

<script lang="ts">
import type { CSSProperties } from 'vue';
import type { TextSetting } from '../ui/text/index.vue';
import type { ImageSetting } from '../ui/image/index.vue';
import type { PopupSetting } from '../common/popup.vue';

export interface InviteModalSetting extends PopupSetting {
  /**
   * 邀请文案
   */
  content?: TextSetting
  /**
   * 链接文本
   */
  linkText?: TextSetting
  /**
   * 复制链接按钮
   */
  copyLinkBtn?: ImageSetting
  /**
   * 复制模板
   */
  copyTemplate?: string
  /**
   * 复制模板列表
   */
  copyTemplates: {
    text: string
    /**
     * 海报，目前只有小程序页面分享才用到
     */
    poster?: string
  }[]
}

export interface InviteModalProps {
  /**
   * 配置
   */
  setting: InviteModalSetting
  /**
   * 是否显示
   */
  show: boolean
  /**
   * 当前队伍id，若存在则表示是组队邀请，否则表示是普通好友邀请
   */
  teamId?: number
  /**
   * 邀请链接key
   */
  keyName?: string
  /**
   * 是否需要绑定角色，默认 true
   */
  bindRole?: boolean
}

export const defaultConfig = (): InviteModalSetting => {
  return {
    x: 0,
    y: 0,
    width: 373,
    height: 353,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403310667_.png',
    okBtn: {
      x: 119,
      y: 260,
      width: 141,
      height: 39,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403327152_.png',
    },
    closeBtn: {
      x: 320,
      y: -37,
      width: 35,
      height: 35,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403274633_.png',
    },
    content: {
      x: 102,
      y: 194,
      width: 174,
      height: 80,
      fontSize: 14,
      content: '',
      color: '#424272',
      align: 'center',
    },
    linkText: {
      x: 56,
      y: 164,
      width: 228,
      height: 31,
      fontSize: 16,
      content: '{{link}}',
      color: '#58586D',
      alignItems: 'center',
      align: 'center',
      fontWeight: true,
      background: 'rgba(0, 0, 0, 0.25)',
    },
    copyLinkBtn: {
      x: 294,
      y: 164,
      width: 31,
      height: 30,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403332431_.png',
    },
    copyTemplate: '百级解禁，商城有礼！加入我的队伍共领自选表情包，更有月卡折扣券、648通宝等好礼哦：{{link}}',
    copyTemplates: [],
  };
};

export default {
  name: 'invite-modal',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, computed, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import useAdminStore from '@bish/store/src/modules/admin';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import { useLog } from '@bish/hooks/src/useLog';
import useShortLink from '@bish/hooks/src/business/useShortLink';
import { pxTransform } from '@bish/utils/src/viewport';
import { additionalLinkParameters, userAgent } from '@bish/utils/src/utils';
import { whiteUrl } from '@bish/utils/src/urlSearch';
import { interpolateString, copyText } from '../__utils/text';
import UiText from '../ui/text/index.vue';
import UiImage from '../ui/image/index.vue';
import Popup from '../common/popup.vue';

const props = withDefaults(defineProps<InviteModalProps>(), {
  setting: defaultConfig,
  show: false,
  bindRole: true,
});

const emits = defineEmits<{
  (event: 'update:setting', value: InviteModalSetting): void;
  (event: 'close'): void;
}>();

const setting = useVModel(props, 'setting', emits);

const adminStore = useAdminStore();
const userStore = useUserStore();
const activityStore = useActivityStore();

const { uploadLog } = useLog();
const { shortLink, getShortLink } = useShortLink();

const { isUserAgentType } = userAgent();

// 微信小程序环境
const isWeapp = computed(() => {
  return isUserAgentType === 'WX_MINI';
});

const showShareBtn = computed(() => {
  return isWeapp.value && activityStore.activityAccountInfo?.account_id && !activityStore.isSummit(false);
});

const linkTextStyle = computed(() => {
  return {
    padding: `0 ${pxTransform(6)}`,
  } as CSSProperties;
});

const linkInterpolation = computed(() => {
  return {
    link: shortLink.value,
  };
});

const shareBtnStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.okBtn?.x || 0),
    top: pxTransform(setting.value.okBtn?.y || 0),
    width: pxTransform(setting.value.okBtn?.width || 0),
    height: pxTransform(setting.value.okBtn?.height || 0),
  };
});

/**
 * 创建短链
 */
const createShortLink = () => {
  const { userData } = userStore;
  const { activityAccountInfo } = activityStore;

  // 存在有一些活动不需要绑定角色
  let invite_user = userData?.phone_number || activityAccountInfo?.account_id || '';
  if (activityAccountInfo.role_info?.server_name) {
    invite_user = `${activityAccountInfo.role_info?.server_name}-${activityAccountInfo.role_info?.role_name}`;
  }
  const params: Record<string, any> = {
    invite_code: activityAccountInfo.invite_code,
    recall_account_id: encodeURIComponent(activityAccountInfo.account_id),
    invite_user: encodeURIComponent(invite_user),
    invite_type: 1,
  }
  if (props.teamId) {
    params.team_id = props.teamId;
  }
  if (props.keyName) {
    params[props.keyName] = 1; // 特殊标识，以免与组队邀请发生冲突
  }
  const pureUrl = whiteUrl(window?.location.href);
  getShortLink(
    additionalLinkParameters(params, pureUrl)
  );
};

watch(
  () => props.show,
  (newVal) => {
    if (newVal && !adminStore.editable && !isWeapp.value) {
      createShortLink();
    }
  },
  { immediate: true },
);

const uploadLogInvite = () => {
  // 事件上报：点击复制分享链接
  uploadLog({
    event_name: 'click',
    click_id: 15,
    click_type: 3,
  });
};

/**
 * 复制链接
 */
const handleCopy = () => {
  uploadLogInvite();

  // 登录态拦截
  const pass = activityStore._checkIn(props.bindRole);
  if (!pass) {
    return;
  }

  // 随机选择一条邀请文案
  const textList = setting.value.copyTemplates?.map(item => item.text) || []
  const randomIndex = Math.floor(Math.random() * textList.length)
  const randomText = textList[randomIndex] ? `${textList[randomIndex]}{{link}}` : ''
  const mergeText = setting.value.copyTemplate || randomText
  const url = mergeText
    ? interpolateString(mergeText, { link: shortLink.value })
    : shortLink.value;
  copyText(url, { success: '链接已复制，快去分享给好友吧~', error: '复制失败' });
}

const handleClose = () => {
  emits('close');
};
</script>

<style>
.invite-modal-content {
  font-weight: 600;
  line-height: 1.3;
}

.invite-modal-link .ui-text > div {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
  text-align: center
}

.invite-modal-wx-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: transparent;
}

.invite-modal-wx-btn::after {
  border: none;
}
</style>