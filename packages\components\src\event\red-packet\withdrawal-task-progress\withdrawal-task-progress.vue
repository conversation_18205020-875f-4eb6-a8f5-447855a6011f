<!--
* @Description: 提现任务进度
-->
<template>
  <Resizable
    v-if="showProgress"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :position="setting.position"
    class="withdrawal-task-progress"
  >
    <ResizableProvider>
      <!-- 提示语 -->
      <UiText :setting="setting.tips">
        {{ withdrawStatusText }}
      </UiText>
      
      <!-- 任务进度条 -->
      <TaskProgress :setting="setting.progress" />

      <!-- 红包图片 -->
      <UiImage :setting="setting.redPacket" />

      <!-- 我的钱包 -->
      <MyWallet
        ref="myWalletRef"
        :setting="setting.myWallet"
        v-model:status="status"
        :open-wallet="handleOpenWallet"
        :class="{ 'withdrawal-task-progress__completed': allCompleted }"
      />
    </ResizableProvider>

    <!-- 暴击引导充值弹窗 -->
    <Popup
      v-if="setting.hit?.enabled"
      z-index="99"
      :show="openHit"
      :lock-scroll="false"
      v-model:setting="setting.hit"
      @close="() => toggleHit(false)"
    >
      <!-- 标题 -->
      <UiText
        v-if="setting.hit.title.enabled"
        v-model:setting="setting.hit.title"
      />

      <!-- 副标题 -->
      <UiText
        v-if="setting.hit.subTitle.enabled"
        v-model:setting="setting.hit.subTitle"
      />

      <!-- 放弃按钮 -->
      <UiImage
        v-if="setting.hit.cancelBtn.enabled"
        v-model:setting="setting.hit.cancelBtn"
        @click="() => handleSetPromoteEarnings(1)"
      />

      <!-- 确认按钮 -->
      <UiImage
        v-model:setting="setting.hit.confirmBtn"
        @click="() => handleSetPromoteEarnings(2)"
      />

      <!-- 放弃提示 -->
      <UiText
        v-if="setting.hit.cancelTips.enabled"
        v-model:setting="setting.hit.cancelTips"
      />
    </Popup>

    <!-- 暴击引导弹窗 -->
    <Popup
      v-if="setting.bullseye?.enabled"
      z-index="99"
      :show="openBullseye"
      :lock-scroll="false"
      v-model:setting="setting.bullseye"
      @close="() => toggleBullseye(false)"
    >
      <!-- 标题 -->
      <UiText
        v-if="setting.bullseye.title.enabled"
        v-model:setting="setting.bullseye.title"
      />

      <!-- 放弃按钮 -->
      <UiImage
        v-if="setting.bullseye.cancelBtn.enabled"
        v-model:setting="setting.bullseye.cancelBtn"
        @click="handleCloseBullseye"
      />

      <!-- 确认按钮 -->
      <UiImage
        v-model:setting="setting.bullseye.confirmBtn"
        @click="handleJumpBullseye"
      />
    </Popup>

    <!-- 内嵌微信商城充值页 -->
    <Popup
      v-if="setting.weappEmbedSetting?.modalSetting"
      :show="openWeappEmbed"
      v-model:setting="setting.weappEmbedSetting.modalSetting"
      z-index="101"
      :resizable="false"
      @close="() => toggleWeappEmbed(false)"
    >
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'withdrawal-task-progress',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, ref } from 'vue';
import { useVModel } from '@vueuse/core';
// #ifdef H5
import { showConfirmDialog } from 'vant'
// #endif
import { showToast } from '@bish/ui/src/toast';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useActivityStore from '@bish/store/src/modules/activity';
import useUserStore from '@bish/store/src/modules/user';
import useAdminStore from '@bish/store/src/modules/admin';
import type { ComponentWithUserInfoTask } from '@bish/api/src/activity';
import { setPromoteEarnings } from '@bish/api/src/redEnvelope';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { useEnv } from '@bish/hooks/src/useEnv';
import { userAgent, additionalLinkParameters, sleep } from '@bish/utils/src/utils';
import { useShop } from '@bish/hooks/src/useShop';
import { navigateScroll } from '../../../__utils/location';
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../ui/image/index.vue';
import UiText from '../../../ui/text/index.vue';
import MyWallet from '../my-wallet/my-wallet.vue';
import TaskProgress from '../../../business/task-collection/task-progress/task-progress.vue';
import Popup from '../../../common/popup.vue';
import type { MyWalletInstance } from '../my-wallet';
import type { WithdrawalTaskProgressSetting, WithdrawalStatus } from './index';
import { defaultConfig } from './index';

export interface WithdrawalTaskProgressProps {
  /**
   * 配置
   */
  setting: WithdrawalTaskProgressSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<WithdrawalTaskProgressProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: WithdrawalTaskProgressSetting): void;
}>();

const myWalletRef = ref<MyWalletInstance>();

const setting = useVModel(props, 'setting', emits);
const status = useVModel(props, 'status', emits);

const [openHit, toggleHit] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showHit' });
const [openBullseye, toggleBullseye] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showBullseye' });
const [openWeappEmbed, toggleWeappEmbed] = useControllableStatus(props, emits, { fieldName: 'showWeappEmbed' });

const activityPageStore = useActivityPageStore();
const activityStore = useActivityStore();
const userStore = useUserStore();
const adminStore = useAdminStore();

const { VITE_APP_TYPE } = useEnv();
const { shopUrl } = useShop();
const { isUserAgentType } = userAgent();

// 用户累计获得金额
const totalAmount = computed(() => {
  const res = activityStore.componentWithUserInfo?.component_with_user_info?.red_envelope?.total_amount || 0;
  return Number(res) || 0;
});

const showProgress = computed(() => {
  // 兼容旧数据：setting.value.alwaysShow === undefined
  return adminStore.editable
    || setting.value.alwaysShow === undefined
    || setting.value.alwaysShow === 1
    || (
      setting.value.alwaysShow === 0
      && totalAmount.value > 0
    );
});

// 关联的任务列表
const relatedTaskList = computed(() => { 
  const ids = setting.value.progress.marks.map(item => item.taskId);
  const taskList: ComponentWithUserInfoTask = [];
  ids.forEach((item) => {
    const task = activityStore.componentWithUserInfo.component_with_user_info?.task?.find((task) => task.id === item);
    if (task) {
      taskList.push(task);
    }
  });
  return taskList;
});

const allCompleted = computed(() => {
  return relatedTaskList.value.every(item => item.status !== 1);
});

const progress = computed(() => {
  const current = Math.ceil(relatedTaskList.value.filter(item => item.status !== 1).length / relatedTaskList.value.length * 100);
  return {
    current,
    left: 100 - current,
  };
});

// 是否已提现
const hasRecord = computed(() => {
  return activityStore.componentWithUserInfo.component_with_user_info?.red_envelope?.draw_list?.length > 0;
});

/**
 * 暴击红包任务配置
 */
const taskConfigHit = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskIdHit);
});

/**
 * 用户暴击红包任务信息
 */
const userTaskHit = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskIdHit);
});

const remainingDays = computed(() => {
  const activityEndTime = activityStore.activityInfo.init?.activity_end_time || 0;
  const withdrawEndTime = activityStore.componentWithUserInfo.component_with_user_info?.red_envelope?.withdraw_end_time || 0;
  const now = Date.now() / 1000;
  
  // 按自然日计算活动结束剩余天数
  const activityEndDate = new Date(activityEndTime * 1000);
  const nowDate = new Date(now * 1000);
  
  // 将日期重置为当天的0点，只比较日期部分
  const activityEndDay = new Date(
    activityEndDate.getFullYear(), 
    activityEndDate.getMonth(), 
    activityEndDate.getDate()
  ).getTime();
  
  const nowDay = new Date(
    nowDate.getFullYear(), 
    nowDate.getMonth(), 
    nowDate.getDate()
  ).getTime();
  
  // 计算天数差，使用Math.floor而不是Math.ceil
  // 这样当天到当天会计算为0天，然后+1变成1天
  // 例如：5月7日到5月13日应该是6天，+1后为7天
  const dayDiff = (activityEndDay - nowDay) / (24 * 60 * 60 * 1000);
  const activityRemainingDays = Math.floor(dayDiff) + 1;
  
  // 按自然日计算提现结束剩余天数
  const withdrawEndDate = new Date(withdrawEndTime * 1000);
  const withdrawEndDay = new Date(
    withdrawEndDate.getFullYear(), 
    withdrawEndDate.getMonth(), 
    withdrawEndDate.getDate()
  ).getTime();
  
  const withdrawDayDiff = (withdrawEndDay - nowDay) / (24 * 60 * 60 * 1000);
  const withdrawRemainingDays = Math.floor(withdrawDayDiff) + 1;
  
  // 如果剩余天数小于等于0，返回0
  if (activityRemainingDays <= 0 && withdrawRemainingDays <= 0) {
    return 0;
  }
  
  // 当活动结束天数小于提现结束天数时，显示活动结束天数
  // 否则显示提现结束天数
  return Math.max(1, Math.min(activityRemainingDays, withdrawRemainingDays));
});

const withdrawStatus = computed<WithdrawalStatus>(() => {
  const activityEndTime = activityStore.activityInfo.init?.activity_end_time || 0;
  const withdrawEndTime = activityStore.componentWithUserInfo.component_with_user_info?.red_envelope?.withdraw_end_time || 0;
  const now = Date.now() / 1000;

  // 处理自然日比较
  // 将时间戳转换为日期对象
  const withdrawEndDate = new Date(withdrawEndTime * 1000);
  const activityEndDate = new Date(activityEndTime * 1000);
  
  // 将日期重置为当天的23:59:59，只比较日期部分
  const withdrawEndDay = new Date(
    withdrawEndDate.getFullYear(), 
    withdrawEndDate.getMonth(), 
    withdrawEndDate.getDate(),
    23, 59, 59
  ).getTime() / 1000;
  
  // 活动时间不需要重置
  const activityEndDay = new Date(activityEndDate).getTime() / 1000;

  // 任务进度完成，且已提现
  if (allCompleted.value && hasRecord.value) {
    return 'cashed';
  }
  // 已过提现时间 or 活动已结束
  if (now > withdrawEndDay || now > activityEndDay) {
    return 'ended';
  }
  // 任务进度完成，且未提现
  if (allCompleted.value && !hasRecord.value) {
    return 'not-withdrawn';
  }
  // 任务进度完成
  if (allCompleted.value) {
    return 'completed';
  }
  // 任务进度未完成
  if (!allCompleted.value) {
    return 'in-progress';
  }
  return 'not-started';
});

const withdrawStatusText = computed(() => {
  switch (withdrawStatus.value) {
    case 'not-started':
      return '未开始';
    case 'in-progress':
      return `剩余${remainingDays.value}天，仅差${progress.value.left}%即将成功提现`;
    case 'completed':
    case 'not-withdrawn':
      return `已可提现，剩余${remainingDays.value}天！`;
    case 'cashed':
      return '已提现';
    case 'ended':
      return '很遗憾，已超过提现时间，无法提现';
  }
});

const handleOpenWallet = () => {
  // 超过提现时间
  if (allCompleted.value && withdrawStatus.value === 'ended') {
    showToast('已过期');
    return false;
  }
  if (!allCompleted.value) {
    showToast(`还差${progress.value.left}%即将成功提现`);
    return false;
  }
  // 暴击任务已完成，且未进行提升收益（暴击）
  if (
    userTaskHit.value?.status === 2 && 
    !activityStore.activityAccountInfo.is_promote_earnings
  ) {
    toggleBullseye(true);
    return false;
  }
  // 暴击任务未完成，且未提升收益
  if (
    userTaskHit.value?.status === 1 && 
    activityStore.activityAccountInfo.is_promote_earnings !== 1
  ) {
    toggleHit(true);
    return false;
  }
  return true;
};

const handleCloseBullseye = async () => {
  toggleBullseye(false);
  await sleep(300);
  handleOpenMyWallet();
};

const handleJumpBullseye = async () => {
  toggleBullseye(false);
  navigateScroll('.withdrawal-task-progress');
};

const handleOpenMyWallet = () => {
  myWalletRef.value?.open();
};

const promoteEarnings = async (promote: 1 | 2) => {
  try {
    const res = await setPromoteEarnings({
      act_id: activityPageStore.activityPageConfig.activity_id,
      act_acc_id: `${activityStore.activityAccountInfo.act_acc_id}`,
      is_promote: promote,
    });
    if (res.code === 0) {
      toggleHit(false);
      if (promote === 1) {
        await sleep(300);
        handleOpenMyWallet();
      }
    } else {
      toggleHit(false);
    }
  } catch (error) {
    console.warn('设置提升收益失败', error);
  } finally {
    await activityStore.getActivityAccountInfo();
  }
};

/**
 * 设置提升收益
 * @param promote 1: 否 2: 是
 */
const handleSetPromoteEarnings = async (promote: 1 | 2) => {
  if (promote === 2) {
    // 设置收益，不需要调用接口，接口只能保存一次，后续不可能更改
    // await promoteEarnings(promote);
    // 微信小程序内嵌环境：跳转指定的登录页面
    if (isUserAgentType === 'WX_WEBVIEW') {
      navigateWeappShop();
      return;
    }
    handleGoShop();
    return;
  }
  // 微信小程序api不一样
  if (VITE_APP_TYPE === 'wx') {
      uni.showModal({
        title: '提示',
        content: '放弃后再也无法活动暴击机会，是否确认放弃',
        success: (res) => {
          if (res.confirm) {
            promoteEarnings(promote);
          }
        }
      })
    } else {
      showConfirmDialog({
        title: '提示',
        message: `放弃后再也无法活动暴击机会，是否确认放弃`,
        beforeClose: (action) =>
          new Promise((closeResolve) => {
            if (action === 'cancel') {
              closeResolve(true);
            }
            if (action === 'confirm') {
              closeResolve(true);
              promoteEarnings(promote);
            }
          }),
      })
    }
};

/**
 * 跳转商城
 */
const handleGoShop = () => {
  const params: Record<string, any> = {}
  
  if (userStore.userData.token) {
    params.token = userStore.userData.token;
  }
  const newShopUrl = additionalLinkParameters(
    params,
    shopUrl.value,
  );
  window.open(newShopUrl, !adminStore.editable ? '_self' : '_blank');
};

/**
 * 跳转到小程序内嵌前往充值页面
 */
const navigateWeappShop = () => {
  const { init } = activityStore.activityInfo;
  const { themeColor } = activityPageStore.mergedPageConfig;
  
  let cfg = '';
  if (setting.value?.weappEmbedSetting) {
    cfg = JSON.stringify({
      ...setting.value?.weappEmbedSetting,
      theme: themeColor, // 附加主题色配置
    });
  }
  
  // TODO: 把小程序前往充值页面提到环境变量
  const weappBishMallRechargeUrl = '/subpkg/bish/mall-recharge';
  const url = additionalLinkParameters(
    {
      cfg: encodeURIComponent(cfg),
      project_id: init?.project_id,
    },
    weappBishMallRechargeUrl,
  );

  wx?.miniProgram.navigateTo({
    url,
  });
};
</script>

<style>
.withdrawal-task-progress {
}
.withdrawal-task-progress .withdrawal-task-progress__completed .my-wallet {
  animation: withdrawal-pulse 2s infinite;
}

@keyframes withdrawal-pulse {
  0%, 100% { /* 开始和结束状态 */
    transform: scale(1);
  }
  50% { /* 中间状态 */
    transform: scale(1.1); /* 放大10% */
  }
}
</style>