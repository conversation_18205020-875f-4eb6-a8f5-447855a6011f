<!--
* @Description: 集卡
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="collect-cards"
  >
    <!-- 卡组 -->
    <CollectCardsGroup
      v-model:setting="setting.group"
      v-model:current="activeGroupIndex"
      :card="collectCardCurrent"
      :user-card-info-current="userCardInfoCurrent"
      @scratch="handleScratch"
    />

    <!-- 卡片列表 -->
    <CollectCardsList
      v-model:setting="setting.list"
      v-model:current="activeIndex"
      :list="collectCardList"
      :user-collect-draw="userCollectDraw"
      @change="handleListChange"
    />

    <!-- 刮卡 -->
    <CollectCardsScratch
      v-model:setting="setting.scratch"
      :show="showScratch"
      :auto-flip="true"
      :lottery-id="setting.lotteryId"
      :need-bind-role="setting.needBindRole"
      :data="scratchData!"
      @close="toggleScratch(false)"
      @scratched="handleScratched"
    />

    <!-- 赠送 -->
    <CollectCardsGive
      v-model:setting="setting.give"
      v-model:status="status"
      :user-card-info-current="userCardInfoCurrent"
      :receive-current="receiveCurrent"
      @share-guide="toggleWeappShareGuide(true)"
    />

    <!-- 索要 -->
    <CollectCardsAskFor
      v-model:setting="setting.askFor"
      v-model:status="status"
      :user-card-info-current="userCardInfoCurrent"
      :receive-current="receiveCurrent"
      @share-guide="toggleWeappShareGuide(true)"
    />

    <!-- 集卡完成提示 -->
    <UiText
      v-if="setting.completeTip.enabled && showCompleteTip"
      v-model:setting="setting.completeTip"
      :interpolation="interpolation"
    />

    <!-- 微信小程序内嵌-引导右上角分享 -->
    <ShareGuide
      v-if="setting.shareGuide"
      :show="openWeappShareGuide"
      v-model:setting="setting.shareGuide"
      @close="() => toggleWeappShareGuide(false)"
    />
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'collect-cards',
}
</script>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import Resizable from '../../ui/resizable/index.vue';
import UiText from '../../ui/text/index.vue';
import type { CollectCardsSetting } from './collect-cards';
import { defaultConfig } from './index';
import ShareGuide from '../share-guide/share-guide.vue';
import CollectCardsGroup from './components/collect-cards-group/collect-cards-group.vue';
import type { ScratchEventData } from './components/collect-cards-group/collect-cards-group.vue';
import CollectCardsList from './components/collect-cards-list/collect-cards-list.vue';
import CollectCardsScratch from './components/collect-cards-scratch/collect-cards-scratch.vue';
import CollectCardsGive from './components/collect-cards-give/collect-cards-give.vue';
import CollectCardsAskFor from './components/collect-cards-ask-for/collect-cards-ask-for.vue';

export interface CollectCardsProps {
  setting: CollectCardsSetting
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<CollectCardsProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: CollectCardsSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activeIndex = ref(0);
const activeGroupIndex = ref(0);
const scratchData = ref<ScratchEventData>({
  id: 0,
  name: '',
  icon: '',
  card_log_id: 0,
});

const setting = useVModel(props, 'setting', emits);
const status = useVModel(props, 'status', emits);

const [showScratch, toggleScratch] = useControllableStatus(props, emits, { fieldName: 'showScratch' });
const [openWeappShareGuide, toggleWeappShareGuide] = useControllableStatus(props, emits, { fieldName: 'showWeappShareGuide' });

const activityStore = useActivityStore();
const adminStore = useAdminStore();

/**
 * 卡片配置信息
 */
const collectCardConfig = computed(() => {
  return activityStore.activityInfo.config?.collect_draw || {};
});

/**
 * 卡片列表
 */
const collectCardList = computed(() => {
  if (adminStore.editable) {
    return [
      {
        activity_id: 759,
        config_id: 1,
        icon: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250414/1744624027676-%E7%81%B5%E5%AE%A0%E8%B2%94%E8%B2%85%E5%8D%A1.png',
        id: 1,
        name: '灵宠貔貅卡',
      },
      {
        activity_id: 759,
        config_id: 1,
        icon: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250414/1744624031414-%E6%8B%9B%E8%B4%A2%E7%8C%AB%E5%8D%A1.png',
        id: 1,
        name: '招财猫',
      },
      {
        activity_id: 759,
        config_id: 1,
        icon: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250414/1744624034815-%E8%A5%BF%E5%9F%9F%E5%95%86%E4%BA%BA%E5%8D%A1.png',
        id: 1,
        name: '西域商人',
      },
    ]
  }
  return collectCardConfig.value.detail || [];
});

/**
 * 当前选中卡片信息
 */
const collectCardCurrent = computed(() => {
  return collectCardList.value[activeIndex.value];
});

/**
 * 用户集卡信息
 */
const userCollectDraw = computed(() => {
  return activityStore.componentWithUserInfo.component_with_user_info?.collect_draw;
});

/**
 * 当前选中用户卡片信息
 */
const userCardInfoCurrent = computed(() => {
  return userCollectDraw.value?.card_list?.find(item => item.id === collectCardCurrent.value?.id);
});

/**
 * 当前选中用户卡片获取记录
 */
const receiveCurrent = computed(() => {
  return userCardInfoCurrent.value?.receive_log[activeGroupIndex.value];
});

const showCompleteTip = computed(() => {
  return adminStore.editable || userCollectDraw.value?.is_full_collect;
});

const interpolation = computed(() => {
  return {
    num: userCollectDraw.value?.rank || 0,
  };
});

const handleListChange = () => {
  // 重置卡组索引
  // activeGroupIndex.value = 0;
};

const handleScratch = (data: ScratchEventData) => {
  scratchData.value = data;
  toggleScratch(true);
};

const handleScratched = () => {
  // TODO: 请求刮奖接口
  console.log('刮奖成功');
};
</script>

<style>
.collect-cards {}
</style> 
