<!--
* @Description: 恭喜获得-弹窗
-->
<template>
  <Popup
    z-index="99"
    :show="show"
    :lock-scroll="false"
    v-model:setting="setting"
    @close="handleClose"
    @ok="handleClose"
  >
    <!-- 自定义弹窗标题 -->
    <template #title>
      <div>{{ title }}</div>
    </template>

    <!-- 奖品图片 -->
    <Resizable
      v-if="!isEmpty && (setting.prizeImg.show ?? true)"
      v-model:x="setting.prizeImg.x"
      v-model:y="setting.prizeImg.y"
      v-model:width="setting.prizeImg.width"
      v-model:height="setting.prizeImg.height"
      :class="{ 'congratulations-img-wrapper': !prize?.icon }"
    >
      <img :src="prize?.icon" class="congratulations-img"  />
    </Resizable>

    <!-- 谢谢参与 -->
    <div v-if="isEmpty" class="congratulations-empty">
      <!-- 这里用的临时路径，todo：放在组件目录下 -->
      <img
        :style="emptyImgStyle"
        class="congratulations-empty-img"
        src="https://pingtai-img.shiyue.com/cdn-miniprogram-member-static/activity/bish/congratulations-empty.png"
      />
      <div :style="emptyTipStyle" class="congratulations-empty-tip">
        差一点就中奖了
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #ok>
      <div>
        <!-- 默认按钮：游戏道具奖励 -->
        <template v-if="adminStore.editable || prize?.type === PRIZE_TYPE_GAME_ITEMS">
          <UiImage
            v-if="setting.okBtn?.imgLink"
            v-model:setting="setting.okBtn"
            @click="handleClose"
          />
        </template>
        <!-- 实物奖励 -->
        <template v-if="prize?.type === PRIZE_TYPE_PHYSICAL">
          <UiImage
            v-if="setting.addressBtn?.enabled"
            v-model:setting="setting.addressBtn"
            @click="handleShowAddressForm"
          />
          <!-- 兼容展示默认按钮，有些活动存在道具是实物，但是不需要填写地址，通过联系客服发放 -->
          <template v-else>
            <UiImage
              v-if="setting.okBtn?.imgLink"
              v-model:setting="setting.okBtn"
              @click="handleClose"
            />
          </template>
        </template>
        <!-- 现金奖励 -->
        <template v-if="prize?.type === PRIZE_TYPE_RED_PACKET">
          <UiImage
            v-if="setting.withdrawBtn?.enabled"
            v-model:setting="setting.withdrawBtn"
            @click="handleWithdraw"
          />
          <!-- 兼容展示默认按钮，有些活动存在道具是红包，但是不需要展示立即提现按钮 -->
          <template v-else>
            <UiImage
              v-if="setting.okBtn?.imgLink"
              v-model:setting="setting.okBtn"
              @click="handleClose"
            />
          </template>
        </template>
      </div>
    </template>

    <!-- 奖品名称 -->
    <UiText
      v-if="!isEmpty"
      v-model:setting="setting.prizeName"
      :scrollspy="false"
      :interpolation="prizeNameInterpolation"
    />
    <!-- 提示 -->
    <UiText
      v-if="showTip"
      v-model:setting="setting.tip"
      :scrollspy="false"
      :editableContent="false"
    >
      <div v-if="adminStore.editable || prize?.type === PRIZE_TYPE_GAME_ITEMS" v-html="setting.gameItemTip" />
      <div v-else-if="prize?.type === PRIZE_TYPE_RED_PACKET" v-html="setting.redPacketTip" />
      <div v-else v-html="setting.otherTip" />
    </UiText>
  </Popup>
</template>

<script lang="ts">
import type { LotteryPrizeDrawData } from '@bish/api/src/lottery';
import type { CommonSetting } from '@bish/types/src';
import type { TextSetting } from '../ui/text/index.vue';
import type { PopupSetting } from '../common/popup.vue';
import type { ImageSetting } from '../ui/image/index.vue';

export interface CongratulationsModalSetting extends PopupSetting {
  /**
   * 是否需要恭喜弹窗，默认 true，不需要则展示对话框
   */
  required?: boolean
  /**
   * 奖品图片
   */
  prizeImg: CommonSetting & {
    /**
     * 是否显示，默认 true
     */
    show?: boolean
  }
  /**
   * 奖品名称
   */
  prizeName: TextSetting
  /**
   * 提示
   */
  tip: TextSetting
  /**
   * 游戏道具提示
   */
  gameItemTip: string
  /**
   * 红包提示
   */
  redPacketTip: string
  /**
   * 其他提示
   */
  otherTip: string
  /**
   * 填写地址按钮
   */
  addressBtn: ImageSetting
  /**
   * 立即提现按钮
   */
  withdrawBtn: ImageSetting
}

export interface CongratulationsModalProps {
  /**
   * 配置
   */
  setting: CongratulationsModalSetting
  /**
   * 是否显示
   */
  show: boolean
  /**
   * 奖品
   */
  prize: LotteryPrizeDrawData
}

export const defaultConfig = (): CongratulationsModalSetting => {
  return {
    required: true,
    x: 0,
    y: 0,
    width: 375,
    height: 376,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1722967826730_.png',
    okBtn: {
      x: 119,
      y: 288,
      width: 143,
      height: 49,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1722967840752_.png',
    },
    closeBtn: {
      x: 330,
      y: -5,
      width: 33,
      height: 33,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1722967853959_.png',
    },
    title: {
      x: 88,
      y: 38,
      width: 200,
      height: 38,
      fontSize: 22,
      color: '#FFFFFF',
      align: 'center',
      alignItems: 'center',
    },
    prizeImg: {
      x: 126,
      y: 90,
      width: 120,
      height: 120,
      show: true,
    },
    prizeName: {
      x: 38,
      y: 229,
      width: 300,
      height: 35,
      fontSize: 20,
      color: '#FFE08D',
      align: 'center',
      content: '{{prizeName}}',
    },
    tip: {
      x: 131,
      y: 260,
      width: 113,
      height: 35,
      fontSize: 12,
      color: '#FFE08D',
      align: 'center',
    },
    gameItemTip: '请到游戏内查收奖品',
    redPacketTip: '请到我的奖励内查看',
    otherTip: '请添加企微领取奖品',
    addressBtn: {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      imgLink: '',
    },
    withdrawBtn: {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      imgLink: '',
    },
  };
};

export default {
  name: 'congratulations-modal',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import usePopupStore from '@bish/store/src/modules/popup';
import useAdminStore from '@bish/store/src/modules/admin';
import { pxTransform } from '@bish/utils/src/viewport';
import { PRIZE_TYPE_PHYSICAL, PRIZE_TYPE_GAME_ITEMS, PRIZE_TYPE_EMPTY, PRIZE_TYPE_RED_PACKET } from '../__constants/prize';
import UiText from '../ui/text/index.vue';
import UiImage from '../ui/image/index.vue';
import Resizable from '../ui/resizable/index.vue';
import Popup from '../common/popup.vue';

const props = withDefaults(defineProps<CongratulationsModalProps>(), {
  setting: defaultConfig,
  show: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: CongratulationsModalSetting): void;
  (event: 'ok'): void;
  (event: 'close'): void;
}>();

const popupStore = usePopupStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const prizeNameInterpolation = computed(() => {
  return {
    prizeName: props.prize?.name,
  }
});

const isEmpty = computed(() => {
  return props.prize?.type === PRIZE_TYPE_EMPTY;
});

const title = computed(() => {
  return isEmpty.value ? '谢谢参与' : '恭喜获得';
});

const emptyImgStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(props.setting.prizeImg.x!),
    top: pxTransform(props.setting.prizeImg.y!),
    width: pxTransform(props.setting.prizeImg.width),
    height: pxTransform(props.setting.prizeImg.width),
  };
});

const emptyTipStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(props.setting.prizeName.x!),
    top: pxTransform(props.setting.prizeName.y!),
    width: pxTransform(props.setting.prizeName.width),
    height: pxTransform(props.setting.prizeName.height),
    fontSize: pxTransform(props.setting.prizeName.fontSize!),
    fontWeight: props.setting.prizeName.fontWeight ? '700' : '400',
    color: '#C9A892',
  };
});

const showTip = computed(() => {
  return adminStore.editable || (props.prize?.type !== PRIZE_TYPE_EMPTY);
});

const handleClose = () => {
  emits('close');
};

const handleShowAddressForm = () => {
  handleClose();
  popupStore.setShowAddressForm(true);
};

const handleWithdraw = () => {
  handleClose();
  popupStore.setShowEnvelopeRecords(true);
};
</script>

<style>
.congratulations-img-wrapper {
  background-color: #f4f4f4;
}

.congratulations-img {
  width: 100%;
  height: 100%;
}

.congratulations-empty {}

.congratulations-empty-img {
  position: absolute;
}

.congratulations-empty-tip {
  position: absolute;
  text-align: center;
}
</style>