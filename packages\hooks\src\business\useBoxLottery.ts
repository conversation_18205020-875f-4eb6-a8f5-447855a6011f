import { computed, ref, } from 'vue';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore, { GAME_CIRCLE } from '@bish/store/src/modules/activity';
import { postLotteryPrizeDraw } from '@bish/api/src/lottery';
import type { LotteryPrizeDrawData } from '@bish/api/src/lottery';
import type { ComponentWithUserInfoLottery } from '@bish/api/src/activity';
import i18n from '@bish/lang/src';
import { useLog } from '../useLog';

export type InitExtraParams = {
  /**
   * 邀请码，分享时需要
   */
  inviteCode?: string
  /**
   * 邀请类型 1链接邀请 3海报分享邀请
   */
  inviteType?: number
}

export type LotterySchedule = ComponentWithUserInfoLottery[number] & {
  /**
   * 	抽奖类型： 1普通抽奖、2刮卡抽奖
   */
  lottery_type?: number
  /**
   * 用户集卡记录id
   */
  card_log_id?: number
}

export function useBoxLottery(lotteryId: number, needBindRole: number = 1) {
  const drawLoading = ref(false)
  const drawData = ref<LotteryPrizeDrawData | undefined>(undefined);
  // const drawData = ref<LotteryPrizeDrawData | undefined>({
  //   activity_id: 212,
  //   icon: "https://www.shiyue.com/storage/cdn/image/2024-08/e6e04521f2cb4de2e6e202e1ebeb4527.png",
  //   id: 816,
  //   lottery_config_id: 257,
  //   name: "6元欧泊",
  //   sort: 5,
  // });

  const activityStore = useActivityStore();

  const { uploadLog } = useLog();

  // 抽奖配置信息
  const lotteryConfig = computed(() => activityStore.activityInfo?.config?.lottery?.find((item => item.id === lotteryId)));

  // 抽奖状态
  const lotterySchedule = computed(
    () => activityStore.componentWithUserInfo?.component_with_user_info?.lottery?.find(item => item.id === lotteryId)
  );

  // 任务进度
  const process= computed(() => {
    return `${lotterySchedule.value?.lottery_all_num || 0}/${lotteryConfig.value?.ext_num_limit_all || 0}`;
  });

  const fetchDraw = (schedule?: LotterySchedule): Promise<boolean> => {
    return new Promise(async (resolve) => {
      // 事件上报--点击立即抽奖
      uploadLog({
        event_name: 'click',
        click_id: 9,
        click_type: 3,
      });

      // 公测判断
      const gameLifeCycle = activityStore.gameLifeCycle();
      if (gameLifeCycle === GAME_CIRCLE.PRELOADING_BEFORE || gameLifeCycle === GAME_CIRCLE.PRELOADING) {
        showToast(`公测后方可进行抽奖，游戏将在${activityStore.gameOpenTime}公测`);
        return;
      }

      const pass = activityStore._checkIn(needBindRole ? true : false);
      if (!pass) {
        resolve(false);
        return;
      }
      if (drawLoading.value) {
        return;
      }
      const mergedSchedule = schedule || lotterySchedule.value;
      if (!mergedSchedule || !mergedSchedule.id) {
        showToast(i18n.global.t('box-lottery-no-task', '缺少任务数据，请稍候重试~'));
        resolve(false);
        return;
      }
      // 没有抽奖次数
      if (!mergedSchedule?.lottery_remain_num) {
        showToast(i18n.global.t('box-lottery-no-chance', '没有抽奖次数~'));
        resolve(false);
        return;
      }
      drawLoading.value = true;
      try {
        const res = await postLotteryPrizeDraw({
          act_id: activityStore.activityInfo?.init?.id,
          act_acc_id: activityStore.activityAccountInfo?.act_acc_id,
          component_config_id: `${mergedSchedule.id}`,
          lottery_type: (mergedSchedule as LotterySchedule).card_log_id ? 2 : 1,
          card_log_id: (mergedSchedule as LotterySchedule).card_log_id,
        });
        resetDrawData();
        if (res.code === 0) {
          drawData.value = res.data;
          resolve(true);
        } else {
          resolve(false);
          fetchDrawEnd();
        }
      } catch (error) {
        fetchDrawEnd();
        console.warn('抽奖失败', error);
      }
    })
  };
  
  const fetchDrawEnd = () => {
    drawLoading.value = false;
    // 重新获取 用户与组件产生的信息
    activityStore.getComponentWithUserInfo();
  };

  const resetDrawData = () => {
    drawData.value = undefined;
  };

  return {
    fetchDraw,
    fetchDrawEnd,
    drawData,
    lotteryConfig,
    lotterySchedule,
    process,
    resetDrawData,
  }
}
