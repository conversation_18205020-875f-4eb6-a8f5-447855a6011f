import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'

const { VITE_ACTIVITY_URL } = useEnv()

export type MiniRemindAuthSaveParams = {
  /**
   * 活动id
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: number | string
  /**
   * 多个的情况下使用 , 拼接 后台管理者发出 4是预约通知 5是活动开始通知 6是公测上线通知
   */
  remind_template_id: string
}

/**
 * 订阅消息提醒保存
 * @param params MiniRemindAuthSaveParams
 * @returns 
 */
export function postMiniRemindAuthSave(params: MiniRemindAuthSaveParams) {
  return http.post(`${VITE_ACTIVITY_URL}/miniProgram/remind/authSave`, params)
}

export type MiniRemindStatusParams = {
  /**
   * 活动id
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: number | string
  /**
   * 多个的情况下使用 , 拼接 后台管理者发出 4是预约通知 5是活动开始通知 6是公测上线通知
   */
  remind_template_id: string
}

export type MiniRemindStatusData = {
  list: {
    /**
     * 订阅次数
     */
    auth_number: number
    /**
     * 4是预约通知 5是活动开始通知 6是公测上线通知
     */
    remind_template_id: number
  }[]
}

/**
 * 订阅状态
 * @param params MiniRemindStatusParams
 * @returns 
 */
export function postMiniRemindStatus(params: MiniRemindStatusParams) {
  return http.post<MiniRemindStatusData>(`${VITE_ACTIVITY_URL}/miniProgram/remind/status`, params)
}