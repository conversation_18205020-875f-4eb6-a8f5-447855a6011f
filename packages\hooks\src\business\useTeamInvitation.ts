import { onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { allowMultipleToast, showToast } from '@bish/ui/src/toast'
import useUserStore from '@bish/store/src/modules/user'
import useActivityStore from '@bish/store/src/modules/activity'
import { replaceQueryProperties } from '@bish/utils/src/utils'
import i18n from '@bish/lang/src';
import useControllableStatus from './useControllableStatus'

export function useTeamInvitation(
  teamId: number,
  ...args: Parameters<typeof useControllableStatus>
) {
  const showInvitationFlag = ref(true)

  const userStore = useUserStore()
  const activityStore = useActivityStore()

  const route = useRoute()
  const query = replaceQueryProperties(route)

  const [showInvitation, toggleInvitation] = useControllableStatus(...args);

  // 这里给个默认值，有些游戏内活动无法携带 invite_type
  const providedInviteType = query.invite_type || '1'
  const isInvited = () => query.team_id && query.invite_code && providedInviteType

  /**
   * 是否是邀请人本身
   */
  const isInviter = () => {
    const inviteCode = query.invite_code ? decodeURIComponent(query.invite_code as string) : ''
    return activityStore.activityAccountInfo?.invite_code && activityStore.activityAccountInfo.invite_code === inviteCode
  }
  /**
   * 初始化显示邀请组队弹窗
   * @param delayed 是否即刻执行的
   * @returns
   */
  const initShowInvitation = (delayed: boolean) => {
    if (isInvited()) {
      // 未登录，直接显示弹窗
      if (!delayed && !userStore.userData.token) {
        toggleInvitation(true)
        showInvitationFlag.value = false
        return
      }
      allowMultipleToast()
      // 已登录，自己不能邀请自己
      if (query.invite_code && userStore.userData.token && isInviter()) {
        showToast(i18n.global.t('box-inviteFriend-cantInviteSelf', '不能邀请自己'))
        return
      }
      // 如果没有队伍，则显示邀请组队弹窗
      const { component_with_user_info } = activityStore.componentWithUserInfo
      const team = component_with_user_info?.team?.find((item: any) => item.id === teamId)
      if (delayed && showInvitationFlag.value) {
        if (!team?.team_id) {
          toggleInvitation(true)
        } else {
          allowMultipleToast()
          showToast(i18n.global.t('box-invite-friend-already', '已经组队'))
          toggleInvitation(false)
        }
        showInvitationFlag.value = false
        return
      }
    }
  }

  // const handleAcceptModalClose = () => {
  //   showInvitationFlag.value = true
  // }

  onMounted(() => {
    // 未登录状态，弹出邀请窗口
    initShowInvitation(false)
  })

  let lastUserTeam: undefined | number = 0
  watch(
    () => activityStore.componentWithUserInfo,
    (newVal) => {
      if (newVal && JSON.stringify(newVal) !== '{}') {
        const { component_with_user_info } = newVal
        const userTeam = component_with_user_info?.team?.find((item: any) => item.id === teamId)
        // 没有队伍，或者存在队伍
        if (userTeam?.team_id !== lastUserTeam) { 
          lastUserTeam = userTeam?.team_id
          if (newVal && JSON.stringify(newVal) !== '{}') {
            initShowInvitation(true)
          }
        }
      }
    }
  )

  return {
    showInvitation,
    toggleInvitation,
  }
}
