import { ref, computed } from 'vue';
import type { Ref } from 'vue';
import type { ActivityInfoConfigLottery } from '@bish/api/src/activity';
import type { LotteryPrizeDrawData } from '@bish/api/src/lottery';

export type LotteryPrizes = ActivityInfoConfigLottery[0]['lottery_prize']

export default function useLotteryAnimation(
  box: number[],
  track: number[],
  lotteryPrizes: Ref<LotteryPrizes>,
  prize: Ref<LotteryPrizeDrawData | undefined>,
  onDrawEnd: () => void,
) {
  // 当前激活的 box
  const activeIndex = ref(-1);
  // 锁
  const drawLock = ref(false);
  /**
   * 时间间隔
   */
  const INTERVAL = 30;
  /**
   * 匀速时间
   */
  const CONSTANT_SPEED_TIME = 80;
  /**
   * 时间临界值
   */
  const THRESHOLD = 300;

  // 计算角度数组
  const anglePerSlice = 360 / track.length;
  const angleArray = track.map((_, index) => index * anglePerSlice);


  // 按钮旋转角度
  const buttonRotation = computed(() => {
    return angleArray[activeIndex.value] || 0;
  });

  let timer1: number | null = null;
  let time1 = THRESHOLD; // 初始时间间隔长度
  /**
   * 从慢到快
   */
  const start = () => {
    if (drawLock.value) {
      return;
    }
    drawLock.value = true;
    // 重置数据
    time1 = 300;
    // activeIndex.value = -1;
    // buttonRotation.value = 0;

    const loop = () => {
      timer1 = setTimeout(() => {
        if (time1 > CONSTANT_SPEED_TIME) {
          // 更新激活索引
          activeIndex.value = activeIndex.value < track.length - 1 ? activeIndex.value + 1 : 0;

          time1 -= INTERVAL;
          loop();
        } else {
          // 结束加速运动
          if (timer1) {
            clearTimeout(timer1);
            timer1 = null;
          }
          // 进入匀速运动
          uniformMotion();
        }
      }, time1);
    };
    loop();
  };

  let timer2: number | null = null;
  let timeCount2 = 0;
  /**
   * 匀速运动
   */
  const uniformMotion = () => {
    // 重置数据
    timeCount2 = 0;

    const loop = () => {
      timeCount2 += CONSTANT_SPEED_TIME;

      // 1.5秒后结束匀速运动
      if (timeCount2 >= 1500 && timer2) {
        clearTimeout(timer2);
        timer2 = null;
        // 进入减速运动
        end();
        return;
      }
      
      // 更新激活索引
      activeIndex.value = activeIndex.value < track.length - 1 ? activeIndex.value + 1 : 0;

      timer2 = setTimeout(() => {
        loop();
      }, CONSTANT_SPEED_TIME);
    };
    loop();
  };

  let timer3: number | null = null;
  let time3 = CONSTANT_SPEED_TIME; // 初始时间间隔长度
  /**
   * 减速运动
   */
  const end = () => {
    // 重置数据
    time3 = CONSTANT_SPEED_TIME;

    const loop = () => {
      timer3 = setTimeout(() => {
        // 到达临界时间
        if (time3 > THRESHOLD - 120) {
          // 结束运动
          if (timer3) {
            // 这里当达到临界时间之后，再运动到中奖奖品位置
            const prizeIndex = lotteryPrizes.value?.findIndex((item) => item.id === prize.value?.id) ?? -1;
            const trackIndex = track.findIndex((item) => item === box[prizeIndex]);

            // 更新激活索引
            activeIndex.value = activeIndex.value < track.length - 1 ? activeIndex.value + 1 : 0;
            

            if (activeIndex.value === trackIndex) {
              clearTimeout(timer3);
              timer3 = null;
              drawLock.value = false;

              // 派发抽奖结束事件到外部
              setTimeout(() => {
                onDrawEnd();
              }, 500);
              return;
            }
            loop();
          }
        } else {
          // 更新激活索引
          activeIndex.value = activeIndex.value < track.length - 1 ? activeIndex.value + 1 : 0;

          time3 += INTERVAL * 2;
          loop();
        }
      }, time3);
    };
    loop();
  };

  // 清理所有定时器
  const cleanupTimers = () => {
    if (timer1) {
      clearTimeout(timer1);
      timer1 = null;
    }
    if (timer2) {
      clearTimeout(timer2);
      timer2 = null;
    }
    if (timer3) {
      clearTimeout(timer3);
      timer3 = null;
    }
  };

  return {
    activeIndex,
    buttonRotation,
    start,
    cleanupTimers,
  };
}