<template>
  <Resizable
    class="teammate"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <div class="teammate-list">
      <ResizableProvider
        v-for="(item, index) in members"
        :key="index"
        class="teammate-item"
        @click="() => handleAdd(item)"
      >
        <Resizable
          v-model:x="setting.item.x"
          v-model:y="setting.item.y"
          v-model:width="setting.item.width"
          v-model:height="setting.item.height"
          :movable="false"
          class="teammate-item-slot"
          :style="{
            backgroundImage: `url(${currentItem(index).bgImage || setting.item.bgImage})`,
          }"
        >
          <Avatar
            v-if="item.account_id"
            :width="setting.item.width"
            :height="setting.item.height"
            :career="item.career"
            :sex="item.sex"
            :default-avatar="currentItem(index).defaultAvatar || setting.item.defaultAvatar"
          />
        </Resizable>
        <!-- 队友名称 -->
        <UiText
          v-model:setting="setting.item.name"
          :confined="false"
          :scrollspy="false"
          :editableContent="false"
          class="teammate-item-wrapper"
        >
          <div v-if="providedShowServerName && item.server_name" class="teammate-item-name">
            {{ item.server_name || '' }}
          </div>
          <div v-if="item.role_name" class="teammate-item-name">
            {{ item.role_name || '' }}
          </div>
        </UiText>
      </ResizableProvider>
    </div>
  </Resizable>
</template>

<script lang="ts">
import type { CommonBackgroundSetting } from '@bish/types/src'
import type { ComponentWithUserInfoMembers } from '@bish/api/src/activity';
import type { TextSetting } from '../../ui/text/index.vue';

export interface TeammateSetting extends CommonBackgroundSetting {
  /**
   * 队友位默认配置
   */
  item: CommonBackgroundSetting & {
    /**
     * 默认头像
     */
    defaultAvatar: string
    /**
     * 名称配置
     */
    name: TextSetting
    /**
     * 是否显示区服，默认 true
     */
    showServerName: boolean
  }
  /**
   * 队伍人数，默认 5
   */
  teamNum: number
  /**
   * 队友位配置列表
   */
  items: {
    /**
     * 背景图片
     */
    bgImage: string
    /**
     * 默认头像
     */
    defaultAvatar: string
  }[]
}

export type Member = ComponentWithUserInfoMembers[0]

export interface TeammateProps {
  /**
   * 配置
   */
  setting: TeammateSetting
  /**
   * 队员数据
   */
  members: Member[]
}

const defaultItem = () => ({
  bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719402664649_.png',
  defaultAvatar: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-6/1722926730553_.png',
});

export const defaultConfig = (): TeammateSetting => {
  return {
    x: 44,
    y: 64,
    width: 238,
    height: 74,
    bgImage: '',
    item: {
      x: 0,
      y: 0,
      width: 38,
      height: 38,
      name: {
        x: -7,
        y: 44,
        width: 50,
        height: 28,
        fontSize: 10,
        color: '#F4E289',
        align: 'center',
      },
      showServerName: true,
      ...defaultItem(),
    },
    teamNum: 5,
    items: [
      defaultItem(),
      defaultItem(),
      defaultItem(),
      defaultItem(),
      defaultItem(),
    ],
  };
};

export default {
  name: 'teammate'
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, computed, defineExpose } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useAdminStore from '@bish/store/src/modules/admin';
import Resizable from '../../ui/resizable/index.vue';
import ResizableProvider from '../../ui/resizable/resizable-provider.vue';
import UiText from '../../ui/text/index.vue';
import Avatar from '../../common/avatar/index.vue'

const props = withDefaults(defineProps<TeammateProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TeammateSetting): void;
  (event: 'add', item?: Member): void;
}>();

const adminStore = useAdminStore();

const providedShowServerName = computed(() => {
  return props.setting.item.showServerName ?? true;
});

/**
 * 添加队友
 * @param item 
 */
const handleAdd = (item?: Member) => {
  if (adminStore.editable) {
    return;
  }
  if (item?.account_id) {
    return;
  }
  emits('add', item);
};

const currentItem = (index: number) => {
  return props.setting?.items?.[index] || {};
};
</script>

<style lang="less">
.teammate {
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-list {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-around;
  }

  &-item {
    position: relative;

    &-slot {
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    &-wrapper {
      :deep(.ui-text > div) {
        overflow: hidden;
      }
    }

    &-name {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      word-break: break-all;
    }
  }
}
</style>