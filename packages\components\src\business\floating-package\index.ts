import type { FloatingPackageSetting } from './floating-package';
import { defaultConfig as serverRolePickerDefaultConfig } from '../server-role-picker.vue';

export * from './floating-package';

export const defaultConfig = (): FloatingPackageSetting => {
  const serverRolePickerConfig = serverRolePickerDefaultConfig();
  return {
    x: 0,
    y: 0,
    width: 94,
    height: 82,
    bgImage: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/********/*************-package-e.png',
    taskId: 0,
    accountStatus: 0,
    affix: 1,
    modal: {
      x: 0,
      y: 0,
      width: 374,
      height: 407,
      bgImage: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/********/*************-package-bg.png',
      closeBtn: {
        x: 336,
        y: -2,
        width: 27,
        height: 25,
        imgLink: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/********/*************-x.png',
      },
      okBtn: {
        x: 80,
        y: 305,
        width: 214,
        height: 33,
        imgLink: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/********/*************-package-btn.png',
      },
      tip: {
        x: 37,
        y: 102,
        width: 300,
        height: 20,
        fontSize: 10,
        color: '#B45641',
        align: 'center',
        content: '回流用户玩新服，即送回流礼包!',
        enabled: true,
      },
      item: {
        x: 124,
        y: 126,
        width: 120,
        height: 120,
        imgLink: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/********/1742183481751-iphone.png',
        enabled: true,
      },
      serverRolePicker: {
        ...serverRolePickerConfig,
        selector: {
          ...serverRolePickerConfig.selector,
          x: 34,
          y: 261,
          color: '#B45641',
          fontSize: 10,
          switchColor: '#1C9BD0',
        },
      },
    },
  };
}; 
