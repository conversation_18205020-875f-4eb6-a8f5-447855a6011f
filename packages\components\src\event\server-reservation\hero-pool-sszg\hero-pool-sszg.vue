<!--
* @Description: 闪烁之光-英雄池
-->
<template>
  <Resizable
    class="hero-pool-sszg"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
    @click="handleOpen"
  >
    <Popup
      z-index="99"
      :show="show"
      :resizable="false"
      v-model:setting="setting.modalSetting"
      @close="handleClose"
    >
      <!-- 列表 -->
      <Resizable
        v-model:x="setting.modalSetting.list.x"
        v-model:y="setting.modalSetting.list.y"
        v-model:width="setting.modalSetting.list.width"
        v-model:height="setting.modalSetting.list.height"
        class="hero-pool-sszg-list"
      >
        <Resizable
          v-for="item in items"
          :key="item.id"
          v-model:x="setting.modalSetting.item.x"
          v-model:y="setting.modalSetting.item.y"
          v-model:width="setting.modalSetting.item.width"
          v-model:height="setting.modalSetting.item.height"
          :movable="false"
          class="hero-pool-sszg-list-item"
        >
          <div class="hero-pool-sszg-list-item-box">
            <img
              class="hero-pool-sszg-list-item-img"
              :src="`${baseImgUrl}/hero/${item.id}.png`"
            />
            <img
              :style="itemXlStyle"
              class="hero-pool-sszg-list-item-xl"
              :src="`${baseImgUrl}/xl/${item.xl}.png`"
            />
            <div class="hero-pool-sszg-list-item-xxs">
              <img
                v-for="star in item.xx"
                :key="star"
                :style="itemXXStyle"
                :src="`${baseImgUrl}/star.png`"
              />
            </div>
          </div>
          <div :style="itemGlStyle" class="hero-pool-sszg-list-item-gl">
            {{ item.gl }}%
          </div>
        </Resizable>
      </Resizable>
    </Popup>
  </Resizable>
</template>

<script lang="ts">
import type { StatefulComponent } from '@bish/types/src/admin';

export interface HeroPoolSszgProps {
  /**
   * 配置
   */
  setting: HeroPoolSszgSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export default {
  name: 'hero-pool-sszg',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { pxTransform } from '@bish/utils/src/viewport';
import useAdminStore from '@bish/store/src/modules/admin';
import Popup from '../../../common/popup.vue';
import type { HeroPoolSszgSetting } from './hero-pool-sszg';
import Resizable from '../../..//ui/resizable/index.vue';
import { defaultConfig } from './index';
import { hero } from './config';
import type { HeroItem } from './config';

type ItemsType = (HeroItem & { gl: number })[]

const baseImgUrl = `https://pingtai-img.shiyue.com//cdn-miniprogram-member-static/sszg`;

const props = withDefaults(defineProps<HeroPoolSszgProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: HeroPoolSszgSetting): void;
  (event: 'close'): void;
}>();

const setting = useVModel(props, 'setting', emits);

const adminStore = useAdminStore();

const [show, toggleShow] = useControllableStatus(props, emits, { fieldName: 'showHeroPoolSszgModal' });

const itemGlStyle = computed<CSSProperties>(() => {
  return {
    fontSize: pxTransform(12),
    paddingTop: pxTransform(2),
    paddingBottom: pxTransform(2),
  }
});

const itemXlStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(18),
    height: pxTransform(18),
  }
});

const itemXXStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(12),
    height: pxTransform(12),
    marginLeft: pxTransform(-4),
  }
});

const items = computed(() => {
  const ret: ItemsType = [];
  for (let i = 0; i < hero.length; i++) {
    const heroItem = hero[i];
    if (heroItem.items?.length) {
      for (let j = 0; j < heroItem.items.length; j++) {
        const item = heroItem.items[j];
        ret.push({
          ...item,
          gl: heroItem.gl,
        });
      }
    }
  }
  return ret;
});


const handleOpen = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }
  
  toggleShow(true);
};

const handleClose = () => {
  toggleShow(false);
};
</script>

<style>
.hero-pool-sszg {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.hero-pool-sszg-list {
  display: inline-flex;
  flex-wrap: wrap;
  justify-content: space-around;
  overflow-y: scroll;
}

.hero-pool-sszg-list-item {
  position: relative;
}

.hero-pool-sszg-list-item-box {
  position: relative;
  padding-bottom: 100%;
  border-radius: 8px 4px 4px 4px;
  overflow: hidden;
  box-sizing: border-box;
}

.hero-pool-sszg-list-item-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid #DA8641;
  box-sizing: border-box;
}

.hero-pool-sszg-list-item-gl {
  color: #C39757;
  text-align: center;
  line-height: 1;
}

.hero-pool-sszg-list-item-xl {
  position: absolute;
  top: 0;
  left: 0;
}

.hero-pool-sszg-list-item-xxs {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
}
</style>
