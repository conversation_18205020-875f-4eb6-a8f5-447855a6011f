import { commonTaskConfig } from '../index';
import type { TaskDailySharingSetting } from './daily-sharing';
import { defaultConfig as defaultShareGuideConfig } from '../../../share-guide';

export * from './daily-sharing';

export const defaultConfig = (): TaskDailySharingSetting => {
  return {
    ...commonTaskConfig(),
    process: {
      x: 265,
      y: 12,
      width: 20,
      height: 20,
      fontSize: 9,
      color: '#684975',
      content: '{{process}}',
    },
    width: 290,
    height: 34,
    taskId: 0,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730982663132-%E4%BB%BB%E5%8A%A13.png',
    claimBtn: {
      x: 197,
      y: 6,
      width: 61,
      height: 22,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730982666553-BUTTON%E5%8E%BB%E5%88%86%E4%BA%AB.png',
    },
    guideModal: {
      x: 0,
      y: 0,
      width: 375,
      height: 364,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730944699167-%E7%BB%84%2033.png',
      closeBtn: {
        x: 327,
        y: 5,
        width: 27,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730944693773-%E5%85%B3%E9%97%AD%E6%8C%89%E9%92%AE%20%E6%8B%B7%E8%B4%9D%203.png',
      },
    },
    needLogin: 1,
    shareGuide: defaultShareGuideConfig(),
  }
};