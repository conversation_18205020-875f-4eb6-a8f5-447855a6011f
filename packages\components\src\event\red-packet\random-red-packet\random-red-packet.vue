<!--
* @Description: 随机红包
* @issues: 关于 van-rolling-text 不支持小数点的问题：https://github.com/youzan/vant/issues/12477
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :position="setting.position"
    class="random-red-packet"
  >
    <ResizableProvider>
      <!-- 背景图 -->
      <UiImage
        v-if="setting.bgImage.enabled"
        v-model:setting="setting.bgImage"
      />
      <!-- 红包未打开 -->
      <template v-if="!openRedPacket">
        <!-- 红包 -->
        <UiImage
          v-model:setting="setting.redPacket"
        />
        <!-- 打开按钮 -->
        <UiImage
          v-model:setting="setting.openBtn"
          class="random-red-packet-open"
          @click="handleClaim"
        />
      </template>
      <!-- 红包打开 -->
      <template v-else>
        <!-- 红包打开 -->
        <UiImage
          v-model:setting="setting.redPacketOpened"
        />
        <!-- 红包金额 -->
        <UiText
          v-model:setting="setting.amount"
          :scrollspy="false"
        >
          <div class="random-red-packet-amount-wrapper">
            <!-- 整数部分 -->
            <van-rolling-text
              ref="integerPartRef"
              class="my-rolling-text"
              direction="up"
              stop-order="rtl"
              :height="setting.amount.height"
              :auto-start="false"
              :start-num="integerPart"
              :target-num="integerPart"
            />
            <!-- 小数点 -->
            <span>.</span>
            <!-- 小数部分 - 使用文本列表 -->
            <van-rolling-text
              ref="decimalPartRef"
              class="my-rolling-text"
              direction="up"
              stop-order="rtl"
              :height="setting.amount.height"
              :auto-start="false"
              :text-list="decimalTextList"
              :duration="0.5"
            />
            <span class="random-red-packet-amount-unit">元</span>
          </div>
        </UiText>
        <!-- 暴击红包 -->
        <UiImage
          v-if="setting.hitBtn?.enabled && showHitBtn"
          v-model:setting="setting.hitBtn"
          class="random-red-packet-hit"
          :class="{
            'random-red-packet-hit-open': userTaskHit?.status === 2,
            'random-red-packet-hit-disabled': userTaskHit?.status === 3,
          }"
          @click="handleHit"
        />
      </template>
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'random-red-packet',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { computed, nextTick, ref, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import type { RollingTextInstance  } from 'vant';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import type { StatefulComponent } from '@bish/types/src/admin';
import { showToast } from '@bish/ui/src/toast';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { postActivityTaskGetAwardTask } from '@bish/api/src/activity';
import type { ActivityInfoConfigTask, ComponentWithUserInfoTask } from '@bish/api/src/activity';
import { useLog } from '@bish/hooks/src/useLog';
import UiImage from '../../../ui/image/index.vue';
import UiText from '../../../ui/text/index.vue';
import Resizable from '../../..//ui/resizable/index.vue';
import ResizableProvider from '../../..//ui/resizable/resizable-provider.vue';
import { defaultConfig } from './index';
import type { RandomRedPacketSetting } from './random-red-packet';

export interface RandomRedPacketProps {
  /**
   * 配置
   */
  setting: RandomRedPacketSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<RandomRedPacketProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: RandomRedPacketSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityStore = useActivityStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const [openRedPacket, toggleRedPacket] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'openRedPacket' });

const { uploadLog } = useLog();

const integerPartRef = ref<RollingTextInstance>();
const decimalPartRef = ref<RollingTextInstance>();

// 添加一个标志来控制是否应该执行动画
const shouldAnimate = ref(false);

// 初始小数部分值
const initialDecimalValue = ref('00');
// 目标小数部分值
const targetDecimalValue = ref('00');

/**
 * 基础红包任务配置
 */
const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

/**
 * 用户基础红包任务信息
 */
const userTask = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
});

/**
 * 暴击红包任务配置
 */
const taskConfigHit = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskIdHit);
});

/**
 * 用户暴击红包任务信息
 */
const userTaskHit = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskIdHit);
});

// 用户累计获得金额
const totalAmount = computed(() => {
  const res = activityStore.componentWithUserInfo?.component_with_user_info?.red_envelope?.total_amount || 0;
  return Number(res) || 0;
});

// 是否已提现
const hasRecord = computed(() => {
  return activityStore.componentWithUserInfo.component_with_user_info?.red_envelope?.draw_list?.length > 0;
});

/**
 * 是否显示暴击按钮
 */
const showHitBtn = computed(() => {
  return adminStore.editable || activityStore.activityAccountInfo?.is_promote_earnings !== 1;
});

watch(
  userTask,
  () => {
    if (userTask.value && userTask.value?.status === 3) {
      toggleRedPacket(true);
    }
  },
  { immediate: true },
);

/**
 * 是否有奖品的
 */
const hasPrize = (tConfig?: ActivityInfoConfigTask[0]) => {
  if (!tConfig) {
    return false;
  }
  const rewardType = tConfig.reward_type;
  // 虚拟道具
  if (rewardType === 1) {
    // 没有配置道具id，不需要领取任务奖励
    return !!tConfig.item_id
  }
  return true;
};

// 计算整数部分
const integerPart = computed(() => {
  return Math.floor(totalAmount.value);
});

// 计算小数部分的文本列表
const decimalTextList = computed(() => {
  // 使用 toFixed(2) 确保保留两位小数，然后提取小数部分
  const decimalStr = totalAmount.value.toFixed(2).split('.')[1];
  
  // 如果是动画状态，返回包含初始值和目标值的数组
  if (shouldAnimate.value) {
    return [initialDecimalValue.value, decimalStr];
  }
  // 非动画状态，只返回当前值
  initialDecimalValue.value = decimalStr;
  return [decimalStr];
});

// 修改 totalAmount 的监听器
watch(
  () => totalAmount.value,
  async (newVal, oldVal) => {
    if (newVal !== oldVal && shouldAnimate.value) {
      // 等待DOM更新
      await nextTick();
      // 启动滚动动画
      integerPartRef.value?.start();
      decimalPartRef.value?.start();
      
      // 重置标志，以便下次需要手动触发
      // 这里需要延迟2秒，因为动画结束之后，totalAmount 会立即更新，导致动画无法触发
      setTimeout(() => {
        shouldAnimate.value = false;
      }, 2000);
    }
  }
);

/**
 * 领取基础红包任务奖励
 */
const claimTaskReward = async (
  taskConfig: ActivityInfoConfigTask[0],
  userTask: ComponentWithUserInfoTask[0],
  taskLogId: number,
  selectType: number,
) => {
  try {
    // 判断任务是否是已完成状态 & 任务配置了商品id
    // 没有商品id的任务，有可能是为抽奖服务的，这个是不需要领取道具奖励
    if (userTask.status === 2 && !hasPrize(taskConfig)) {
      return;
    }
    const res = await postActivityTaskGetAwardTask(
      {
        act_id: activityStore.activityInfo.init?.id,
        act_acc_id: activityStore.activityAccountInfo.act_acc_id,
        // 这里处理一下月度礼包，本月如果有领取的话，那么到下个月 taskLogId 是不会进行重置成 0 的
        // 只有状态会重置成 1 进行中（未完成）
        // 所以当状态是 1 的时候，taskLogId 传 0
        task_log_id: taskLogId,
        select_type: selectType, // 1游戏道具 2红包 3集卡 4召回红包 5暴击红包
      },
    );
    if (res.code === 0) {
      setTimeout(async () => {
        // 总是将初始值设为 "00"，确保动画能够触发
        initialDecimalValue.value = "00";
        
        // 设置标志，表示应该执行动画
        shouldAnimate.value = true;
        
        // 重新获取用户与组件的数据
        await activityStore.getComponentWithUserInfo();
        
        // 注意：这里不需要手动调用 start()，
        // 因为 getComponentWithUserInfo 会更新 totalAmount，
        // 从而触发 watch，watch 会检查 shouldAnimate 并执行动画
      }, 500);
    } else {
      activityStore.getComponentWithUserInfo();
    }
  } catch (error) {
    console.warn('领取红包失败', error);
  }
};

const handleClaim = async () => {
  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfig.value) {
    showToast('活动已结束~');
    return;
  }
  // 事件上报：点击领取随机红包
  uploadLog({
    event_name: 'click',
    click_id: 147,
    click_type: 3,
  });
  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  if (!userTask.value) {
    return;
  }
  claimTaskReward(
    taskConfig.value,
    userTask.value,
    userTask.value.status === 1
      ? 0
      : userTask.value.task_log_id,
    4,
  );
};

/**
 * 暴击红包
 */
const handleHit = async () => {
  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfigHit.value) {
    showToast('活动已结束~');
    return;
  }
  // 事件上报：点击领我要暴击红包
  uploadLog({
    event_name: 'click',
    click_id: 149,
    click_type: 3,
  });
  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  if (!userTaskHit.value) {
    return;
  }

  // 暴击有效期判断
  const activityEndTime = activityStore.activityInfo.init?.activity_end_time || 0;
  const withdrawEndTime = activityStore.componentWithUserInfo.component_with_user_info?.red_envelope?.withdraw_end_time || 0;
  const now = Date.now() / 1000;

  // 处理自然日比较
  // 将时间戳转换为日期对象
  const withdrawEndDate = new Date(withdrawEndTime * 1000);
  const activityEndDate = new Date(activityEndTime * 1000);
  
  // 将日期重置为当天的23:59:59，只比较日期部分
  const withdrawEndDay = new Date(
    withdrawEndDate.getFullYear(), 
    withdrawEndDate.getMonth(), 
    withdrawEndDate.getDate(),
    23, 59, 59
  ).getTime() / 1000;
  
  // 活动时间不需要重置
  const activityEndDay = new Date(activityEndDate).getTime() / 1000;

  // 活动已结束
  if (now > activityEndDay) {
    showToast('活动已结束');
    return;
  }

  // 已过暴击时间
  if (now > withdrawEndDay) {
    showToast('很遗憾，已过了暴击时间');
    return;
  }

  const { hitTips } = setting.value;
  // 未达到暴击条件
  if (userTaskHit?.value?.status === 1) {
    if (hitTips) {
      showToast(hitTips);
    }
    return;
  }
  // 不暴击，进行过提现
  if (
    activityStore.activityAccountInfo.is_promote_earnings === 1
    && hasRecord.value
  ) {
    showToast('已提现，无法再进行暴击');
    return;
  }
  // 已领取暴击红包
  if (userTaskHit?.value?.status === 3) {
    showToast('已成功暴击');
    return;
  }
  claimTaskReward(
    taskConfigHit.value,
    userTaskHit.value,
    userTaskHit.value.status === 1
      ? 0
      : userTaskHit.value.task_log_id,
    5
  );
};
</script>

<style>
.random-red-packet {}

.random-red-packet-open {
  animation: heartbeat-pulse 1.2s infinite;
}

.random-red-packet-hit {}

.random-red-packet-hit-open {
  animation: heartbeat-pulse 1.2s infinite;
}

.random-red-packet-hit-disabled {
  filter: grayscale(100%);
}

.random-red-packet .van-rolling-text {
  font-size: inherit;
  color: inherit;
}

.random-red-packet .van-rolling-text-item {
  width: auto;
}

.random-red-packet .random-red-packet-amount-unit {
  font-size: 0.65em;
  position: relative;
  top: -0.1em;
}

@keyframes heartbeat-pulse {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.15);
  }
  35% {
    transform: scale(1.05);
  }
  45% {
    transform: scale(1.12);
  }
  55% {
    transform: scale(1);
  }
  65% {
    transform: scale(1.08);
  }
  75% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}
</style>

