export function useEnv() {
  const {
    VITE_BASE_URL,
    VITE_BASE_SIGN_KEY,
    VITE_PUBLIC_PATH,
    MODE,
    VITE_SY_LOGIN,
    VITE_LOGIN_API,
    VITE_ACTIVITY_URL,
    VITE_ACTIVITY_SIGN_KEY,
    VITE_SDK_URL,
    VITE_SDK_SIGN_KEY,
    VITE_COMMON_URL,
    VITE_COMMON_SIGN_KEY,
    VITE_PAY_URL,
    VITE_ENV,
    VITE_LOG_URL,
    VITE_LOG_SECRET,
    VITE_LOG_KEY,
    VITE_PAY_SIGN_KEY,
    VITE_SHOP_URL,
    VITE_INTEGRAL_ShOP_URL,
    VITE_MALL_URL,
    VITE_MALL_SECRET,
    VITE_SHOP_ID,
    VITE_WECHAT_URL,
    VITE_WECHAT_SECRET,
    VITE_SHORT_URL,
    VITE_BBS_H5_URL,
    VITE_Y_CREATER_URL,
    VITE_AD_POOL_URL,
    VITE_APP_TYPE,
  } = import.meta.env
  // 如果名字变换了，我们可以在这里解构别名
  return {
    MODE,
    VITE_BASE_URL,
    VITE_BASE_SIGN_KEY,
    VITE_PUBLIC_PATH,
    VITE_SY_LOGIN,
    VITE_LOGIN_API,
    VITE_ACTIVITY_URL,
    VITE_ACTIVITY_SIGN_KEY,
    VITE_SDK_URL,
    VITE_SDK_SIGN_KEY,
    VITE_COMMON_URL,
    VITE_COMMON_SIGN_KEY,
    VITE_PAY_URL,
    VITE_ENV,
    VITE_LOG_URL,
    VITE_LOG_SECRET,
    VITE_LOG_KEY,
    VITE_PAY_SIGN_KEY,
    VITE_SHOP_URL,
    VITE_INTEGRAL_ShOP_URL,
    VITE_MALL_URL,
    VITE_MALL_SECRET,
    VITE_SHOP_ID,
    VITE_WECHAT_URL,
    VITE_WECHAT_SECRET,
    VITE_SHORT_URL,
    VITE_BBS_H5_URL,
    VITE_Y_CREATER_URL,
    VITE_AD_POOL_URL,
    VITE_APP_TYPE,
  }
}
