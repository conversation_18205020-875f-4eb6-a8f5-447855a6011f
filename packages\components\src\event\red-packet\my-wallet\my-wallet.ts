import type { CommonBackgroundSetting } from '@bish/types/src';
import type { ImageSetting } from '../../../ui/image/index.vue';
import type { PopupSetting } from '../../../common/popup.vue';
import type { TextSetting } from '../../../ui/text/index.vue';

// 定义组件实例类型
export interface MyWalletInstance {
  /**
   * 打开钱包弹窗
   * @returns {boolean} 是否成功打开
   */
  open: () => boolean;
}

export type AmountSetting = {
  /**
   * 列表配置
   */
  list: CommonBackgroundSetting
  /**
   * 列表项配置
   */
  item: CommonBackgroundSetting
  /**
   * 列表项金额配置
   */
  itemNum: TextSetting
  /**
   * 列表项
   */
  items: {
    /**
     * 目标金额
     */
    num: number
  }[]
}

export type AllSetting = {
  /**
   * 金额配置
   */
  num: TextSetting
  /**
   * 提现按钮
   */
  actionBtn: ImageSetting
  /**
   * 单笔做大提现金额
   */
  max: number
}

export interface MyWalletSetting extends CommonBackgroundSetting {
  /**
   * 是否固定在页面，1是 0否，默认 1，设置后将使用 fixed 布局
   */
  affix: number
  /**
   * 弹窗配置
   */
  modal: PopupSetting & {
    /**
     * 余额配置
     */
    balance: CommonBackgroundSetting & {
      /**
       * 余额数
       */
      num: TextSetting
    }
    /**
     * 金额提现方式，1 列表 2 全部提现
     */
    actionMode: number
    /**
     * 提现金额配置
     */
    amount?: AmountSetting
    /**
     * 全部提现
     */
    all?: AllSetting
    /**
     * 提现记录
     */
    records: CommonBackgroundSetting
    /**
     * 刷新按钮
     */
    refreshBtn: ImageSetting
  }
  /**
   * 总是显示, 1是 0否，默认 1
   */
  alwaysShow: number
}