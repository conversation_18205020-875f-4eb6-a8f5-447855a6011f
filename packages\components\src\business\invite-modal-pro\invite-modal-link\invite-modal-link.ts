import type { TextSetting } from '../../../ui/text/index.vue';
import type { ImageSetting } from '../../../ui/image/index.vue';
import type { PopupSetting } from '../../../common/popup.vue';  

export interface InviteModalLinkSetting extends PopupSetting {
  /**
   * 邀请文案
   */
  content?: TextSetting
  /**
   * 链接文本
   */
  linkText?: TextSetting
  /**
   * 复制链接按钮
   */
  copyLinkBtn?: ImageSetting
  /**
   * 复制模板列表
   */
  copyTemplates: {
    text: string
    /**
     * 海报，目前只有小程序页面分享才用到
     */
    poster?: string
  }[]
  /**
   * 创建海报按钮
   */
  createPosterBtn?: ImageSetting
}