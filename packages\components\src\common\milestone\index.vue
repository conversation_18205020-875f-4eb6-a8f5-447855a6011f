<!--
* @Description: 里程碑，一般用于预约进度、邀请进度
-->
<template>
  <Resizable
    class="milestone"
    v-model:x="vSetting.x"
    v-model:y="vSetting.y"
    v-model:width="vSetting.width"
    v-model:height="vSetting.height"
    :style="{
      backgroundImage: `url(${vSetting.bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 进度条 -->
      <Resizable
        class="milestone-progress"
        v-model:x="vSetting.progress.x"
        v-model:y="vSetting.progress.y"
        v-model:width="vSetting.progress.width"
        v-model:height="vSetting.progress.height"
        :baseline="false"
      >
        <div class="milestone-progress-outer">
          <!-- 进度条背景 -->
          <div
            class="milestone-progress-trail"
            :style="{
              backgroundImage: `url(${vSetting.progress.trailBgImage})`,
            }"
          />
          <div
            class="milestone-progress-inner"
            :style="{
              width: strokeWidth,
            }"
          >
            <!-- 当前进度条 -->
            <div
              class="milestone-progress-stroke-bg"
              :style="{
                backgroundImage: `url(${vSetting.progress.strokeBgImage})`,
              }"
            />
          </div>
          <!-- 标记 -->
          <div v-if="Object.keys(marks).length > 0" class="milestone-progress-mark">
            <div
              v-for="(value, key) in marks"
              :key="key"
              class="milestone-progress-mark-item"
              :style="(
                !Array.isArray(marks)
                  ? {
                    left: `${key}%`,
                    top: '50%',
                    transform: `translate(-50%, -50%)`,
                  }
                  : markArrItemStyle
              )"
            >
              <!-- 节点 -->
              <div class="milestone-progress-dot">
                <template v-if="isActive(key)">
                  <!-- 激活节点 -->
                  <UiImage
                    v-if="vSetting.progress.dotActive.imgLink"
                    v-model:setting="vSetting.progress.dotActive"
                    :movable="false"
                  />
                </template>
                <template v-else>
                  <!-- 默认节点 -->
                  <UiImage
                    v-if="vSetting.progress.dot.imgLink"
                    v-model:setting="vSetting.progress.dot"
                    :movable="false"
                  />
                </template>
              </div>
              <slot name="mark" :item="value" :isActive="isActive(key)" :key="key" />
            </div>
          </div>
        </div>
      </Resizable>
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
import type { ResizableProps } from '../../ui/resizable/index.vue';
import type { ImageSetting } from '../../ui/image/index.vue';

export type MilestoneSetting = ResizableProps & {
  /**
   * 背景
   */
  bgImage?: string
  /**
   * 进度条
   */
  progress: ResizableProps & {
    /**
     * 进度条背景
     */
    trailBgImage: string
    /**
     * 当前进度背景
     */
    strokeBgImage: string
    /**
     * 默认节点
     */
    dot: ImageSetting
    /**
     * 激活节点
     */
    dotActive: ImageSetting
  }
}

export type MarksObjItem = {
  /**
   * 刻度值，0-100
   */
  [key: number]: any
}

export type MarksArrItem = {
  /**
   * 刻度名称
   */
  name: string
  /**
   * 刻度值
   */
  point: number
  /**
   * 额外属性
   */
  [key: string]: any
}

export interface MilestoneProps {
  /**
   * 配置
   */
  setting: MilestoneSetting
  /**
   * 百分比，默认 0
   */
  percent: number
  /**
   * 刻度标记
   */
  marks: MarksObjItem | MarksArrItem
}

export const defaultConfig = (): MilestoneSetting => {
  return {
    x: 0,
    y: 0,
    width: 353,
    height: 186,
    bgImage: '',
    progress: {
      x: 0,
      y: 76,
      width: 353,
      height: 10,
      trailBgImage: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713155393069_.png',
      strokeBgImage: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713155395694_.png',
      dot: {
        width: 13,
        height: 13,
        imgLink: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2023-9-20/1695193165941_.png',
      },
      dotActive: {
        width: 21,
        height: 15,
        imgLink: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713155401922_.png',
      },
    },
  };
}; 

export default {
  options: {},
  name: 'common-milestone',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import Resizable from '../../ui/resizable/index.vue';
import ResizableProvider from '../../ui/resizable/resizable-provider.vue';
import UiImage from '../../ui/image/index.vue';

const props = withDefaults(defineProps<MilestoneProps>(), {
  setting: defaultConfig,
  percent: 0,
  marks: () => ({}),
});

const emits = defineEmits<{
  (event: 'update:setting', value: MilestoneSetting): void;
}>();

const vSetting = useVModel(props, 'setting', emits);

const strokeWidth = computed(() => {
  // 限制最大值为100，最小值为0
  const width = Math.max(0, Math.min(100, props.percent));
  return `${width.toFixed(2)}%`;
});

const markArrItemStyle = computed<CSSProperties>(() => {
  return {
    width: `${+((100 / (props.marks as unknown as MarksArrItem[]).length).toFixed(2))}%`,
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  };
});

const currentActive = computed(() => {
  let current = -1;
  const marks = (props.marks as MarksArrItem);
  const totalWidth = props.setting.progress.width; // 进度条容器宽度
  const innerWidth = props.setting.progress.width * (window.parseFloat(strokeWidth.value) / 100); // 进度条宽度
  const numIntervals = marks.length * 2; // 区间数量

  const intervalWidth = totalWidth / numIntervals; // 每个区间的宽度
  const intervalIndex = Math.floor(innerWidth / intervalWidth); // 当前进度所在的区间索引

  for (let i = 0; i < marks.length; i++) {
    const itemIndex = 2 * (i + 1) - 1;
    
    if (itemIndex <= intervalIndex) {
      current = i;
    }
  }

  return current;
});

const isActive = (key: number) => {
  if (!Array.isArray(props.marks)) {
    return key <= props.percent;
  }
  return key <= currentActive.value;
};
</script>

<style>
.milestone {
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.milestone-progress-outer {
  width: 100%;
  height: 100%;
}

.milestone-progress-inner {
  position: absolute;
  top: 0;
  left: 0;
  width: auto;
  height: 100%;
  overflow: hidden;
}

.milestone-progress-trail {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.milestone-progress-stroke-bg {
  width: 100%;
  height: 100%;
  transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
  background-size: cover;
  background-repeat: no-repeat;
}

.milestone-progress-stroke-bg::before {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #ffffff;
  opacity: 0;
  animation-name: milestoneActive;
  animation-duration: 2.4s;
  animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
  animation-iteration-count: infinite;
  content: '';
  transform-origin: left;
}

.milestone-progress-mark {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
}

.milestone-progress-mark-item,
.milestone-progress-dot-item {
  position: absolute;
  left: 0;
  text-align: center;
}

@keyframes milestoneActive {
  0% {
    transform: translateX(-100%) scaleX(0);
    opacity: 0.1;
  }
  20% {
    transform: translateX(-100%) scaleX(0);
    opacity: 0.5;
  }
  100% {
    transform: translateX(0) scaleX(1);
    opacity: 0;
  }
}
</style>