import { onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { showLoadingToast } from '@bish/ui/src/toast';
import { postActivityPageConfig } from '@bish/api/src/activityPage'
import useActivityStore from '@bish/store/src/modules/activity';
import useActivityPageStore from '@bish/store/src/modules/activityPage'
import useUserStore from '@bish/store/src/modules/user'
import { UserAgent, useUserAgent } from '@bish/hooks/src/useUserAgent'
import { JSONParse, additionalLinkParameters, deleteLinkParameters, parseQueryString } from '@bish/utils/src/utils'
import { replaceQueryProperties } from '@bish/utils/src/utils'
import { sendShareMessage } from '@bish/utils/src/wx'
import type { ShareMessageConfigType } from '@bish/utils/src/wx'
import { useWxShare } from '@bish/hooks/src/useWxShare'
import type { MergedPageConfig } from '@bish/types/src/admin'
import localStorage from '@bish/utils/src/storage/localStorage'

// 隐私参数
const blackQuery = [
  'token',
  'invite_code',
  'recall_account_id',
  'invite_user',
  'invite_type',
  'team_id',
]

export function useActivityPageConfig() {
  const route = useRoute()
  const query = replaceQueryProperties(route)
  
  const activityStore = useActivityStore()
  const activityPageStore = useActivityPageStore()

  const { getWechatShareSign } = useWxShare()
  const { isUserAgentType } = useUserAgent()

  /**
   * 初始全局的活动页配置信息、活动数据
   */
  const initActivityConfigInfo = async () => {
    const toast = showLoadingToast({
      duration: 0,
      forbidClick: true,
    })
    try {
      initChannelId()
      await fetchActivityPageConfig()
      await activityStore.initActivityInfo(false)
    } catch (error) {
      console.warn('初始化活动数据失败', error)
    } finally {
      toast.close()
    }
  }

  /**
   * 设置微信小程序内嵌分享参数
   * @param shareParams 分享参数
   * @returns 
   */
  const setWeappShareMessage = (shareParams: Record<string, any>, shareConfig?: Partial<ShareMessageConfigType>) => {
    if (isUserAgentType !== 'WX_WEBVIEW') {
      return;
    }
    const { mergedPageConfig } = activityPageStore
    const randomShareContent = activityPageStore.randomShareContent(mergedPageConfig.shareTemps)

    // 删除原先登录成功附加到 url 的相关参数
    const pureUrl = deleteLinkParameters(blackQuery, window.location.href)
    const fullUrl = additionalLinkParameters(shareParams, pureUrl)
    sendShareMessage({
      title: randomShareContent?.title,
      desc: randomShareContent?.desc,
      imgUrl: randomShareContent?.imageUrl,
      path: `/subpkg/bish/webview?url=${encodeURIComponent(fullUrl)}`,
      ...shareConfig,
    })
  }

  // 初始微信小程序分享数据
  const initPageShare = () => {
    setWeappShareMessage({})
  }

  /**
   * 设置微信公众号网页分享参数
   * @param data 装修数据
   */
  const setWeChartShareConfig = (data: MergedPageConfig) => {
    if (isUserAgentType === UserAgent.WECHATWEB) {
      const queryParams = parseQueryString(window.location.href)
      const shareLink = additionalLinkParameters(
        {
          event_page: queryParams.event_page,
        },
        window.location.href.split('?')[0]
      )
      getWechatShareSign(data.shareTitle || '', data.shareDesc || '', data.shareImageUrl || '', shareLink)
    }
  }

  const fetchActivityPageConfig = async () => {
    try {
      if (!query.event_page) {
        return
      }
      const res = await postActivityPageConfig({
        id: +(query.event_page as string),
      })
      if (res.code === 0) {
        activityPageStore.setActivityPageConfig({
          ...res.data,
          area_type: res.data.area_type ?? 1, // 默认国内
        })

        // 设置微信小程序内嵌分享数据
        setWeappShareMessage({})

        // 设置微信分享参数
        setWeChartShareConfig(activityPageStore.mergedPageConfig)
      }
    } catch (error) {
      console.warn('获取活动页装修信息出错', error)
    }
  }

  /**
   * 初始渠道id参数，用户数据上报
   */
  const initChannelId = () => {
    if (query.c) {
      localStorage.setLocalStorage('SY_LOG_DATA_c', 1)
      localStorage.setLocalStorage('SY_LOG_DATA_scene_id', query.c)
    }
  }

  return {
    initActivityConfigInfo,
    fetchActivityPageConfig,
    setWeappShareMessage,
    initPageShare,
  }
}

export {
  ShareMessageConfigType
}