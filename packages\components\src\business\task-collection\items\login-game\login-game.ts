import type { TaskCollectionCommon } from '../index'
import type { ImageSetting } from '../../../../ui/image/index.vue'
import type { TextSetting } from '../../../../ui/text/index.vue'
import type { PopupSetting } from '../../../../common/popup.vue';
import type { DownloadGameSetting } from '../../../../business/download-game.vue';

export type ActionType = 'toast' | 'modal' | 'download'

/**
 * 任务-登录游戏配置
 */
export type TaskLoginGameSetting = TaskCollectionCommon & {
  /**
   * 交互方式，可选 'toast' | 'modal'，默认 'toast'
   */
  actionType: ActionType
  /**
   * 提示语，默认 请登录游戏完成任务
   */
  tip: string
  /**
   * 引导弹窗配置
   */
  guideModal?: GuideModalSetting
  /**
   * 下载游戏配置
   */
  downloadGame?: DownloadGameSetting
}

export type GuideModalSetting = PopupSetting & {
  /**
   * 提示配置
   */
  tip: TextSetting
  /**
   * 海报展示
   */
  poster: ImageSetting
  /**
   * 前往游戏按钮
   */
  visitGame: ImageSetting
  /**
   * 下载游戏按钮
   */
  downloadGame: DownloadGameSetting
}