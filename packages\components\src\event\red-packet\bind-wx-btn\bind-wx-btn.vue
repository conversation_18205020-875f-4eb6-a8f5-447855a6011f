<!--
* @Description: 绑定微信按钮
-->
<template>
  <Resizable
    class="bind-wx-btn"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
  >
    <ResizableProvider>
      <!-- 绑定按钮 -->
      <UiImage
        v-if="setting.bindBtn?.imgLink"
        v-model:setting="setting.bindBtn"
        @click="handleBind"
      />
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'bind-wx-btn',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import type { StatefulComponent } from '@bish/types/src/admin';
import { showToast } from '@bish/ui/src/toast';
import { useAccountBindWx } from '@bish/hooks/src/business/useAccountBindWx';
import useAdminStore from '@bish/store/src/modules/admin';
import UiImage from '../../../ui/image/index.vue';
import Resizable from '../../..//ui/resizable/index.vue';
import ResizableProvider from '../../..//ui/resizable/resizable-provider.vue';
import { defaultConfig } from './index';
import type { BindWxBtnSetting } from './bind-wx-btn';

export interface BindWxProps {
  /**
   * 配置
   */
  setting: BindWxBtnSetting
}

const props = withDefaults(defineProps<BindWxProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: BindWxBtnSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityStore = useActivityStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const { handleBindGuide } = useAccountBindWx();

const handleBind = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }

  const pass = activityStore._checkIn(false);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  if (!activityStore.activityAccountInfo?.wx_bind_status) {
    handleBindGuide();
  } else {
    showToast('当前账号已绑定，无法重复绑定');
  }
};
</script>
