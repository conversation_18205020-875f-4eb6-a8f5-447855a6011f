import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'
import crypto from 'crypto-js'
import type { ComponentWithUserInfoMembers } from '../activity'
const { VITE_ACTIVITY_URL, VITE_SHORT_URL } = useEnv()

export type CreateLimitedTeamParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 队伍配置id
   */
  team_config_id: number
  /**
   * 来源 1为游戏 2为h5
   */
  source?: number
  /**
   * 队伍名字
   */
  team_name?: string
  /**
   * 区服id
   */
  server_id?: string
  /**
   * 区服名称
   */
  server_name?: string
  /**
   * 角色id
   */
  role_id?: number
  /**
   * 角色等级
   */
  role_level?: number
  /**
   * 角色名称
   */
  role_name?: string
  /**
   * 职业，0是无职业,1是奕剑,2是龙宫,3是青丘,4是高老庄,5是唐门,6是普陀,7是九玄
   * 字符串数组
   */
  career_ask_collect?: string
  /**
   * 在线时间，0是默认,1是经常在线,2是早上在线,3是晚上在线
   */
  online_time?: number
  /**
   * 队伍风格，0是默认,1是竞技,2是情义,3是休闲
   */
  team_style?: number
  /**
   * 小队(访问)状态，0是默认,1是公开,2是私密
   */
  access_status?: number
  /**
   * 队伍宣言
   */
  team_slogan?: string
}

/**
 * 创建队伍
 * @param data CreateLimitedTeamParams
 * @returns 
 */
export function postCreateLimitedTeam(params: CreateLimitedTeamParams) {
  return http.post(`${VITE_ACTIVITY_URL}/team/game/createLimitedTeam`, params)
}

/**
 * @description: 点击分享链接判断组队状态
 * @param {object} data
 * @return {*}
 */
export function checkoutTeamStatus(data: object) {
  return http.post(`${VITE_ACTIVITY_URL}/team/status `, data)
}

export type JoinLimitedTeamParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 队伍id
   */
  team_id: number
  /**
   * 区服id
   */
  server_id?: string
  /**
   * 区服名称
   */
  server_name?: string
  /**
   * 角色id
   */
  role_id?: number
  /**
   * 角色等级
   */
  role_level?: number
  /**
   * 角色名称
   */
  role_name?: string
  /**
   * 性别
   */
  sex?: number
  /**
   * 门派
   */
  career?: number
}

/**
 * @description: 接受组队
 * @param {object} data
 * @return {*}
 */
export function postJoinLimitedTeam(params: JoinLimitedTeamParams) {
  return http.post(`${VITE_ACTIVITY_URL}/team/game/joinLimitedTeam`, params)
}

export type UpdateTeamInfoParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 队伍id
   */
  team_id: number
  /**
   * 队伍名字
   */
  team_name?: string
  /**
   * 在线时间，0是默认,1是经常在线,2是早上在线,3是晚上在线
   */
  online_time: number
  /**
   * 队伍风格，0是默认,1是竞技,2是情义,3是休闲
   */
  team_style: number
  /**
   * 未加入队伍的职业要求集合，0是无职业,1是奕剑,2是龙宫,3是青丘,4是高老庄,5是唐门,6是普陀,7是九玄
   * 字符串数组
   */
  not_career_ask_collect: string
  /**
   * 职业，0是无职业,1是奕剑,2是龙宫,3是青丘,4是高老庄,5是唐门,6是普陀,7是九玄
   * 字符串数组
   */
  career_ask_collect: string
  /**
   * 小队(访问)状态，0是默认,1是公开,2是私密
   */
  access_status: number
  /**
   * 队伍宣言
   */
  team_slogan?: string
}

/**
 * 更新队伍配置
 * @param params UpdateTeamInfoParams
 * @returns 
 */
export function postUpdateTeamInfo(params: UpdateTeamInfoParams) {
  return http.post(`${VITE_ACTIVITY_URL}/team/game/updateTeamInfo`, params)
}

export type QuitTeamParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 队伍id
   */
  team_id: number
}

/**
 * 退出队伍
 * @param params QuitTeamParams
 * @returns 
 */
export function postQuitTeam(params: QuitTeamParams) {
  return http.post(`${VITE_ACTIVITY_URL}/team/quitTeam`, params)
}

export type GameTeamListParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 第几页
   */
  page?: number
  /**
   * 每页多少行，默认是10
   */
  page_rows?: number
  /**
   * 职业
   */
  career_ask_collect?: number
  /**
   * 在线时间
   */
  online_time?: number
  /**
   * 队伍风格
   */
  team_style?: number
  /**
   * 小队(访问)状态
   */
  access_status?: number
}

export type GameTeamListData = {
  /**
   * 所有数据的总数
   */
  count: number
  /**
   * 页数
   */
  page: number
  /**
   * 在线时间,0是默认,1是经常在线,2是早上在线,3是晚上在线
   */
  online_time: number
  /**
   * 队伍风格,0是默认,1是竞技,2是情义,3是休闲
   */
  team_style: number
  /**
   * 列表
   */
  list: {
    /**
     * id
     */
    id: number
    /**
     * 活动id
     */
    activity_id: number
    /**
     * 队伍配置id
     */
    team_config_id: number
    /**
     * 队伍名称
     */
    team_name: string
    /**
     * 队伍信息存放
     */
    team_list: ComponentWithUserInfoMembers
  }[]
}

/**
 * 组队列表
 * @param params GameTeamListParams
 * @returns 
 */
export function postGameTeamList(params: GameTeamListParams) {
  return http.post(`${VITE_ACTIVITY_URL}/team/game/teamList`, params)
}

// function generateRandomString(length: number) {
//   let result = ''
//   const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
//   const charactersLength = characters.length
//   for (let i = 0; i < length; i++) {
//     result += characters.charAt(Math.floor(Math.random() * charactersLength))
//   }
//   return result
// }

const secret = 'EGZK4PxRAt5hUHngvcDMgwpY2c7DNrGw'
export const getShortLink = (data: {
  group_name: string
  link_name: string
  origin_link: string
}): Promise<{ data: { short_link: string }; code: number }> => {
  const ts = Math.floor(+new Date() / 1000)
  const sign = crypto.MD5(`${data.origin_link}${ts}${secret}`).toString()
  return window
    .fetch(`${VITE_SHORT_URL}/common/openApi/search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        origin_link: data.origin_link,
        sign,
        ts,
      }),
    })
    .then((res) => res.json())
    .then((res) => {
      if (res.data.short_link || res.code !== 0) {
        return res
      } else {
        const ts = Math.floor(+new Date() / 1000)
        const sign = crypto.MD5(`${data.group_name}${data.link_name}${data.origin_link}${ts}${secret}`).toString()
        return window
          .fetch(`${VITE_SHORT_URL}/common/openApi/create`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              ...data,
              sign,
              ts,
            }),
          })
          .then((res) => res.json())
      }
    })
}
