import type { BoxPositionItem } from '../../box-lottery/index.vue';
import type { BoxLotterySetting } from './box-lottery';
import { defaultConfig as congratulationsModalDefaultConfig } from '../../congratulations-modal.vue';

export * from './box-lottery';

export const defaultBoxPositionItem = (): BoxPositionItem => {
  return {
    x: 0,
    y:0,
  };
}

export const defaultConfig = (): BoxLotterySetting => {
  return {
    x: 0,
    y: 0,
    width: 328,
    height: 317,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713182893080_.png',
    boxLottery: {
      x: 24,
      y: 24,
      width: 279,
      height: 279,
      bgImage: '',
      boxGrid: 9,
      box: {
        x: 0,
        y: 0,
        width: 87,
        height: 87,
        bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713182896787_.png',
        prize: {
          x: 18,
          y: 10,
          width: 50,
          height: 50,
        },
        prizeName: {
          x: -4,
          y: 64,
          width: 94,
          height: 14,
          fontSize: 12,
          color: '#C2FFAA',
          align: 'center',
        },
      },
      drawBtn: {
        x: 98,
        y: 96,
        width: 85,
        height: 85,
        bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713182899162_.png',
        remaining: {
          x: -58,
          y: -113,
          width: 200,
          height: 20,
          fontSize: 12,
          color: '#C2FFAA',
          content: '剩余抽奖次数：{{num}}',
          align: 'center',
          show: true,
          enabled: true,
        },
      },
      congratulations: congratulationsModalDefaultConfig(),
      boxPosition: [
        defaultBoxPositionItem(),
        defaultBoxPositionItem(),
        defaultBoxPositionItem(),
        defaultBoxPositionItem(),
        defaultBoxPositionItem(),
        defaultBoxPositionItem(),
        defaultBoxPositionItem(),
        defaultBoxPositionItem(),
      ],
    },
    needBindRole: 1,
    process: {
      x: 14,
      y: -22,
      width: 300,
      height: 20,
      fontSize: 14,
      color: '#C2FFAA',
      content: '每日登录游戏即可获取抽奖次数 ({{process}})',
      align: 'center',
    },
    lotteryId: 0,
  }
}