import { userAgent } from '@bish/utils/src/utils';
import { bishInst } from '@bish/core/src/models/Bish';
import { showToast } from '@bish/ui/src/toast';
import { copyText } from './text'

/**
 * 根据传入的参数 href 打开新页面或滚动到页面中的某个元素
 * @param href 可以是普通的页面链接或 document.querySelector 中的 selector 参数
 */
export const navigateScroll = async (href: string) => {
  const { isUserAgentType } = userAgent();
  const isWeapp = isUserAgentType === 'WX_MINI'; // 微信小程序环境
  // URL Scheme 正则表达式
  const urlSchemeRegex = /^(?!https?:\/\/)[a-zA-Z0-9.]+:\/\//;

  // 尝试解析 href 为 URL
  try {
    if (isWeapp) {
      await uni.navigateTo({
        url: href,
      });
    } else {
      // 微信浏览器环境，并且为 url scheme 链接，微信网页环境会拦截
      if (isUserAgentType === 'WECHATWEB' && urlSchemeRegex.test(href)) {
        copyText(window.location.href);
        showToast(`页面链接已复制，前往手机浏览器打开链接进行访问~`);
        return;
      }
      
      // 检测是否为 URL Scheme（可能是 App 唤起链接）
      if (urlSchemeRegex.test(href)) {
        const startTime = Date.now();
        const timeout = 2500; // 超时时间设置为 2.5 秒
        
        // 尝试打开 App
        window.location.href = href;
        
        // 设置超时检测
        setTimeout(() => {
          const endTime = Date.now();
          // 如果时间差小于设定的超时时间，说明唤起失败
          if (endTime - startTime < timeout + 200) {
            const isHidden = document.hidden || 
                            (document as any).mozHidden || 
                            (document as any).msHidden || 
                            (document as any).webkitHidden;
            
            if (!isHidden) {
              // 唤起失败，显示提示
              showToast('请先下载APP');
            }
          }
        }, timeout);
        
        return;
      }
      
      const url = new URL(href);
      // 如果解析成功，认为 href 是一个 URL
      window.open(url.href, '_self');
    }
  } catch (e) {
    if (isWeapp) {
      bishInst.pageObserver.notify({
        type: 'scrollTo',
        detail: href,
      });
    } else {
      // 如果解析失败，认为 href 是一个选择器
      const element = document.querySelector(href) as HTMLElement;
      if (element) {
        // 滚动到元素
        window.scrollTo({
          top: element.offsetTop - window.innerHeight / 2 + element.offsetHeight / 2,
          behavior: 'smooth',
        });
      } else {
        console.warn(`未找到元素: ${href}`);
      }
    }
  }
};

export const navigate = (href: string, target: string = '_blank') => {
  const { isUserAgentType } = userAgent();
  const isWeapp = isUserAgentType === 'WX_MINI'; // 微信小程序环境

  if (isWeapp) {
    uni.navigateTo({
      url: href,
    });
  } else {
    window.open(href, target);
  }
};
