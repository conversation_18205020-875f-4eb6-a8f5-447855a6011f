import type { LoginWeappEmbedSetting } from './login-weapp-embed';

export * from './login-weapp-embed';

export const defaultConfig = (): LoginWeappEmbedSetting => ({
  modalSetting: {
    x: 0,
    y: 0,
    width: 375, // 这里的宽度没啥用，在用户端会被覆盖成 100vw
    height: 667, // 这里的高度没啥用，在用户端会被覆盖成 100vh
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241112/1731382383772-%E7%BB%84%20204.png',
    okBtn: {
      x: 85,
      y: 493,
      width: 206,
      height: 63,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241112/1731382380880-%E7%BB%84%2066.png',
    },
    // closeBtn: {
    //   x: 314,
    //   y: 23,
    //   width: 30,
    //   height: 26,
    //   imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241111/1731309996327-8%20%E6%8B%B7%E8%B4%9D%203.png'
    // },
  },
  agreement: {
    position: {
      x: 56,
      y: 623,
      width: 263,
      height: 32,
    },
    show: true,
    textColor: '#999999',
    linkColor: 'rgb(36, 43, 69)',
  },
  content: {
    x: 0,
    y: 565,
    width: 364,
    height: 53,
    fontSize: 10,
    color: '#ffffff',
    content: '请授权我们使用您的手机号码，用于《永夜降临：复苏》手游的预约登记，如您未注册诗悦游戏账号，将自动使用该手机号注册。',
    enabled: false,
  }
});