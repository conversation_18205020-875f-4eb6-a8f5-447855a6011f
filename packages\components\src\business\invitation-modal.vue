<!--
* @Description: 接受好友/组队邀请-弹窗
-->
<template>
  <Popup
    z-index="99"
    :show="show"
    :lock-scroll="false"
    v-model:setting="setting"
    @close="handleClose"
    @ok="handleAccept"
  >
    <!-- 海报 -->
    <UiImage
      v-if="setting.poster?.imgLink"
      v-model:setting="setting.poster"
    />
    <!-- 邀请文案 -->
    <UiText
      v-model:setting="setting.content"
      :interpolation="interpolation"
      class="invitation-modal-content"
    />
    <!-- 取消按钮 -->
    <UiImage
      v-if="setting.cancelBtn?.imgLink"
      v-model:setting="setting.cancelBtn"
      @click.stop="handleClose"
    />
  </Popup>
</template>

<script lang="ts">
import type { ComponentWithUserInfoMembers } from '@bish/api/src/activity';
import type { TextSetting } from '../ui/text/index.vue';
import type { ImageSetting } from '../ui/image/index.vue';
import type { PopupSetting } from '../common/popup.vue';

export interface InvitationModalSetting extends PopupSetting {
  /**
   * 海报
   */
  poster?: ImageSetting
  /**
   * 邀请文案
   */
  content?: TextSetting
  /**
   * 取消按钮
   */
  cancelBtn?: ImageSetting
  /**
   * 接受邀请后续操作
   */
  afterAccept?: {
    /**
     * 操作方式 1：链接 2：弹窗
     */
    optionType: number
    /**
     * 链接
     */
    link?: string
    /**
     * 弹窗
     */
    popup: PopupSetting
  }
}

export type Member = ComponentWithUserInfoMembers[0]

export interface InvitationModalProps {
  /**
   * 配置
   */
  setting: InvitationModalSetting
  /**
   * 是否显示
   */
  show: boolean
  /**
   * 邀请人
   */
  inviter: string
}

export const defaultConfig = (): InvitationModalSetting => {
  return {
    x: 0,
    y: 0,
    width: 373,
    height: 353,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403310667_.png',
    okBtn: {
      x: 198,
      y: 277,
      width: 121,
      height: 34,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403297387_.png',
    },
    closeBtn: {
      x: 324,
      y: -35,
      width: 35,
      height: 35,
      imgLink: 'https://cms-1256453865.cos.ap-shanghai.myqcloud.com/public/2024-07-31/1722414059018-%E5%85%B3%E9%97%AD%20%E6%8B%B7%E8%B4%9D%202.png'
    },
    cancelBtn: {
      x: 61,
      y: 277,
      width: 121,
      height: 34,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403294597_.png',
    },
    poster: {
      x: 57,
      y: 33,
      width: 263,
      height: 149,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403354250_.png',
    },
    content: {
      x: 102,
      y: 194,
      width: 174,
      height: 80,
      fontSize: 14,
      content: '您的好友【{{friendName}}】邀请您组队领礼!(加入队伍后将无法退出)',
      color: '#424272',
      align: 'center',
    },
    afterAccept: {
      optionType: 1,
      link: '',
      popup: {
        x: 0,
        y: 0,
        width: 375,
        height: 441,
        bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250209/1739108480402-%E7%BB%84%201%402x%20%286%29.png',
        closeBtn: {
          x: 307,
          y: 41,
          width: 20,
          height: 20,
          imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250210/1739177442093-close.png',
        }
      } 
    }
  };
};

export default {
  name: 'invitation-modal',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import UiText from '../ui/text/index.vue';
import UiImage from '../ui/image/index.vue';
import Popup from '../common/popup.vue';

const props = withDefaults(defineProps<InvitationModalProps>(), {
  setting: defaultConfig,
  show: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: InvitationModalSetting): void;
  (event: 'ok'): void;
  (event: 'close'): void;
}>();

const setting = useVModel(props, 'setting', emits);

const interpolation = computed(() => {
  return {
    friendName: props.inviter || '',
  };
});

/**
 * 接受邀请
 */
const handleAccept = () => {
  emits('ok');
};

const handleClose = () => {
  emits('close');
};
</script>

<style>
.invitation-modal-content {
  font-weight: 600;
  line-height: 1.3;
}
</style>