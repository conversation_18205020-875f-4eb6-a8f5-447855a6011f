import type { BoxPositionItem, WheelFortuneSetting } from './wheel-fortune';
import { defaultConfig as congratulationsModalDefaultConfig } from '../../congratulations-modal.vue';
export * from './wheel-fortune';

export const defaultBoxPositionItem = (): BoxPositionItem => {
  return {
    x: 0,
    y:0,
  };
}

export const defaultConfig = (): WheelFortuneSetting => {
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 375,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250225/1740466499185-lottery-bg-2.png',
    lotteryId: 0,
    boxGrid: 10,
    size: 318,
    activeColor: '#F0ECD2',
    needBindRole: 1,
    process: {
      x: 14,
      y: -22,
      width: 300,
      height: 20,
      fontSize: 14,
      color: '#C2FFAA',
      content: '每日登陆游戏即可获取抽奖次数 ({{process}})',
      align: 'center',
      enabled: false,
    },
    box: {
      x: 20,
      y: 20,
      width: 60,
      height: 70,
      bgImage: '',
      prize: {
        x: 10,
        y: 0,
        width: 40,
        height: 40,
      },
      prizeName: {
        x: 0,
        y: 46,
        width: 60,
        height: 14,
        fontSize: 9,
        color: '#ffffff',
        align: 'center',
      },
    },
    drawBtn: {
      x: 147,
      y: 144,
      width: 85,
      height: 85,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250224/1740359718754-lottery-draw.png',
      remaining: {
        x: -56,
        y: 219,
        width: 200,
        height: 20,
        fontSize: 13,
        color: '#ffddab',
        content: '剩余抽奖次数：{{num}}',
        align: 'center',
        fontWeight: true,
        enabled: true,
      },
    },
    congratulations: congratulationsModalDefaultConfig(),
    angle: 0,
    activeBg: '',
    pointer: {
      x: 147,
      y: 65,
      width: 57,
      height: 221,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250421/1745215330134-lottery-pointer.png',
      enabled: false,
    },
    boxPosition: [
      defaultBoxPositionItem(),
      defaultBoxPositionItem(),
      defaultBoxPositionItem(),
      defaultBoxPositionItem(),
      defaultBoxPositionItem(),
      defaultBoxPositionItem(),
      defaultBoxPositionItem(),
      defaultBoxPositionItem(),
      defaultBoxPositionItem(),
      defaultBoxPositionItem(),
    ],
  };
};