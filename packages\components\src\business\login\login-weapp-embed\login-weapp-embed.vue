
<!--
 * @Description: 内嵌微信小程序登录页配置
-->
<template>
  <Popup
    :show="show"
    v-model:setting="setting.modalSetting"
    z-index="101"
    :resizable="false"
    :content-style="popupContentStyle"
    @close="handleClose"
  >
    <!-- 用户协议 -->
    <Resizable
      v-model:x="setting.agreement.position.x"
      v-model:y="setting.agreement.position.y"
      v-model:width="setting.agreement.position.width"
      v-model:height="setting.agreement.position.height"
      v-if="setting.agreement.show"
    >
      <div class="login-agreement" :style="agreementStyle">
        <van-checkbox
          :icon-size="pxTransform(12)"
          v-model="agreementChecked"
          shape="square"
          :checked-color="theme"
          style="display: inline-flex;"
        >
          <span :style="agreementTxtStyle">我已阅读并同意</span>
        </van-checkbox>
        <span :style="agreementLinkStyle">《用户协议》</span>
        <span :style="agreementTxtStyle">及</span>
        <span :style="agreementLinkStyle">《隐私协议》</span>
      </div>
    </Resizable>
     <!-- 内容文案 -->
     <UiText v-if="setting.content?.enabled" v-model:setting="setting.content" />
  </Popup>
</template>

<script lang="ts">
export default {
  name: 'login-weapp-embed',
}
</script>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { pxTransform } from '@bish/utils/src/viewport';
import Popup from '../../../common/popup.vue';
import { useVModel } from '@vueuse/core';
import Resizable from '../../../ui/resizable/index.vue';
import UiText from '../../../ui/text/index.vue';
import { defaultConfig } from './index';
import type { LoginWeappEmbedSetting } from './login-weapp-embed';

export interface LoginWeappEmbedProps {
  /**
   * 配置
   */
  setting: LoginWeappEmbedSetting
  /**
   * 主题色
   */
  theme?: string
  /**
   * 是否显示
   */
  show: boolean
}

const props = withDefaults(defineProps<LoginWeappEmbedProps>(), {
  setting: defaultConfig,
  show: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: LoginWeappEmbedSetting): void;
  (event: 'close'): void;
}>();

// 是否已勾选协议
const agreementChecked = ref(false);

const setting = useVModel(props, 'setting', emits);

const agreementStyle = computed<CSSProperties>(() => ({
  fontSize: pxTransform(12),
}));

const agreementTxtStyle = computed<CSSProperties>(() => {
  return {
    color: setting.value.agreement.textColor || '#999999',
  }
});

const agreementLinkStyle = computed<CSSProperties>(() => {
  return {
    color: setting.value.agreement.linkColor || props.theme,
  }
});

const popupContentStyle = computed<CSSProperties>(() => {
  return {
    maxWidth: `${pxTransform(375)}`,
  };
});

const handleClose = () => {
  emits('close');
};
</script>

<style>
.login-weapp-embed {}

.login-weapp-embed-agreement {
  color: #999;
  line-height: 1.1;
}

.login-weapp-embed-agreement .van-checkbox {
  align-items: baseline;
}

.login-weapp-embed-agreement .van-checkbox__icon .van-icon {
  border-color: #999;
}
</style>
