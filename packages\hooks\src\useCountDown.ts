import { ref, onBeforeUnmount } from 'vue'

/**
 * @description 倒计时hooks
 * @param {*} param.total 总时长
 * @returns
 */
export default function useCountDown({ total = 60 } = {}) {
  // 倒数
  const count = ref(0)

  let timer: number | null = null

  /**
   * 开始倒计时
   */
  const startCountDown = () => {
    if (count.value > 0) {
      return
    }
    count.value = total
    timer = setInterval(() => {
      count.value--
      if (count.value <= 0) {
        clearTimer()
      }
    }, 1000)
  }

  const clearTimer = () => {
    if (timer) {
      count.value = 0
      clearInterval(timer)
    }
  }

  onBeforeUnmount(() => {
    clearTimer()
  })

  return {
    count,
    startCountDown,
    clearTimer,
  }
}
