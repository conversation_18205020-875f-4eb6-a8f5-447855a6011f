import type { CommonBackgroundSetting, CommonSetting } from '@bish/types/src';
import type { PopupSetting } from '../../common/popup.vue';
import type { TextSetting } from '../../ui/text/index.vue';

export interface QuestionSetting extends CommonBackgroundSetting {
  /**
   * 弹窗配置
   */
  modal: PopupSetting & {
    /**
     * 内容配置
     */
    content: CommonSetting
    /**
     * 说明
     */
    instruction: string
  }
} 