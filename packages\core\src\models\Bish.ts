import type { LoginData } from '@bish/api/src/user'
import useUserStore from '@bish/store/src/modules/user'
import storage from '@bish/utils/src/storage/localStorage'
import { Observer } from './Observer'

export type WeappConfig = {
  /**
   * 指纹前缀，只在微信小程序环境数据上报中使用该字段
   */
  distinctPrefix?: string
  /**
   * 系统类型，只在微信小程序环境数据上报中使用该字段
   * 操作系统	os_type	否	字符串	操作系统类型：（现在属于M端是123和默认值）1:安卓 2:ios  3:SDkwin系统; 4:pc；5:小程序 6：微社区 7：平台社区小程序
   */
  systemType?: string
  /**
   * 系统来源，只在微信小程序环境数据上报中使用该字段
   * 1：浏览器、2：公众号:、3：游戏客户端 4:服务端 5：小程序 6：微社区 7：平台社区小程序
   */
  systemSource?: number
}

export type BishOptions = {
  /**
   * 微信小程序相关配置
   */
  weapp: WeappConfig
}

export type WeappNotifyPayload = {
  /**
   * 类型： phoneLogin 手机号登录 | jumpAgreement 跳转协议
   */
  type: 'phoneLogin' | 'jumpAgreement'
  /**
   * 参数
   * TODO: 补全 phoneLogin、jumpAgreement detailType
   */
  detail: any
}

export type PageNotifyPayload = {
  /**
   * 类型： scrollTo 滚动到某个元素
   */
  type: 'scrollTo'
  /**
   * 参数
   */
  detail: any
}

class Bish {
  loginObserver: Observer
  weappObserver: Observer<WeappNotifyPayload>
  /**
   * 统一处理页面交互，例如：滚动到某个元素
   */
  pageObserver: Observer<PageNotifyPayload>
  weapp: WeappConfig

  constructor(options?: BishOptions) {
    this.initialize(options)
  }

  protected initialize(options?: BishOptions) {
    this.loginObserver = new Observer()
    this.weappObserver = new Observer<WeappNotifyPayload>()
    this.pageObserver = new Observer<PageNotifyPayload>()
    this.weapp = Object.assign({}, {
      distinctPrefix: options?.weapp?.distinctPrefix || 'pt_miniprogram_', // 默认平台会员小程序
      systemType: options?.weapp?.systemType || '7', // 默认平台会员小程序
      systemSource: options?.weapp?.systemSource || 7 // 默认平台会员小程序
    })
  }

  public async login(userInfo: LoginData) {
    const userStore = useUserStore()

    userStore.setUserData(userInfo)
    storage.removeLocalStorage('openid')
    // 执行登录成功回调队列
    userStore.workLoggedTask()
  }

  public async setWeappConfig(config: WeappConfig) {
    if (typeof config === 'object') {
      this.weapp = Object.assign({}, this.weapp, config)
    }
  }
}

// 暂且在这里提供实例
const bishInst = new Bish()

export {
  bishInst,
}
