import type { SetupContext } from 'vue'
import { computed, nextTick, reactive, ref, toRefs, watch } from 'vue'
// import { cloneDeep, getMainClass, isEqualValue } from '../_utils'
import { cloneDeep, isEqualValue } from '@bish/shared/src/common'
import type { PickerEmits, PickerOption, PickerProps, PickerColumnsType } from './Picker'

const DEFAULT_FILED_NAMES = {
  text: 'text',
  value: 'value',
  children: 'children',
  className: '',
}

export const componentName = `${''}-picker`

export function usePicker(props: PickerProps, emit: SetupContext<PickerEmits>['emit']) {
  const classes = computed(() => {
    // return getMainClass(props, componentName)
    return ''
  })

  const state: {
    formattedColumns: (PickerOption | PickerOption[])[]
  } = reactive({
    formattedColumns: props.columns,
  })

  const columnFieldNames = computed(() => {
    return {
      ...DEFAULT_FILED_NAMES,
      ...props.columnsFieldNames,
    }
  })

  // 选中项
  const defaultValues = ref<(number | string)[]>([])

  // 当前类型
  const columnsType = computed<PickerColumnsType>(() => {
    const firstColumn: PickerOption | PickerOption[] = state.formattedColumns[0]
    const fields = columnFieldNames.value

    if (firstColumn) {
      if (Array.isArray(firstColumn))
        return 'multiple'

      if (fields.children in firstColumn)
        return 'cascade'
    }

    return 'single'
  })

  // 级联数据格式化
  const formatCascade = (columns: PickerOption[], defaultValues: (number | string)[]) => {
    const formatted: PickerOption[][] = []
    const fields = columnFieldNames.value

    let cursor: PickerOption = {
      text: '',
      value: '',
      [fields.children]: columns,
    }

    let columnIndex = 0

    while (cursor && cursor[fields.children]) {
      const options: PickerOption[] = cursor[fields.children]
      const value = defaultValues[columnIndex]

      let index = options.findIndex(columnItem => columnItem[fields.value] === value)
      if (index === -1)
        index = 0

      cursor = cursor[fields.children][index]

      columnIndex += 1

      formatted.push(options)
    }

    return formatted
  }

  // 将传入的 columns 格式化
  const columnsList = computed<(PickerOption | PickerOption[])[]>(() => {
    switch (columnsType.value) {
      case 'single':
        return [state.formattedColumns]
      case 'multiple':
        return state.formattedColumns
      case 'cascade':
        return formatCascade(
          state.formattedColumns,
          defaultValues.value ? defaultValues.value : [],
        )
    }

    return []
  })

  const defaultIndexes = computed(() => {
    const fields = columnFieldNames.value

    return (columnsList.value as PickerOption[][]).map((column: PickerOption[], index: number) => {
      const targetIndex = column.findIndex(item => item[fields.value] === defaultValues.value[index])
      return targetIndex === -1 ? 0 : targetIndex
    })
  })

  const delayDefaultIndexes = ref<number[]>(columnsList.value.map(() => 0))

  watch(defaultIndexes, async (value) => {
    await nextTick()

    delayDefaultIndexes.value = value
  }, { immediate: true })

  const columnRefs = ref<any[]>([])

  const columnRef = (el: any) => {
    if (el && columnRefs.value.length < columnsList.value.length)
      columnRefs.value.push(el)
  }

  const selectedOptions = computed(() => {
    const fields = columnFieldNames.value

    return (columnsList.value as PickerOption[][]).map((column: PickerOption[], index: number) => {
      return column.find(item => item[fields.value] === defaultValues.value[index]) || column[0]
    })
  })

  const cancel = () => {
    emit('cancel', {
      selectedValues: defaultValues.value,
      selectedOptions: selectedOptions.value,
    })
  }

  const changeHandler = (columnIndex: number, option: PickerOption) => {
    const fields = columnFieldNames.value

    // 补全子级默认值
    if (option && Object.keys(option).length) {
      defaultValues.value = defaultValues.value ? defaultValues.value : []

      if (columnsType.value === 'cascade') {
        // 补全父级默认值
        let currentLevel = 0
        let parent = state.formattedColumns
        
        // 确保父级都有默认值
        while (currentLevel < columnIndex) {
          const currentValue = defaultValues.value[currentLevel] as string
          const currentOptions = parent as PickerOption[]
          const defaultOption = currentOptions.find(opt => opt[fields.value] === currentValue) || currentOptions[0]
          
          if (!defaultValues.value[currentLevel]) {
            defaultValues.value[currentLevel] = defaultOption?.[fields.value] ?? ''
          }
          
          parent = defaultOption?.[fields.children] || []
          currentLevel++
        }

        // 更新当前列及子级
        defaultValues.value = [
          ...defaultValues.value.slice(0, columnIndex),
          option[fields.value] ?? '',
          ...Array(columnIndex).fill('') // 清空后续旧值
        ]

        let cursor = option
        let index = columnIndex
        while (cursor?.[fields.children]?.length) {
          const nextValue = cursor[fields.children][0]?.[fields.value] ?? ''
          
          if (index + 1 >= defaultValues.value.length) {
            defaultValues.value.push(nextValue)
          } else {
            defaultValues.value[index + 1] = nextValue
          }
          
          cursor = cursor[fields.children][0]
          index++
        }

        // 截断多余层级
        defaultValues.value = defaultValues.value.slice(0, index + 1)
      }
      else {
        defaultValues.value[columnIndex] = Object.prototype.hasOwnProperty.call(option, fields.value)
          ? option[fields.value]
          : ''
      }

      emit('change', {
        columnIndex,
        selectedValues: defaultValues.value,
        selectedOptions: selectedOptions.value,
      })
    }
  }

  const confirm = () => {
    const fields = columnFieldNames.value

    if (defaultValues.value && !defaultValues.value.length) {
      // 统一补全逻辑：适用于所有类型
      columnsList.value.forEach((columnGroup, index) => {
        const columns = Array.isArray(columnGroup) ? columnGroup : [columnGroup]
        
        // 当该层级没有值时自动补全
        if (!defaultValues.value?.[index] && columns.length > 0) {
          defaultValues.value = defaultValues.value || []
          defaultValues.value[index] = columns[0]?.[fields.value] ?? ''
        }
      })

      // 确保数组长度匹配
      if (defaultValues.value && defaultValues.value.length > columnsList.value.length) {
        defaultValues.value = defaultValues.value.slice(0, columnsList.value.length)
      }
    }

    emit('confirm', {
      selectedValues: defaultValues.value,
      selectedOptions: selectedOptions.value,
    })
  }

  const confirmHandler = () => {
    if (columnRefs.value.length > 0) {
      columnRefs.value.forEach((column) => {
        column.stopMomentum()
      })
    }

    confirm()
  }

  watch(
    () => props.modelValue,
    (value) => {
      if (!isEqualValue(value, defaultValues.value))
        defaultValues.value = cloneDeep(value)
    },
    { deep: true, immediate: true },
  )

  watch(
    defaultValues,
    (value) => {
      if (!isEqualValue(value, props.modelValue))
        emit('update:modelValue', value)
    },
    { deep: true },
  )

  watch(
    () => props.columns,
    (value) => {
      state.formattedColumns = value
    },
  )

  return {
    classes,
    ...toRefs(state),
    columnsType,
    columnsList,
    columnFieldNames,
    cancel,
    changeHandler,
    confirmHandler,
    confirm,
    defaultValues,
    defaultIndexes,
    delayDefaultIndexes,
    columnRefs,
    columnRef,
    selectedOptions,
  }
}
