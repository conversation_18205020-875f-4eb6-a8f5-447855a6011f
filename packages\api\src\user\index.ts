import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'

const { VITE_SDK_URL, VITE_ACTIVITY_URL, VITE_WECHAT_URL } = useEnv()

export type CardAuthParams = {
  /**
   * 身份证号码
   */
  card_id: string
  /**
   * 	姓名
   */
  card_name: string
}

/**
 * 实名认证
 * @param params CardAuthParams
 * @returns 
 */
export function cardAuth(params: CardAuthParams) {
  return http.post(`${VITE_SDK_URL}/web/card/auth`, params)
}

export type AllRoleInfoParams = {
  /**
   * 项目id，活动详情 init 中的 project_id=11(闪烁之光)，107是长安幻想
   */
  project_id: number
}

export type AllRoleInfoRoleList = {
  /**
   * 角色职业
   */
  career: number
  /**
   * 上一次登录时间
   */
  login_time: number
  /**
   * 最近xx天登录过
   */
  login_time_cut_of_year: string
  /**
   * 注册时间
   */
  reg_time: number
  /**
   * 角色id
   */
  role_id: number
  /**
   * 角色等级
   */
  role_level: number
  /**
   * 角色名称
   */
  role_name: string
  /**
   * 战力值
   */
  role_score: number
  /**
   * 会员等级
   */
  role_vip_lev: number
  /**
   * 性别
   */
  sex: number
  /**
   * 充值金额
   */
  total_amount: number
}[]

export type AllRoleInfo = {
  /**
   * 区服名称
   */
  server_name: string
  /**
   * 区服id
   */
  server_id: string
  /**
   * 角色列表
   */
  role_list: AllRoleInfoRoleList
}[]

/**
 * @description 获取玩家所有角色信息
 * @param {*} params.project_id 项目id，活动详情 init 中的 project_id=11(闪烁之光)，107是长安幻想
 * @returns
 */
export function postAllRoleInfo(params: AllRoleInfoParams) {
  const { project_id } = params
  return http.post<AllRoleInfo>(
    `${VITE_SDK_URL}/account/role/getNewAccAllRoleInfo`,
    {
      project_id: `${project_id}`,
    },
    {
      errToast: false,
    },
  )
}

export type BindWxIdentityParams = {
  /**
   * 活动账号数据 由getAccountInfo接口提供
   */
  act_acc_id: string
  /**
   * 活动id
   */
  act_id: number
  /**
   * 微信unionid
   */
  unionid: string
  /**
   * 微信openid
   */
  openid: string
  /**
   * openid类型，1公众号 2小程序
   */
  type?: number
}

/**
 * @description 提交sdk账号与微信关系
 */
export function bindWxIdentity(params: BindWxIdentityParams) {
  return http.post(`${VITE_ACTIVITY_URL}/redPacket/bindWxIdentity`, params, { needToken: false })
}

export type WebOauthParams = {
  /**
   * 积分商城调用授权时 , 固定为 : SY_MALL_[32位唯一随机标识],由前端自行生成,保证不重复,
   * 例如: SY_MALL_Ghdsn2349TGdsfoin23rd9s8fh8jsSis。SY_MALL：积分商城系统（诗悦游戏公众号授权）
   * SY_GAME_RECRUIT：体验官问卷（诗悦游戏共创社公众号授权）SY_WECHAT_CHARGE：微信web支付充值（诗悦游戏公众号授权）
   * MQ_REDPACK：微信红包发送（梦趣游戏公众号授权）
   */
  state: string
  /**
   * GET_OAUTH_URL=>以json的格式返回重定向的url, 不传=>则直接跳转到落地页
   */
  request_mode?: string
  /**
   * 由前端自行生成这个参数,最后应该表现形式为base64加密后的字符串,应该包含以下参数,并用&符号和=号赋值连起来，其中包含：
   * redirect_url 微信授权成功后,重定向跳转回去的url
   * step 前端的回调步骤,可选,自行处理重定向后的步骤
   * xx 可选更多的参数,自行传递,用&符和=号连起来,重定向的时候都会带上
   */
  redirect_info?: string
}

/**
 * 检查是否有关联的授权诗悦账号
 * @param data WebOauthParams
 * @returns 
 */
export function webOauth(data: WebOauthParams) {
  return http.post(`${VITE_WECHAT_URL}/wechat/webOauth`, data)
}

/**
 * @description 获取图形验证码
 * @param type 验证类型
 */
export function postWebCaptcha(type: string) {
  return http.post(`${VITE_SDK_URL}/web/captcha`, {
    type,
  })
}

export type LoginData = {
  /**
   * 标志是否实名：已手机或身份证绑定，在强制判断身份证绑定时候不能做唯一
   */
  is_real: boolean
  /**
   * 是否是新注册用户
   */
  is_reg: boolean
  /**
   * TOKEN
   */
  token: string
  /**
   * 绑定手机号码 没绑定则为空
   */
  phone_number: string
  /**
   * 账号id
   */
  account_id: string
  /**
   * 用户类型
   */
  user_type: string
  /**
   * 是否设置充值二级密码
   */
  isset_security: boolean
  /**
   * 	助手首次登陆标识,非助手统一返回false
   */
  ga_first_login: boolean
  /**
   * 	第三方授权绑定情况
   */
  oauth_bind_status: {
    alipay: false
    apple: false
    qq: false
    taptap: false
    wechat: false
  }
}

/**
 * @description 登录注册一体接口
 * @param phone 手机号
 * @param code 短信验证码
 * @param randstr 腾讯云验证码，验证成功的票据
 * @param ticket 腾讯云验证码，本次验证的随机串
 * @param randcode 登录注册失败返回的 1101（账号不存在）的 rand_code
 * @param source 来源标识 如客服：kf、社区：bbs
 */
export function postWebPhoneLogin({ phone, code, randstr, ticket, randcode, source }: {
  phone: string
  code: string
  randstr?: string
  ticket?: string
  randcode?: string
  source?: string
}) {
  return http.post<LoginData>(`${VITE_SDK_URL}/web/phoneLogin`, {
    phone_number: phone,
    code,
    ticket: ticket ? ticket : undefined,
    rand_str: randstr ? randstr : undefined,
    rand_code: randcode ? randcode: undefined,
    source: source ? source : undefined,
  }, {
    errToast: false,
  })
}

/**
 * @description 获取手机验证码
 * @param type 短信类型
 * @param phone 手机号
 * @param randstr 腾讯云验证码，验证成功的票据
 * @param ticket 腾讯云验证码，本次验证的随机串
 */
export function postWebSmsSend({ type, phone, randstr, ticket, projectId }: {
  type: string
  phone: string
  randstr?: string
  ticket?: string
  projectId: string
}) {
  return http.post(`${VITE_SDK_URL}/web/sms/send`, {
    sms_type: type,
    phone_number: phone,
    project_id: projectId,
    ticket: ticket ? ticket : undefined,
    rand_str: randstr ? randstr : undefined,
  })
}

/**
 * @description 账号登录
 * @param account 账号
 * @param password 密码
 * @param code 图形验证码，非必填
 * @param randstr 腾讯云验证码，验证成功的票据
 * @param ticket 腾讯云验证码，本次验证的随机串
 * @param source 来源标识 如客服：kf、社区：bbs
 */
export function postWebLogin({ account, password, code, randstr, ticket, source }: {
  account: string
  password: string
  code?: string
  randstr?: string
  ticket?: string
  source?: string
}) {
  return http.post<LoginData>(`${VITE_SDK_URL}/web/login`, {
    phone_number: account,
    password,
    code: code ? code : undefined,
    ticket: ticket ? ticket : undefined,
    rand_str: randstr ? randstr : undefined,
    source: source ? source : undefined,
  }, {
    errToast: false,
  })
}

// /**
//  * @description 手机是否注册
//  * @param phoneNumber 手机号
//  */
// export function postWebIsPhoneReg({ phoneNumber }, env) {
//   const baseUrl = userBaseUrlMap[env] || VITE_USER_URL
//   return request({
//     url: `${baseUrl}/web/isPhoneReg`,
//     method: 'post',
//     data: { phone_number: phoneNumber },
//   })
// }

export type HwRoleListParams = {
  /**
   * 项目ID
   */
  project_id: number
  /**
   * 活动id
   */
  act_id: number
}

/**
 * 获取海外用户区服角色列表
 * @param params HwRoleListParams
 * @returns 
 */
export function postHwRoleList(params: HwRoleListParams) {
  return http.post<AllRoleInfo>(`${VITE_ACTIVITY_URL}/activity/role_list`, params)
}

export type LoginTokenParams = {
  /**
   * 项目ID
   */
  token: string
}

export type LoginTokenData = {
  /**
   * 标志是否实名：已手机或身份证绑定，在强制判断身份证绑定时候不能做唯一
   */
  is_real: boolean
  /**
   * TOKEN
   */
  token: string
  /**
   * 绑定手机号码 没绑定则为空
   */
  phone_number: string
  /**
   * 账号id
   */
  account_id: string
  /**
   * 账号名
   */
  name: string
  /**
   * 用户类型
   */
  user_type: string
  /**
   * 是否设置充值二级密码
   */
  isset_security: boolean
  /**
   * 是否新注册用户
   */
  is_reg: boolean
}

/**
 * 获取海外用户区服角色列表
 * @param params LoginTokenParams
 * @returns 
 */
export function postLoginToken(params: LoginTokenParams) {
  return http.post<LoginTokenData>(`${VITE_SDK_URL}/web/token/loginToken`, params, { needToken: false })
}


export type CaptchaAppidParams = {
  /**
   * 验证码类型 普通验证：normal、最强验证码：strongest
   */
  captcha_type: 'normal' | 'strongest'
}

export type CaptchaAppidData = {
  /**
   * 加密的CaptchaAppid
   */
  encrypt_captcha_app_id: string
}

/**
 * 获取加密的CaptchaAppid
 * @param params CaptchaAppidParams
 * @returns 
 */
export function postCaptchaAppid(params: CaptchaAppidParams) {
  return http.post<CaptchaAppidData>(`${VITE_SDK_URL}/web/getCaptchaAppid`, params, { needToken: false })
}
