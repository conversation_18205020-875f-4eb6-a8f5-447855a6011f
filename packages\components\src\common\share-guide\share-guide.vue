<!--
* @Description: 微信小程序内嵌-引导右上角分享弹窗
-->
<template>
  <Overlay
    :show="show"
    z-index="99"
    @click="handleClose"
  >
    <div :style="contentStyle" class="share-guide-content">
      <img :src="share_guide_img" :style="imgStyle" class="share-guide-img" />
    </div>
  </Overlay>
</template>

<script lang="ts">
import type { CSSProperties } from 'vue';

export interface ShareGuideProps {
  /**
   * 是否显示
   */
  show: boolean
}

export default {
  name: 'share-guide',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import { pxTransform } from '@bish/utils/src/viewport';
import Overlay from '@bish/ui/src/overlay/index.vue';
import share_guide_img from './share-guide.png';

withDefaults(defineProps<ShareGuideProps>(), {
  show: false,
});

const emits = defineEmits<{
  (event: 'update:show', value: boolean): void;
}>();

const contentStyle = computed<CSSProperties>(() => {
  return {
    padding: `${pxTransform(20)} ${pxTransform(70)}`,
  };
});

const imgStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(200),
  };
});

const handleClose = () => {
  emits('update:show', false);
};
</script>

<style>
.share-guide-content {
  max-width: 375PX;
  margin: 0 auto;
  text-align: right;
}

.share-guide-img {}
</style>