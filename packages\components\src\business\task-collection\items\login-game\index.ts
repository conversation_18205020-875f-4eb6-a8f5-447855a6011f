import { commonTaskConfig } from '../index';
import { defaultConfig as downloadGameDefaultConfig } from '../../../download-game.vue';
import type { TaskLoginGameSetting } from './login-game';

export * from './login-game';

export const guideModalConfig = (): TaskLoginGameSetting['guideModal'] => {
  const downloadGameConfig = downloadGameDefaultConfig();
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 364,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730944699167-%E7%BB%84%2033.png',
    closeBtn: {
      x: 327,
      y: 5,
      width: 27,
      height: 27,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730944693773-%E5%85%B3%E9%97%AD%E6%8C%89%E9%92%AE%20%E6%8B%B7%E8%B4%9D%203.png',
    },
    tip: {
      x: 51,
      y: 84,
      width: 281,
      height: 61,
      fontSize: 15,
      color: '#674B7C',
      align: 'center',
      alignItems: 'center',
      // content: '请前往《闪烁之光》游戏内</br>在登录页面点击右侧【预约】完成预抽卡',
      content: '',
      fontWeight: true,
    },
    poster: {
      x: 65,
      y: 153,
      width: 246,
      height: 119,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730972365526-%E5%BC%95%E5%AF%BC%E6%B5%B7%E6%8A%A5.jpg',
    },
    visitGame: {
      x: 49,
      y: 285,
      width: 137,
      height: 46,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730944706364-BUTTON%E5%89%8D%E5%BE%80%E6%B8%B8%E6%88%8F.png',
      tip: '请前往《闪烁之光》登录参与预约抽卡~',
    },
    downloadGame: {
      ...downloadGameConfig,
      x: 190,
      y: 285,
      width: 137,
      height: 46,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730945404384-BUTTON%E4%B8%8B%E8%BD%BD%E6%B8%B8%E6%88%8F.png',
    },
  }
};

export const defaultDownloadGameConfig = (): TaskLoginGameSetting['downloadGame'] => {
  const downloadGameConfig = downloadGameDefaultConfig();
  return {
    ...downloadGameConfig,
    x: 248,
    y: 21,
    width: 84,
    height: 27,
  };
};

export const defaultConfig = (): TaskLoginGameSetting => {
  return {
    ...commonTaskConfig(),
    doBtn: {
      x: 248,
      y: 20,
      width: 84,
      height: 27,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241017/1729164224581-BUTTON%E5%8E%BB%E7%99%BB%E5%BD%95.png',
      enabled: true,
    },
    achievedBtn: {
      x: 248,
      y: 20,
      width: 84,
      height: 27,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250123/1737612696102-achieved-btn.png',
      enabled: false,
    },
    claimBtn: {
      x: 248,
      y: 20,
      width: 84,
      height: 27,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250120/1737375094345-join-claim.png',
      enabled: false,
    },
    actionType: 'toast',
    tip: '请登录游戏完成任务',
  }
};