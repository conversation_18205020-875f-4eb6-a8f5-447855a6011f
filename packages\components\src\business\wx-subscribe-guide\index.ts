import type { WxSubscribeGuideSetting } from './wx-subscribe-guide';

export * from './wx-subscribe-guide';

export const defaultConfig = (): WxSubscribeGuideSetting => {
  return {
    x: 0,
    y: 0,
    width: 375, // 这里的宽度没啥用，在用户端会被覆盖成 100vw
    height: 451, // 这里的高度没啥用，在用户端会被覆盖成 100vh
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241114/1731556851565-subscribe-tip-bg.png',
    closeBtn: {
      x: 313,
      y: 29,
      width: 30,
      height: 26,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241114/1731556883921-close-btn.png',
    },
    okBtn: {
      x: 102,
      y: 285,
      width: 171,
      height: 52,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241114/1731556848511-subscribe-btn.png',
    },
  };
};