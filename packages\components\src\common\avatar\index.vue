<template>
  <div class="avatar" :style="avatarStyle">
    <img class="avatar-img" v-if="avatarSrc" :src="avatarSrc" />
  </div>
</template>

<script lang="ts" setup>
import { withDefaults, ref, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { pxTransform } from '@bish/utils/src/viewport';

import cahx_head1and0 from './images/cahx/head1-0.png';
import cahx_head1and1 from './images/cahx/head1-1.png';
import cahx_head2and0 from './images/cahx/head2-0.png';
import cahx_head2and1 from './images/cahx/head2-1.png';
import cahx_head3and0 from './images/cahx/head3-0.png';
import cahx_head3and1 from './images/cahx/head3-1.png';
import cahx_head4and0 from './images/cahx/head4-0.png';
import cahx_head4and1 from './images/cahx/head4-1.png';
import cahx_head5and0 from './images/cahx/head5-0.png';
import cahx_head5and1 from './images/cahx/head5-1.png';
import cahx_head6and0 from './images/cahx/head6-0.png';
import cahx_head6and1 from './images/cahx/head6-1.png';
import cahx_head7and0 from './images/cahx/head7-0.png';
import cahx_head7and1 from './images/cahx/head7-1.png';
import cahx_head8and0 from './images/cahx/head8-0.png';
import cahx_head8and1 from './images/cahx/head8-1.png';
import cahx_head9and0 from './images/cahx/head9-0.png';
import cahx_head9and1 from './images/cahx/head9-1.png';

export interface AvatarProps {
  /**
   * 宽度，默认 38
   */
  width?: number
  /**
   * 高度，默认 38
   */
  height?: number
  /**
   * 游戏，默认 cahx
   */
  game?: string
  /**
   * 职业
   */
  career?: string | number
  /**
   * 性别
   */
  sex?: string | number
  /**
   * 默认头像，会在匹配不到的时候展示
   */
  defaultAvatar?: string
}

const avatarMap: Record<string, Array<string[]>> = {
  cahx: [
    [cahx_head1and0, cahx_head1and1],
    [cahx_head2and0, cahx_head2and1],
    [cahx_head3and0, cahx_head3and1],
    [cahx_head4and0, cahx_head4and1],
    [cahx_head5and0, cahx_head5and1],
    [cahx_head6and0, cahx_head6and1],
    [cahx_head7and0, cahx_head7and1],
    [cahx_head8and0, cahx_head8and1],
    [cahx_head9and0, cahx_head9and1],
  ],
};

const props = withDefaults(defineProps<AvatarProps>(), {
  width: 38,
  height: 38,
  game: 'cahx',
  career: '',
  sex: '',
  defaultAvatar: '',
});

const avatarSrc = computed(() => {
  const careerIndex = +props.career - 1;
  return avatarMap[props.game]?.[careerIndex]?.[+props.sex] || props.defaultAvatar;
});

const avatarStyle = computed(() => {
  return {
    width: pxTransform(props.width),
    height: pxTransform(props.height),
  } as CSSProperties;
});

</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
  name: 'common-popup',
}
</script>

<style>
.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  overflow: hidden;
}

.avatar-img {
  width: 82%;
  height: auto;
}
</style>