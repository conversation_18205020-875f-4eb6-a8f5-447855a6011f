import type { WeSubscribeModalSetting } from './we-subscribe-modal';

export * from './we-subscribe-modal';

export const defaultConfig = (): WeSubscribeModalSetting => {
  return {
    modalSetting: {
      x: 0,
      y: 0,
      width: 375, // 这里的宽度没啥用，在用户端会被覆盖成 100vw
      height: 667, // 这里的高度没啥用，在用户端会被覆盖成 100vh
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241115/1731639109319-%E7%BB%84%20204.png',
      okBtn: {
        x: 84,
        y: 534,
        width: 206,
        height: 63,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241115/1731639118640-%E5%89%8D%E5%BE%80%E8%AE%A2%E9%98%85.png',
      },
    },
    templateList: [],
  };
};