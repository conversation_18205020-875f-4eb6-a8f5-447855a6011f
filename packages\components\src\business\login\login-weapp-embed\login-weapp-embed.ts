import type { ResizableProps } from '../../../ui/resizable/index.vue';
import type { PopupSetting } from '../../../common/popup.vue';
import type { TextSetting } from '../../../ui/text/index.vue';
export interface LoginWeappEmbedSetting {
  /**
   * 弹窗基础配置
   */
  modalSetting: PopupSetting
  /**
   * 用户隐私协议配置
   */
  agreement: {
    /**
     * 位置、大小信息
     */
    position: ResizableProps
    /**
     * 是否展示
     */
    show: boolean
    /**
     * 字体颜色
     */
    textColor: string
    /**
     * 协议颜色
     */
    linkColor: string
  }
  /**
   * 文案配置
   */
  content: TextSetting
}
