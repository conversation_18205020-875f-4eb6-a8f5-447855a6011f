import { postActivityBindAccRole } from '@bish/api/src/activity';
import type { AllRoleInfoRoleList } from '@bish/api/src/user';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useActivityStore from '@bish/store/src/modules/activity';

export type BindServerInfo = {
  server_id: string,
  server_name: string,
}

export type BindRoleInfo = AllRoleInfoRoleList[0]

export type ServerRoleValue = {
  /**
   * 区服
   */
  server: string[]
  /**
   * 角色
   */
  role: number[]
}

export default function useBindRole(
  successCallback?: () => void,
) {
  const activityPageStore = useActivityPageStore();
  const activityStore = useActivityStore();
  
  // 绑定游戏角色
  const bindActivityAccRole = async (
    server: BindServerInfo,
    role: BindRoleInfo,
  ) => {
    if (!server || !role) {
      return;
    }
    try {
      const platform = server.server_id.split('_')[0];
      const zoneId = server.server_id.split('_')[1];
      const res = await postActivityBindAccRole({
        act_id: activityPageStore.activityPageConfig.activity_id,
        act_acc_id: activityStore.activityAccountInfo.act_acc_id ? `${activityStore.activityAccountInfo.act_acc_id}` : undefined!,
        platform,
        zone_id: +zoneId,
        role_id: role.role_id,
        role_name: role.role_name,
        server_name: server.server_name,
        role_score: role.role_score,
        vip: role.role_vip_lev,
        role_level: role.role_level,
        reg_time: `${role.reg_time}`,
        career: role.career,
        sex: role.sex,
        last_login_time: role.login_time,
      });
      if (res?.code === 0) {
        // 执行成功回调
        successCallback?.();
        // 重新获取相关数据
        activityStore.getActivityAccountInfo();
      }
    } catch (error) {
      console.warn('绑定角色失败', error);
    }
  }

  const handleConfirm = (value: ServerRoleValue) => {
    const { server, role } = value;
    let curServer = null;
    let curRole = null;
    if (activityStore.allRoleInfo) {
      const current = activityStore.allRoleInfo.find(item => item.server_id === server[0]);
      if (current) {
        curServer = {
          server_id: current.server_id,
          server_name: current.server_name,
        };
      }
      if (current) {
        curRole = current.role_list.find(item => item.role_id === role[0]);
      }
    }
    if (curServer && curRole) {
      bindActivityAccRole(curServer, curRole);
    }
  };

  return {
    bindRole: handleConfirm,
  }
}
