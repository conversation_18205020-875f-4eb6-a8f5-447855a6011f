<template>
  <Popup
    v-model:setting="setting.modalSetting"
    z-index="99"
    v-bind="$attrs"
    @ok="() => emits('ok')"
  >
    <Resizable
      v-model:x="setting.container.x"
      v-model:y="setting.container.y"
      v-model:width="setting.container.width"
      v-model:height="setting.container.height"
      class="reward-root"
    >
      <template v-if="renderList.length">
        <template v-for="item in renderList" :key="item.id">
          <!-- <ResizableProvider></ResizableProvider> -->
          <ResizableProvider class="reward-item" :style="itemStyle">
            <ui-text v-model:setting="setting.item.name" :interpolation="{ item_name: item.item_name }" />
            <ui-text v-model:setting="setting.item.time" :interpolation="{ time: item.created_at }" />

            <Resizable
              v-model:x="setting.item.status.position.x"
              v-model:y="setting.item.status.position.y"
              v-model:width="setting.item.status.position.width"
              v-model:height="setting.item.status.position.height"
            >
              <div v-if="item.item_type === 1" :style="statusTextStyle">
                {{ getStatusText(item.item_type, item.send_status as number) }}
              </div>
              <img v-else class="reward-status-img" :src="item.send_status === 2 ? setting.item.status.success: setting.item.status.fail" />
            </Resizable>
          </ResizableProvider>
        </template>
      </template>
      <div :style="emptyStyle" class="reward-empty" v-else>
        {{ $t('reward-records-empty', '暂无中奖记录~') }}
      </div>
    </Resizable>

    <!-- 填写地址入口 -->
    <template v-if="hasPhysicalItem">
      <UiImage
        v-if="hasPhysical && setting.address?.imgLink"
        v-model:setting="setting.address"
        @click="handleShowAddressForm"
      />
    </template>
  </Popup>
</template>

<script lang="ts">
import type { ResizableProps } from '../../ui/resizable/index.vue'
import type { PopupSetting } from '../../common/popup.vue'
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import usePopupStore from '@bish/store/src/modules/popup';
import { pxTransform } from '@bish/utils/src/viewport';
import type { CommonImageSetting } from '@bish/types/src'

export interface RewardModalSetting {
  modalSetting: PopupSetting // 弹窗基础配置
  container: ResizableProps
  item: {
    height?: number
    margin?: number
    background?: string
    name: TextSetting
    time: TextSetting
    status: {
      position: ResizableProps
      success: string
      fail: string
    }
    /**
     * 背景图片
     */
    bgImage: string
  }
  /**
   * 道具类型
   */
  prizeTypes: number[]
  /**
   * 填写地址
   */
  address?: CommonImageSetting
}

export interface RewardModalProps {
  setting: RewardModalSetting
}

export const defaultAddressConfig = (): CommonImageSetting => {
  return {
    x: 246,
    y: 48,
    width: 56,
    height: 12,
    imgLink: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/20241203/1733206502213-address.png',
  };
};

export const defaultConfig = (): RewardModalSetting => ({
  modalSetting: {
    x: 0,
    y: 0,
    width: 375,
    height: 471,
    bgImage: "https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1723020485138_.png",
    okBtn: {
      x: 116,
      y: 370,
      width: 143,
      height: 49,
      imgLink: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1722967840752_.png',
    },
    closeBtn: {
      x: 330,
      y: -5,
      width: 33,
      height: 33,
      imgLink: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1722967853959_.png',
    },
  },
  container: {
    x: 45,
    y: 93,
    width: 285,
    height: 270,
  },
  item: {
    height: 56,
    margin: 10,
    background: '#172036',
    name: {
      x: 10,
      y: 9,
      width: 185,
      height: 25,
      fontSize: 16,
      color: '#EFDDAE',
      content: '{{item_name}}'
    },
    time: {
      x: 10,
      y: 35,
      width: 160,
      height: 18,
      fontSize: 12,
      color: '#EFDDAE',
      content: '{{time}}'
    },
    status: {
      position: {
        x: 209,
        y: 17,
        width: 65,
        height: 24,
      },
      success: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1723020605403_.png',
      fail: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1723021018944_.png',
    },
    bgImage: '',
  },
  prizeTypes: [],
})

export default {
  name: 'RewardModal',
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  }
}
</script>
<script setup lang="ts">
import { computed } from 'vue'
import type { CSSProperties } from 'vue'
import Popup from '../../common/popup.vue'
import Resizable from '../../ui/resizable/index.vue';
import ResizableProvider from '../../ui/resizable/resizable-provider.vue';
import { PRIZE_TYPE_PHYSICAL } from '../../__constants/prize';
import UiText, { type TextSetting } from '../../ui/text/index.vue'
import UiImage from '../../ui/image/index.vue'

const activityStore = useActivityStore()
const adminStore = useAdminStore()
const popupStore = usePopupStore()

const props = withDefaults(defineProps<RewardModalProps>(), {
  setting: defaultConfig,
})
const emits = defineEmits<{
  (event: 'update:setting', value: RewardModalSetting): void;
  (event: 'ok'): void;
}>();

const setting = useVModel(props, 'setting', emits);

const itemStyle = computed<CSSProperties>(() => ({
  height: pxTransform(setting.value.item.height!),
  marginBottom: pxTransform(setting.value.item.margin!),
  backgroundColor: setting.value.item.background,
  backgroundImage: `url(${setting.value.item.bgImage})`,
}))
const emptyStyle = computed<CSSProperties>(() => ({
  color: setting.value.item.name.color,
  fontSize: pxTransform(setting.value.item.name.fontSize!),
}))
const statusTextStyle = computed<CSSProperties>(() => ({
  color: setting.value.item.time.color,
  fontSize: pxTransform(setting.value.item.name.fontSize!),
  textAlign: 'center',
}))
const renderList = computed(() => {
  if (adminStore.editable) {
    return [
      { created_at: '2024-08-05 14:17:55', id: 1, item_type: 2, item_name: '礼包商品名称1', send_status: 2 },
      { created_at: '2024-08-05 14:18:55', id: 1, item_type: 1, item_name: '实物奖励', send_status: 3 },
    ]
  }
  return activityStore.componentWithUserInfo?.award_list || []
})

const hasPhysical = computed(() => {
  return setting.value.prizeTypes?.includes(PRIZE_TYPE_PHYSICAL);
})

const hasPhysicalItem = computed(() => {
  return renderList.value.find(item => item.item_type === PRIZE_TYPE_PHYSICAL);
})

const userAddress = computed(() => {
  return activityStore.activityAccountInfo?.extra?.address_info || null
})

const handleShowAddressForm = () => {
  popupStore.setShowAddressForm(true);
}

const getStatusText = (type: number, status: number) => {
  let text = ''
  switch (status) {
    case 1:
      text = type === 1 && !userAddress.value ? '待填写' : '发放中'
      break
    case 2:
      text = '已发放'
      break
    default:
      text = '发放失败'
      break
  }
  if (type === 5) {
    text = '前往钱包查看'
  }
  return text
}
</script>

<style lang="less" scoped>
.reward-root {
  overflow-y: auto;
  overflow-x: hidden;
}
.reward-item {
  width: 100%;
  position: relative;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.reward-status-img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}
.reward-empty {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  text-align: center;
  white-space: nowrap;
}
</style>
