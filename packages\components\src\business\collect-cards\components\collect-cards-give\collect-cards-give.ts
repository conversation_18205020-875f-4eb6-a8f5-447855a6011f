import type { CommonSetting } from '@bish/types/src';
import type { PopupSetting } from '../../../../common/popup.vue';
import type { TextSetting } from '../../../../ui/text/index.vue';
import type { ImageSetting } from '../../../../ui/image/index.vue';

export interface CollectCardsGiveSetting {
  /**
   * 入口按钮
   */
  actionBtn: ImageSetting
  /**
   * 赠送弹窗
   */
  modal: PopupSetting & {
    /**
     * 标题
     */
    title: ImageSetting
    /**
     * 卡片盒子
     */
    box: ImageSetting
    /**
     * 卡片
     */
    card: CommonSetting
    /**
     * 卡片名称
     */
    name: TextSetting
  }
  /**
   * 已拥有弹窗
   */
  ownedModal: PopupSetting & {
    /**
     * 标题
     */
    title: ImageSetting
    /**
     * 卡片盒子
     */
    box: ImageSetting
    /**
     * 卡片
     */
    card: CommonSetting
    /**
     * 卡片名称
     */
    name: TextSetting
    /**
     * 提示
     */
    tip: TextSetting
  }
  /**
   * 已领取弹窗
   */
  claimedModal: PopupSetting & {
    /**
     * 标题
     */
    title: ImageSetting
    /**
     * 图标
     */
    icon: ImageSetting
  }
  /**
   * 复制模板列表
   */
  copyTemplates: {
    /**
     * 卡片id
     */
    cardId: number
    /**
     * 标题文案
     */
    text: string
    /**
     * 海报，目前只有小程序页面分享才用到
     */
    poster?: string
  }[]
} 
