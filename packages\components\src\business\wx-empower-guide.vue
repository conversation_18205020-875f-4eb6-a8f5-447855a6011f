<!--
* @Description: 微信授权指引-弹窗
-->
<template>
  <Popup
    z-index="99"
    :show="show"
    :lock-scroll="false"
    v-model:setting="setting.modalSetting"
    @close="handleClose"
    @ok="handleCopyLink"
  >
    <!-- 内容文案 -->
    <UiText v-if="setting.modalSetting.content?.content" v-model:setting="setting.modalSetting.content" class="wx-empower-guide-content" />
  </Popup>
</template>

<script lang="ts">
import type { StatefulComponent } from '@bish/types/src/admin';
import type { TextSetting } from '../ui/text/index.vue';
import type { PopupSetting } from '../common/popup.vue';

export interface WxEmpowerGuideModalSetting extends PopupSetting {
  /**
   * 内容
   */
  content: TextSetting
}

export interface WxEmpowerGuideSetting {
  /**
   * 弹窗
   */
  modalSetting: WxEmpowerGuideModalSetting
}

export interface WxEmpowerGuideProps {
  /**
   * 配置
   */
  setting: WxEmpowerGuideSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): WxEmpowerGuideSetting => {
  return {
    modalSetting: {
      x: 0,
      y: 0,
      width: 375,
      height: 343,
      bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724917969520_.png',
      okBtn: {
        x: 86,
        y: 248,
        width: 205,
        height: 57,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724917973229_.png',
      },
      content: {
        x: 74,
        y: 142,
        width: 229,
        height: 60,
        fontSize: 12,
        color: '#732508',
        align: 'center',
        content: '为了保证您的参与体验，请点击下方复制链接按钮，并在微信环境内打开链接~',
      },
      closeBtn: {
        x: 315,
        y: 31,
        width: 19,
        height: 19,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724917975880_.png',
      },
    },
  };
};

export default {
  name: 'wx-empower-guide',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useAdminStore from '@bish/store/src/modules/admin';
import usePopupStore from '@bish/store/src/modules/popup';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import useShortLink from '@bish/hooks/src/business/useShortLink';
import { useAccountBindWx } from '@bish/hooks/src/business/useAccountBindWx';
import { copyText } from '../__utils/text';
import UiText from '../ui/text/index.vue';
import Popup from '../common/popup.vue';

const props = withDefaults(defineProps<WxEmpowerGuideProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: WxEmpowerGuideSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const adminStore = useAdminStore();
const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);

const [show, toggleShow] = useControllableStatus(props, emits, { fieldName: 'showWxEmpowerGuide' });

const { getRedirectWxAuthUrl } = useAccountBindWx();
const { shortLink, getShortLink } = useShortLink();

/**
 * 创建短链
 */
const createShortLink = () => {
  const wxAuthUrl = getRedirectWxAuthUrl();
  getShortLink(wxAuthUrl);
};

watch(
  () => popupStore.showWxEmpowerGuide,
  (newVal) => {
    toggleShow(newVal);
  },
);

watch(
  () => show.value,
  (newVal) => {
    if (newVal && !adminStore.editable) {
      createShortLink();
    }
  },
  { immediate: true },
);

const handleClose = () => {
  if (!popupStore.showWxEmpowerGuide) {
    toggleShow(false);
  }
  popupStore.setShowWxEmpowerGuide(false);
};

const handleCopyLink = () => {
  if (!shortLink.value) {
    showToast('链接复制失败，请稍后重试！');
  } else {
    copyText(shortLink.value, { success: '链接已复制~', error: '复制失败' });
  }
}
</script>

<style lang="less">
.wx-empower-guide-content {
  font-weight: 600;
  line-height: 1.6;
}
</style>