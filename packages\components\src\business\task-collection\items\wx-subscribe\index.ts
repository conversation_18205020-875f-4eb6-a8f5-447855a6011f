import { commonTaskConfig } from '../index';
import type { TaskWxSubscribeSetting } from './wx-subscribe';

export * from './wx-subscribe';

export const defaultConfig = (): TaskWxSubscribeSetting => {
  return {
    ...commonTaskConfig(),
    process: {
      x: 265,
      y: 12,
      width: 20,
      height: 20,
      fontSize: 9,
      color: '#684975',
      content: '{{process}}',
    },
    width: 290,
    height: 34,
    taskId: 0,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732104726278-%E6%B6%88%E6%81%AF%E8%AE%A2%E9%98%85.png',
    claimBtn: {
      x: 197,
      y: 6,
      width: 61,
      height: 22,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732104722696-%E5%8E%BB%E8%AE%A2%E9%98%85.png',
    },
    achievedBtn: {
      x: 197,
      y: 6,
      width: 61,
      height: 22,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732105234700-%E5%B7%B2%E5%AE%8C%E6%88%90.png',
    },
    wxSubscribeTemp: [],
    wxSubscribeGuide: {
      x: 0,
      y: 0,
      width: 375,
      height: 204,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732069833551-bg.png',
      closeBtn: {
        x: 327,
        y: 0,
        width: 27,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732069799956-close.png',
      },
      okBtn: {
        x: 119,
        y: 127,
        width: 137,
        height: 46,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732069830798-btn.png',
      },
    },
    wxSubscribe: {
      x: 0,
      y: 0,
      width: 375,
      height: 186,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241124/1732442919278-wx-subscribe-bg.png',
      closeBtn: {
        x: 327,
        y: 2,
        width: 27,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732069799956-close.png',
      },
      okBtn: {
        x: 119,
        y: 103,
        width: 137,
        height: 46,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241124/1732442922167-wx-subscribe-btn.png',
      },
    },
  }
};