import type { CollectCardsScratchSetting } from './collect-cards-scratch';

export * from './collect-cards-scratch';

export const defaultConfig = (): CollectCardsScratchSetting => {
  return {
    x: 0,
    y: 426,
    width: 375,
    height: 382,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744289519671-scratch-bg.png',
    closeBtn: {
      x: 128,
      y: 352,
      width: 119,
      height: 30,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250414/1744623187842-scratch-close.png',
    },
    title: {
      x: 56,
      y: -29,
      width: 264,
      height: 27,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744289550581-scratch-title.png',
      enabled: true,
    },
    box: {
      x: 78,
      y: 5,
      width: 217,
      height: 303,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744363647725-card-box.png',
      enabled: true,
    },
    card: {
      x: 94,
      y: 14,
      width: 186,
      height: 287,
    },
    scratch: {
      x: 130,
      y: 244,
      width: 110,
      height: 39,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744288947445-scratch.png',
      enabled: true,
    },
    name: {
      x: 0,
      y: 316,
      width: 375,
      height: 20,
      fontSize: 13,
      color: '#FFF8DC',
      align: 'center',
      fontWeight: true,
      enabled: true,
    },
    congrats: {
      x: 94,
      y: 14,
      width: 186,
      height: 287,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250412/1744446825066-congrats-bg.png',
      prizeImg: {
        x: 61,
        y: 84,
        width: 64,
        height: 64,
        show: true,
      },
      prizeName: {
        x: 0,
        y: 156,
        width: 186,
        height: 35,
        fontSize: 9,
        color: '#FFFCFB',
        align: 'center',
        content: '{{prizeName}}',
      },
      tip: {
        x: 0,
        y: 196,
        width: 186,
        height: 36,
        fontSize: 9,
        color: '#6C3D01',
        align: 'center',
        alignItems: 'center',
      },
      gameItemTip: '<div style="line-height: 1.8;">奖励将于72小时内发放</br>后续可到<strong>【我的奖励】</strong>处查看我获得的奖励</div>',
      redPacketTip: '<div style="line-height: 1.8;">红包可到【我的钱包】处进行提现</br>提现后72小时内到账</div>',
      otherTip: '请添加企微领取奖品',
    },
    congratsMask: {
      x: 94,
      y: 14,
      width: 186,
      height: 287,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250417/1744878005878-congrats-mask.jpg',
      enabled: true,
    },
    scratchBg: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250418/1744955837568-stratch-bg.png',
  };
}; 
