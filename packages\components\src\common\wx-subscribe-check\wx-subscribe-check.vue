<!--
* @Description: 微信小程序-引导 勾选总是保持以上选择，不再询问
-->
<template>
  <Overlay
    :show="show"
    z-index="99"
    @click="handleClose"
  >
    <div class="wx-subscribe-check-content">
      <img :src="wx_subscribe_check_img" :style="imgStyle" class="wx-subscribe-check-img" />
      <view :style="textStyle" class="wx-subscribe-check-text">
        勾选总是保持选择，及时接收消息
      </view>
    </div>
  </Overlay>
</template>

<script lang="ts">
import type { CSSProperties } from 'vue';

export interface ShareGuideProps {
  /**
   * 是否显示
   */
  show: boolean
}

export default {
  name: 'wx-subscribe-check',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import { pxTransform } from '@bish/utils/src/viewport';
import Overlay from '@bish/ui/src/overlay/index.vue';
import wx_subscribe_check_img from './wx-subscribe-check.png';

withDefaults(defineProps<ShareGuideProps>(), {
  show: false,
});

const emits = defineEmits<{
  (event: 'update:show', value: boolean): void;
  (event: 'close'): void;
}>();

const imgStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(309),
    height: pxTransform(154.5),
  };
});

const textStyle = computed<CSSProperties>(() => {
  return {
    marginTop: pxTransform(15),
    fontSize: pxTransform(14),
  };
});

const handleClose = () => {
  emits('update:show', false);
  emits('close');
};
</script>

<style>
.wx-subscribe-check-content {
  padding-top: 16vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.wx-subscribe-check-img {
  display: block;
}

.wx-subscribe-check-text {
  font-weight: bold;
  color: #FFFFFf;
  text-align: center;
}
</style>