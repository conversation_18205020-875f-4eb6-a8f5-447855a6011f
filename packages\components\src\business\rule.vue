<template>
  <Resizable
    class="rule"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
    @click="showModal"
  >
    <Popup
      z-index="99"
      :show="show"
      :lock-scroll="false"
      :resizable="false"
      v-model:setting="setting.modal"
      @close="handleClose"
      @ok="handleClose"
    >
      <RuleBox v-model:setting="setting.modal" />
    </Popup>
  </Resizable>
</template>

<script lang="ts">
import type { CommonBackgroundSetting } from '@bish/types/src'
import type { StatefulComponent } from '@bish/types/src/admin';
import type { PopupSetting } from '../common/popup.vue';
import type { RuleBoxSetting } from './rule-box.vue';

export interface RuleSetting extends CommonBackgroundSetting {
  /**
   * 弹窗配置
   */
  modal: RuleBoxSetting & PopupSetting
}

export interface RuleProps {
  /**
   * 配置
   */
  setting: RuleSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): RuleSetting => {
  return {
    x: 0,
    y: 0,
    width: 21,
    height: 54,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719396462306_.png',
    modal: {
      x: 0,
      y: 0,
      width: 373,
      height: 438,
      bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719396497138_.png',
      okBtn: {
        x: 128,
        y: 355,
        width: 121,
        height: 34,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719396520604_.png',
      },
      content: {
        x: 52,
        y: 93,
        width: 269,
        height: 250,
      },
      closeBtn: {
        x: 324,
        y: -35,
        width: 35,
        height: 35,
        imgLink: 'https://cms-1256453865.cos.ap-shanghai.myqcloud.com/public/2024-07-31/1722414059018-%E5%85%B3%E9%97%AD%20%E6%8B%B7%E8%B4%9D%202.png'
      },
    },
  };
};

export default {
  name: 'rule',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults } from 'vue';
import { useVModel } from '@vueuse/core';
import useAdminStore from '@bish/store/src/modules/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import Resizable from '../ui/resizable/index.vue';
import Popup from '../common/popup.vue';
import RuleBox from './rule-box.vue';

const props = withDefaults(defineProps<RuleProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: RuleSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const [show, toggleShow] = useControllableStatus(props, emits, { fieldName: 'showPopup' });

const showModal = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }
  toggleShow(true);
};

const handleClose = () => {
  toggleShow(false);
};
</script>

<style lang="less">
.rule {
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-content {
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;
    text-align: justify;
    word-break: break-all;

    p {
      margin: 0;
      display: inline-block;
    }

    span {
      white-space: normal !important;
    }

    &-empty {
      text-align: center;
    }
  }
}
</style>