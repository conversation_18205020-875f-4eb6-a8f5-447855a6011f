<!--
* @Description: 预约抽卡-抽卡次数
-->
<template>
  <UiText
    v-model:setting="setting"
    :interpolation="interpolation"
    class="gacha-num"
  />
</template>

<script lang="ts">
export default {
  name: 'gacha-num',
}
</script>

<script lang="ts" setup>
import { computed, watch, ref } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useActivityStore from '@bish/store/src/modules/activity';
import type { StatefulComponent } from '@bish/types/src/admin';
import { postGameDrawNum } from '@bish/api/src/game';
import UiText from '../../../ui/text/index.vue';
import { defaultConfig } from './index';
import type { GachaNumSetting } from './gacha-num';

export interface GachaBNumProps {
  /**
   * 配置
   */
  setting: GachaNumSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<GachaBNumProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: GachaNumSetting): void;
}>();

const num = ref(0);

const activityPageStore = useActivityPageStore();
const activityStore = useActivityStore();

const setting = useVModel(props, 'setting', emits);

const interpolation = computed(() => {
  return {
    num: num.value ?? '',
  };
});

const fetchGameDrawNum = async () => {
  try {
    const res = await postGameDrawNum({
      activity_id: activityStore.activityInfo.init?.id,
      act_acc_id: activityStore.activityAccountInfo.act_acc_id,
    });
    if (res.code === 0) {
      num.value = res.data.recruit_num;
    }
  } catch (error) {
    console.warn('获取游戏抽卡数失败', error);
  }
};

watch(
  () => activityStore.componentWithUserInfo,
  (newVal) => {
    if (newVal.component_with_user_info) {
      fetchGameDrawNum();
    } else {
      num.value = 0;
    }
  },
);
</script>

<style>
.gacha-num {
  font-weight: 500;
}
</style>
