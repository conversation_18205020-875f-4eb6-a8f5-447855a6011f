import type { CommonSetting, CommonBackgroundSetting } from '@bish/types/src';
import type { TextSetting } from '../../../../ui/text/index.vue';
import type { ImageSetting } from '../../../../ui/image/index.vue';
import type { CollectCardsItemSetting } from '../collect-cards-item';

export interface CollectCardsListSetting extends CommonSetting {
  /**
   * 列表项容器
   */
  item: CommonSetting
  /**
   * 列表项卡片
   */
  itemCard: CollectCardsItemSetting
  /**
   * 列表项卡片名称
   */
  name: TextSetting
  /**
   * 列表项卡片徽标
   */
  badge: TextSetting & {
    /**
     * 徽标背景图
     */
    bgImage: string
  }
} 
