import type { TaskCollectionCommon } from '../index';
import type { ImageSetting } from '../../../../ui/image/index.vue';
import type { InviteModalSetting } from '../../../invite-modal.vue';
import type { InvitationModalSetting } from '../../../invitation-modal.vue';
import type { PopupSetting } from '../../../../common/popup.vue';

/**
 * 任务-邀请好友
 */
export type TaskInviteFriendsSetting = TaskCollectionCommon & {
  /**
   * 去邀请按钮
   */
  claimBtn: ImageSetting
  /**
   * 邀请好友弹窗
   */
  inviteCard: InviteModalSetting
  /**
   * 接受好友邀请弹窗
   */
  invitationCard: InvitationModalSetting
  /**
   * 任务未完成提示
   */
  notAccomplishTip?: string
  /**
   * 任务未完成跳转
   */
  notAccomplishUrl?: string
  /**
   * 分享提示
   */
  shareTips: PopupSetting & {
    /**
     * 是否开启，0不开启 1开启，不开启则不展示
     */
    open: number
    /**
     * 操作按钮
     */
    actionBtn: ImageSetting
  }
}