<template>
  <div
    class="download-game"
    :class="{ 'download-game-affix': setting.affix && !adminStore.editable }"
  >
    <UiImage
      v-model:setting="setting"
      v-bind="attrs"
      @click="handleDownload"
    />

    <!-- 微信小程序环境 -->
    <button
      v-if="isWeapp"
      :style="wxBtnStyle"
      class="download-game-wx-btn"
      open-type="contact"
      :session-from="session.form"
      @click="uploadDownloadLog"
    />
  
    <Popup
      v-if="setting.weappEmbedSetting?.modalSetting"
      :show="openWeappEmbed"
      v-model:setting="setting.weappEmbedSetting.modalSetting"
      z-index="101"
      :resizable="false"
      @close="() => toggleWeappEmbed(false)"
    >
    </Popup>
  </div>
</template>

<script lang="ts">
import type { CommonImageSetting } from '@bish/types/src'
import type { StatefulComponent } from '@bish/types/src/admin';
import type { PopupSetting } from '../common/popup.vue';

export interface DownloadWeappEmbedSetting {
  /**
   * 弹窗基础配置
   */
  modalSetting: PopupSetting
}

export interface DownloadGameSetting extends CommonImageSetting {
  /**
   * 是否需要引导，有些活动跳转页自带微信环境判断，不需要引导，默认 true
   */
  guided: boolean
  /**
   * 是否固定在页面，默认 false，设置后将使用 fixed 布局
   */
  affix?: boolean
  /**
   * 是否内嵌微信小程序，1是 0否
   */
  weappWebview: number
  /**
   * 内嵌中间页配置
   */
  weappEmbedSetting?: DownloadWeappEmbedSetting
}

export interface DownloadGameProps {
  /**
   * 配置
   */
  setting: DownloadGameSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
  /**
   * 被曝光的资源类型，1弹窗 2悬浮，默认 2
   */
  exposureType?: number
}

export const defaultWeappEmbedConfig = (): DownloadWeappEmbedSetting => {
  return {
    modalSetting: {
      x: 0,
      y: 0,
      width: 375, // 这里的宽度没啥用，在用户端会被覆盖成 100vw
      height: 667, // 这里的高度没啥用，在用户端会被覆盖成 100vh
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241114/1731550320159-%E7%BB%84%201.png',
      okBtn: {
        x: 98,
        y: 598,
        width: 178,
        height: 54,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241113/1731477073072-%E7%BB%84%2066.png',
      },
    },
  };
};

export const defaultConfig = (): DownloadGameSetting => {
  return {
    x: 238,
    y: 14,
    width: 121,
    height: 33,
    imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713154465674_.png',
    guided: true,
    affix: false,
    weappWebview: 0,
  };
};

export default {
  name: 'download-game',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, useAttrs } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useActivityStore, { GAME_CIRCLE } from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { useLog } from '@bish/hooks/src/useLog';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus'
import { userAgent, additionalLinkParameters } from '@bish/utils/src/utils';
import { pxTransform } from '@bish/utils/src/viewport';
import { showToast } from '@bish/ui/src/toast';
import { downloadGame } from '../__utils';
import UiImage from '../ui/image/index.vue';
import Popup from '../common/popup.vue';

const props = withDefaults(defineProps<DownloadGameProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: DownloadGameSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const attrs = useAttrs();
const setting = useVModel(props, 'setting', emits);

const activityPageStore = useActivityPageStore();
const activityStore = useActivityStore();
const adminStore = useAdminStore();

const [openWeappEmbed, toggleWeappEmbed] = useControllableStatus(props, emits, { fieldName: 'showWeappEmbed' });

const { uploadLog } = useLog();

const { isUserAgentType } = userAgent();

// 微信小程序环境
const isWeapp = computed(() => {
  return isUserAgentType === 'WX_MINI';
});

const session = computed(() => {
  const { init } = activityStore.activityInfo;
  return {
    form: JSON.stringify({ project_id: init?.project_id, event_type: 'download_game' }),
  };
});

const gameConfig = computed(() => {
  return activityStore.activityInfo.config.game;
});

const wxBtnStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.x || 0),
    top: pxTransform(setting.value.y || 0),
    width: pxTransform(setting.value.width || 0),
    height: pxTransform(setting.value.height || 0),
  };
});

const download = () => {
  // 下载页在iOS微信环境无法正常引导用户【长安春节裂变活动新增处理逻辑】
  downloadGame(gameConfig.value?.game_download_url, setting.value.guided);
};

/**
 * 跳转到小程序内嵌游戏下载页面
 */
const navigateWeappDownload = () => {
  const { init } = activityStore.activityInfo;
  const { themeColor } = activityPageStore.mergedPageConfig;
  
  let cfg = '';
  if (setting.value?.weappEmbedSetting) {
    cfg = JSON.stringify({
      ...setting.value?.weappEmbedSetting,
      theme: themeColor, // 附加主题色配置
    });
  }
  
  // TODO: 把小程序登录页面提到环境变量
  const weappBishDownloadUrl = '/subpkg/bish/download';
  const url = additionalLinkParameters(
    {
      cfg: encodeURIComponent(cfg),
      project_id: init?.project_id,
    },
    weappBishDownloadUrl,
  );

  wx?.miniProgram.navigateTo({
    url,
  });
};

const uploadDownloadLog = () => {
  // 事件上报--点击立即下载人数
  uploadLog({
    event_name: 'click',
    click_id: 2,
    click_type: 3,
    exposure_type: props.exposureType || 2,
  });
};

const handleDownload = () => {
  console.log('cfg cfg cfg', encodeURIComponent(JSON.stringify({
    ...setting.value?.weappEmbedSetting,
    theme: activityPageStore.mergedPageConfig.themeColor, // 附加主题色配置
  })))
  // 后台端编辑模式下，点击跳转链接时，弹出确认框，避免误操作
  if (adminStore.editable) {
    if (window.confirm('是否确认进行跳转？')) {
      download();
    }
    return
  }
  uploadDownloadLog();

  // 游戏预下载未开启
  const gameLifeCycle = activityStore.gameLifeCycle()
  if (gameLifeCycle === GAME_CIRCLE.PRELOADING_BEFORE) {
    showToast(`游戏将在${activityStore.gameDownloadTime}开放预下载`)
    return
  }

  // 微信小程序内嵌环境：跳转指定的登录页面
  if (isUserAgentType === 'WX_WEBVIEW') {
    navigateWeappDownload();
    return;
  }
  
  download();
};
</script>

<style>
.download-game {}

.download-game-affix {
  position: fixed;
  z-index: 98;
}

.download-game-wx-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: transparent;
}

.download-game-wx-btn::after {
  border: none;
}
</style>