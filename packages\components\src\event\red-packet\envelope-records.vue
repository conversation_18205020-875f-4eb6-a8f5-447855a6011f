<template>
  <Records
    v-model:setting="setting"
    :open="open"
    :columns="setting.columns"
    :dataSource="dataSource"
    @open="() => handleShow()"
    @close="handleClose"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'send_status'">
        {{ statusNameMap[record.send_status] }}
      </template>
    </template>
  </Records>
</template>

<script lang="ts">
import type { RecordsSetting, Column } from '../../business/records/records.vue';
import { defaultConfig as recordsDefaultConfig } from '../../business/records/records.vue';

const statusNameMap: Record<number, string> = {
  1: '发放中',
  2: '发放成功',
  3: '发放失败',
};

export interface EnvelopeRecordsSetting extends RecordsSetting {
  /**
   * 表格列
   */
  columns: Column[]
}

export interface EnvelopeRecordsProps {
  /**
   * 配置
   */
  setting: EnvelopeRecordsSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): EnvelopeRecordsSetting => {
  const recordsDefault = recordsDefaultConfig();
  return {
    ...recordsDefault,
    modalSetting: {
      ...recordsDefault.modalSetting,
    },
    columns: [
      {
        title: '金额',
        dataIndex: 'num',
      },
      {
        title: '领取时间',
        dataIndex: 'draw_time',
      },
      // {
      //   title: '发放状态',
      //   dataIndex: 'send_status',
      //   width: '30%',
      // },
    ],
  }
};

export default {
  name: 'envelope-records',
}
</script>

<script lang="ts" setup>
import { computed, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import usePopupStore from '@bish/store/src/modules/popup';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import type { StatefulComponent } from '@bish/types/src/admin';
import Records from '../../business/records/records.vue';

const props = withDefaults(defineProps<EnvelopeRecordsProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: RecordsSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const userStore = useUserStore();
const activityStore = useActivityStore();
const adminStore = useAdminStore();
const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);
const [open, toggle] = useControllableStatus(props, emits, { fieldName: 'showEnvelopeRecords' });

const dataSource = computed(() => {
  if (adminStore.editable) {
    return [
      {
        num: 100,
        draw_time: '2024.09.09 16:01:33',
        send_status: 1,
      },
      {
        num: 200,
        draw_time: '2024.09.09 16:01:33',
        send_status: 2,
      },
      {
        num: 300,
        draw_time: '2024.09.09 16:01:33',
        send_status: 3,
      },
    ];
  }
  return activityStore.componentWithUserInfo?.component_with_user_info?.red_envelope?.draw_list || [];
});

watch(
  () => popupStore.showEnvelopeRecords,
  (newVal) => {
    toggle(newVal);
  },
);

const handleShow = () => {
// 这里后台组件配置特殊逻辑
  if (adminStore.editable) {
    return toggle(true);
  }

  if (!userStore.isLogined) {
    popupStore.setShowLoginModal(true);
    userStore.scheduler.add(() => {
      popupStore.setShowEnvelopeRecords(true);
      return Promise.resolve();
    });
  } else {
    popupStore.setShowEnvelopeRecords(true);
  }
}

const handleClose = () => {
  // 同步数据
  if (open && !popupStore.showEnvelopeRecords) {
    toggle(false);
  }
  popupStore.setShowEnvelopeRecords(false);
};
</script>

<style lang="less">
.envelope-records {}
</style>
