import { commonTaskConfig } from '../index';
import { defaultConfig as inviteModalDefaultConfig } from '../../../invite-modal.vue';
import { defaultConfig as invitationModalDefaultConfig } from '../../../invitation-modal.vue';
import type { TextSetting } from '../../../../ui/text/index.vue';
import type { ImageSetting } from '../../../../ui/image/index.vue';
import type { TaskInviteFriendsSetting } from './invite-friends';

export * from './invite-friends';

const inviteModalConfig = inviteModalDefaultConfig();
const invitationModalConfig = invitationModalDefaultConfig();

export const defaultConfig = (): TaskInviteFriendsSetting => {
  return {
    ...commonTaskConfig(),
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241017/1729164040579-%E9%82%80%E8%AF%B7%E5%A5%BD%E5%8F%8B%E7%99%BB%E5%BD%95%E6%B8%B8%E6%88%8F.png',
    claimBtn: {
      x: 248,
      y: 20,
      width: 84,
      height: 27,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241017/1729164228698-BUTTON%E5%8E%BB%E9%82%80%E8%AF%B7.png',
    },
    inviteCard: {
      ...inviteModalConfig,
      width: 375,
      height: 306,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241017/1729159421042-%E7%BB%84%205%20%281%29.png',
      content: {
        ...(inviteModalConfig.content || {}) as TextSetting,
        x: 86,
        y: 115,
        width: 174,
        height: 62,
        content: '',
        color: '#5E948A',
      },
      linkText: {
        ...(inviteModalConfig.linkText || {}) as TextSetting,
        x: 56,
        y: 180,
        width: 264,
        height: 32,
        color: '#FFFFFF',
        content: '',
      },
      copyLinkBtn: {
        ...(inviteModalConfig.copyLinkBtn || {}) as ImageSetting,
        imgLink: '',
      },
      okBtn: {
        x: 113,
        y: 206,
        width: 150,
        height: 44,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241017/1729159425047-BUTTON%E5%A4%8D%E5%88%B6%E9%93%BE%E6%8E%A5.png',
      },
      closeBtn: {
        x: 326,
        y: 4,
        width: 34,
        height: 34,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241017/1729159429880-%E7%BB%84%203.png',
      },
      copyTemplate: '',
      copyTemplates: [
        {
          text: '大家好我是长安幻想的锦鲤，因为中奖太多常被称为欧皇，现在把好运传授给你，使用它可让长安V你百万福利：',
        },
        {
          text: '大家好我是长安幻想的股东，因为充值太多常被称为金主，现在把财富密码传授给你，使用它可让长安V你百万福利：',
        },
        {
          text: '我是长安幻想的运营临时工，因为发奖太多被公司开除，现在把中奖密令传授给你，使用它可让长安V你百万福利：',
        },
      ],
    },
    invitationCard: {
      ...invitationModalConfig,
      width: 375,
      height: 306,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241017/1729160056807-%E7%BB%84%205.png',
      cancelBtn: {
        ...invitationModalConfig.cancelBtn || {} as ImageSetting,
        x: 50,
        y: 275,
        width: 150,
        height: 44,
        imgLink: '',
      },
      okBtn: {
        ...invitationModalConfig.okBtn || {} as ImageSetting,
        x: 113,
        y: 214,
        width: 150,
        height: 44,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241017/1729164766647-%E7%8E%B0%E5%9C%A8%E5%8F%82%E4%B8%8E.png',
      },
      closeBtn: {
        x: 326,
        y: 4,
        width: 34,
        height: 34,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241017/1729159429880-%E7%BB%84%203.png',
      },
      poster: {
        ...invitationModalConfig.poster || {} as ImageSetting,
        x: 54,
        y: 73,
        width: 240,
        height: 150,
        imgLink: '',
      },
      content: {
        ...invitationModalConfig.content || {} as TextSetting,
        x: 78,
        y: 116,
        width: 200,
        height: 48,
        fontSize: 14,
        fontWeight: true,
        // content: '您的好友【{{friendName}}】邀请您参与活动，一起领豪礼！',
        content: '',
        color: '#3C6759',
      },
    },
    notAccomplishTip: '',
    notAccomplishUrl: '',
    shareTips: {
      x: 0,
      y: 0,
      width: 375,
      height: 229,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241218/1734526609501-%E5%88%86%E4%BA%AB%E6%8F%90%E7%A4%BA-bg.png',
      open: 0,
      actionBtn: {
        x: 118,
        y: 141,
        width: 139,
        height: 59,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241218/1734526606938-%E5%88%86%E4%BA%AB%E6%8F%90%E7%A4%BA-btn.png',
        href: 'https://bish.shiyue.com/cahx/2024/BiGU5f004v8hlEZ_1115/#/',
      },
    },
  }
};