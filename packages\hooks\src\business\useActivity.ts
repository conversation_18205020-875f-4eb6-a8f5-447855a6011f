import { watch } from 'vue'
import useUserStore from '@bish/store/src/modules/user'
import { useToggle } from '@bish/hooks/src/useToggle'
import useActivityStore from '@bish/store/src/modules/activity'

export type InitExtraParams = {
  /**
   * 邀请码，分享时需要
   */
  inviteCode?: string
  /**
   * 邀请类型 1链接邀请 3海报分享邀请
   */
  inviteType?: number
}

export function useActivity() {
  const userStore = useUserStore()
  const { visibleRef: visibleLogin, toggle: toggleLogin } = useToggle()
  const { visibleRef: visibleBindPreRole, toggle: toggleBindPreRole } = useToggle()
  const activityStore = useActivityStore()

  watch(
    () => userStore.userData.token,
    (newVal) => {
      if (newVal) {
        activityStore.initActivityInfo()
      }
    }
  )

  // 退出登录重置状态
  const handleLogout = () => {
    // 重置活动账号相关数据
    activityStore.activityAccountInfo = {}
    activityStore.componentWithUserInfo = {}
    activityStore.bindRoleInfo = {}
    activityStore.initActivityInfo(false)
  }

  return {
    visibleLogin,
    toggleLogin,
    visibleBindPreRole,
    toggleBindPreRole,
    handleLogout,
  }
}
