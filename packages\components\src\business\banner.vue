<template>
  <Resizable
    class="banner"
    :class="{ 'banner__fixed': !adminStore.editable }"
    :x="0"
    :y="0"
    :width="setting.width"
    :height="setting.height"
    :resizable="false"
    :movable="false"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <!-- logo -->
    <UiImage v-if="setting.logo?.imgLink" v-model:setting="setting.logo" />

    <!-- 下载按钮 -->
    <DownloadGame
      v-if="setting.downloadBtn?.imgLink"
      v-model:setting="setting.downloadBtn"
      :status="status"
    />
  </Resizable>
</template>

<script lang="ts">
import type { CommonBackgroundSetting, CommonImageSetting } from '@bish/types/src';
import type { StatefulComponent } from '@bish/types/src/admin';
import { defaultConfig as downloadGameDefaultConfig } from './download-game.vue';

export interface BannerSetting extends CommonBackgroundSetting {
  /**
   * logo 配置
   */
  logo?: CommonImageSetting
  /**
   * 下载按钮配置
   */
  downloadBtn: DownloadGameSetting
}

export interface BannerProps {
  /**
   * 配置
   */
  setting: BannerSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): BannerSetting => {
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 57,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713154460345_.png',
    logo: {
      x: 10,
      y: 3,
      width: 154,
      height: 48,
      // imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-3-5/1709629676491_.png',
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713154463339_.png',
    },
    downloadBtn: downloadGameDefaultConfig(),
  };
};

export default {
  name: 'banner',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults } from 'vue';
import { useVModel } from '@vueuse/core';
import useAdminStore from '@bish/store/src/modules/admin';
import Resizable from '../ui/resizable/index.vue';
import UiImage from '../ui/image/index.vue';
import DownloadGame from './download-game.vue';
import type { DownloadGameSetting } from './download-game.vue';

const props = withDefaults(defineProps<BannerProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: BannerSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);
</script>

<style>
.banner {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute !important;
}

.banner__fixed {
  position: fixed !important;
  left: 50% !important;
  z-index: 98;
  transform: translate3d(-50%, 0, 0);
}
</style>