import type { CommonSetting } from '@bish/types/src'
import type { ResizableProps } from '../../../ui/resizable/index.vue'
import type { PopupSetting } from '../../../common/popup.vue'

export type LoginWeappModalSetting = {
  /**
   * 弹窗基础配置
   */
  modalSetting: PopupSetting
  /**
   * 用户隐私协议配置
   */
  agreement: {
    /**
     * 位置信息
     */
    position: ResizableProps
    /**
     * 是否展示
     */
    show: boolean
    /**
     * 字体颜色
     */
    textColor: string
    /**
     * 协议颜色
     */
    linkColor: string
  }
}

export interface LoginWeappSetting extends Partial<CommonSetting> {
  /**
   * 主题色
   */
  theme?: string
  /**
   * 入口文案，对齐方式
   */
  align?: 'left' | 'right'
  /**
   * 弹窗配置
   */
  modalSetting: LoginWeappModalSetting
  /**
   * 是否限制退出登录，1是 0否，默认 0
   */
  limitSwitch?: number
}