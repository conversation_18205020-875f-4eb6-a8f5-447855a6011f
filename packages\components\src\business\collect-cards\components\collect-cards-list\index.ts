import type { CollectCardsListSetting } from './collect-cards-list';

export * from './collect-cards-list';

export const defaultConfig = (): CollectCardsListSetting => {
  return {
    x: 0,
    y: 387,
    width: 375,
    height: 150,
    item: {
      x: 0,
      y: 0,
      width: 84,
      height: 128,
    },
    itemCard: {
      x: 10,
      y: 0,
      width: 68,
      height: 97,
      card: {
        x: 0,
        y: 0,
        width: 68,
        height: 97,
      },
    },
    name: {
      x: -8,
      y: 108,
      width: 100,
      height: 20,
      fontSize: 13,
      color: '#FFF8DC',
      align: 'center',
      fontWeight: true,
      enabled: true,
    },
    badge: {
      x: 64,
      y: -5,
      width: 21,
      height: 21,
      fontSize: 13,
      color: '#FFFFFF',
      align: 'center',
      alignItems: 'center',
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744277442664-badge.png',
      enabled: true,
    },
  };
}; 
