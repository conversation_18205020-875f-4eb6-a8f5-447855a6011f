<!--
* @Description: 邀请好友/组队-弹窗
-->
<template>
  <div class="invite-modal-pro">
    <!-- 分享模式：复制链接 -->
    <template v-if="setting.actionMode === ACTION_MODE_LINK && setting.link">
      <InviteModalLink
        :show="show"
        :shortLink="shortLink"
        v-model:setting="setting.link"
        @close="handleClose"
      />
    </template>

    <!-- 分享模式：海报 -->
    <template v-if="setting.actionMode === ACTION_MODE_POSTER && setting.poster">
      <InviteModalPoster
        :show="show"
        :shortLink="posterShortLink"
        v-model:setting="setting.poster"
        @close="handleClose"
      />
    </template>

    <!-- 分享模式：复制链接+海报 -->
    <template v-if="setting.actionMode === ACTION_MODE_COMBINE && setting.combine">
      <InviteModalLink
        :show="show"
        :shortLink="shortLink"
        v-model:setting="setting.combine.link"
        @close="handleClose"
        @create-poster="handleCreatePoster"
      />
      <InviteModalPoster
        :show="openPoster"
        :shortLink="posterShortLink"
        v-model:setting="setting.combine.poster"
        @close="handleClosePoster"
      />
    </template>
  </div>
</template>

<script lang="ts">
export default {
  name: 'invite-modal-pro',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import useAdminStore from '@bish/store/src/modules/admin';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import { useLog } from '@bish/hooks/src/useLog';
import useShortLink from '@bish/hooks/src/business/useShortLink';
import { additionalLinkParameters, userAgent } from '@bish/utils/src/utils';
import { whiteUrl } from '@bish/utils/src/urlSearch';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import localStorage from '@bish/utils/src/storage/localStorage';
import type { InviteModalProSetting } from './invite-modal-pro';
import { defaultConfig, ACTION_MODE_LINK, ACTION_MODE_POSTER, ACTION_MODE_COMBINE } from './index';
import InviteModalPoster from './invite-modal-poster/invite-modal-poster.vue';
import InviteModalLink from './invite-modal-link/invite-modal-link.vue';

export interface InviteModalProps {
  /**
   * 配置
   */
  setting: InviteModalProSetting
  /**
   * 是否显示
   */
  show: boolean
  /**
   * 当前队伍id，若存在则表示是组队邀请，否则表示是普通好友邀请
   */
  teamId?: number
  /**
   * 邀请链接key
   */
  keyName?: string
  /**
   * 是否需要绑定角色，默认 true
   */
  bindRole?: boolean
}

const props = withDefaults(defineProps<InviteModalProps>(), {
  setting: defaultConfig,
  show: false,
  bindRole: true,
});

const emits = defineEmits<{
  (event: 'update:setting', value: InviteModalProSetting): void;
  (event: 'close'): void;
}>();

const setting = useVModel(props, 'setting', emits);

const adminStore = useAdminStore();
const userStore = useUserStore();
const activityStore = useActivityStore();

const [openPoster, togglePoster] = useControllableStatus(props, emits, { fieldName: 'showPoster' });

const { uploadLog } = useLog();
const { shortLink, getShortLink } = useShortLink();
const { shortLink: posterShortLink, getShortLink: getPosterShortLink } = useShortLink();

const { isUserAgentType } = userAgent();

// 微信小程序环境
const isWeapp = computed(() => {
  return isUserAgentType === 'WX_MINI';
});

/**
 * 创建短链
 */
const createShortLink = () => {
  const { userData } = userStore;
  const { activityAccountInfo } = activityStore;

  // 存在有一些活动不需要绑定角色
  let invite_user = userData?.phone_number || activityAccountInfo?.account_id || '';
  if (activityAccountInfo.role_info?.server_name) {
    invite_user = `${activityAccountInfo.role_info?.server_name}-${activityAccountInfo.role_info?.role_name}`;
  }
  const scene_id = localStorage.getLocalStorage('SY_LOG_DATA_scene_id') || '';
  const params: Record<string, any> = {
    invite_code: activityAccountInfo.invite_code,
    recall_account_id: encodeURIComponent(activityAccountInfo.account_id),
    invite_user: encodeURIComponent(invite_user),
    invite_type: 1,
  }
  if (props.teamId) {
    params.team_id = props.teamId;
  }
  if (props.keyName) {
    params[props.keyName] = 1; // 特殊标识，以免与组队邀请发生冲突
  }
  if (scene_id) {
    params.c = scene_id;
  }
  const url = props.setting.customUrl || window?.location.href;
  const pureUrl = whiteUrl(url);
  getShortLink(
    additionalLinkParameters(params, pureUrl)
  );
  // 海报短链，附加 is_poster 参数，与普通短链做区别
  getPosterShortLink(
    additionalLinkParameters({ ...params, is_poster: 1 }, pureUrl)
  );
};

watch(
  () => props.show,
  (newVal) => {
    if (newVal && !adminStore.editable && !isWeapp.value) {
      createShortLink();
    }
  },
  { immediate: true },
);

const uploadLogInvite = () => {
  // 事件上报：点击复制分享链接
  uploadLog({
    event_name: 'click',
    click_id: 15,
    click_type: 3,
  });
};

const handleClose = () => {
  emits('close');
};

const handleCreatePoster = () => {
  togglePoster(true);
};

const handleClosePoster = () => {
  togglePoster(false);
};
</script>

<style>
</style>