import type { AddressFormSetting } from './address-form';

export * from './address-form';

export const defaultConfig = (): AddressFormSetting => {
  return {
    modal: {
      x: 0,
      y: 0,
      width: 375,
      height: 346,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241203/1733192907144-m-bg.png',
      closeBtn: {
        x: 307,
        y: 13,
        width: 21,
        height: 21,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241203/1733192916265-m-close.png',
      },
      okBtn: {
        x: 112,
        y: 266,
        width: 151,
        height: 46,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241203/1733192913693-m-btn.png',
      },
      form: {
        position: {
          x: 63,
          y: 89,
          width: 249,
          height: 168,
        },
        label: {
          color: '#935226',
        },
      },
    },
  };
};