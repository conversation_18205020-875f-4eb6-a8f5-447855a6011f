<template>
  <Resizable
    class="reward"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
    @click="handleShow"
  >
    <reward-modal
      v-model:setting="setting.modal"
      :show="show"
      @ok="() => toggleShow(false)"
      @close="() => toggleShow(false)"
    />
  </Resizable>
</template>

<script lang="ts">
import type { CommonBackgroundSetting } from '@bish/types/src'
import { defaultConfig as modalDefaultConfig, type RewardModalSetting } from './reward-modal.vue'
import useUserStore from '@bish/store/src/modules/user';
import usePopupStore from '@bish/store/src/modules/popup';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import type { StatefulComponent } from '@bish/types/src/admin';

export interface RewardSetting extends CommonBackgroundSetting {
  /**
   * 弹窗配置
   */
  modal: RewardModalSetting
}


export interface RewardProps {
  /**
   * 配置
   */
  setting: RewardSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): RewardSetting => {
  return {
    x: 0,
    y: 0,
    width: 21,
    height: 54,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1723020574847_.png',
    modal: modalDefaultConfig()
  };
};

export default {
  name: 'reward',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults } from 'vue';
import { useVModel } from '@vueuse/core';
import Resizable from '../../ui/resizable/index.vue';
import RewardModal from './reward-modal.vue';

const userStore = useUserStore()
const popupStore = usePopupStore()
const activityStore = useActivityStore()
const adminStore = useAdminStore()

const props = withDefaults(defineProps<RewardProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: RewardSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
}>();

const setting = useVModel(props, 'setting', emits);

const [show, toggleShow] = useControllableStatus(props, emits, { fieldName: 'showPopup' });

const handleShow = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return
  }

  if (!userStore.isLogined) {
    popupStore.setShowLoginModal(true)
    userStore.scheduler.add(() => {
      toggleShow(true)
      return Promise.resolve()
    })
  } else {
    // 重新获取用户与组件的数据
    activityStore.getComponentWithUserInfo();
    toggleShow(true)
  }
}
</script>

<style lang="less">
.reward {
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-content {
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;
    text-align: justify;
    word-break: break-all;

    p {
      margin: 0;
      display: inline-block;
    }

    :deep(span) {
      white-space: normal !important;
    }

    &-empty {
      text-align: center;
    }
  }
}
</style>