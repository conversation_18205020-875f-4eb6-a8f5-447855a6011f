import type { HeroPoolSszgSetting } from './hero-pool-sszg';

export * from './hero-pool-sszg';

export const defaultConfig = (): HeroPoolSszgSetting => {
  return {
    x: 0,
    y: 0,
    width: 74,
    height: 20,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241124/1732436948051-entry.png',
    modalSetting: {
      x: 0,
      y: 0,
      width: 375,
      height: 426,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241124/1732436950421-bg.png',
      closeBtn: {
        x: 327,
        y: 2,
        width: 27,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732069799956-close.png',
      },
      list: {
        x: 48,
        y: 86,
        width: 280,
        height: 318,
      },
      item: {
        x: 0,
        y: 0,
        width: 58,
        height: 76,
      },
    },
  };
};