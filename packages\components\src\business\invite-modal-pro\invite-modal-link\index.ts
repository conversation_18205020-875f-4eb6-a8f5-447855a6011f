import type { InviteModalLinkSetting } from './invite-modal-link';

export * from './invite-modal-link';

export const defaultConfig = (): InviteModalLinkSetting => {
  return {
    x: 0,
    y: 0,
    width: 373,
    height: 353,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403310667_.png',
    okBtn: {
      x: 119,
      y: 260,
      width: 141,
      height: 39,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403327152_.png',
    },
    closeBtn: {
      x: 320,
      y: -37,
      width: 35,
      height: 35,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403274633_.png',
    },
    content: {
      x: 102,
      y: 194,
      width: 174,
      height: 80,
      fontSize: 14,
      content: '',
      color: '#424272',
      align: 'center',
    },
    linkText: {
      x: 56,
      y: 164,
      width: 228,
      height: 31,
      fontSize: 16,
      content: '{{link}}',
      color: '#58586D',
      alignItems: 'center',
      align: 'center',
      fontWeight: true,
      background: 'rgba(0, 0, 0, 0.25)',
    },
    copyLinkBtn: {
      x: 294,
      y: 164,
      width: 31,
      height: 30,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403332431_.png',
    },
    copyTemplates: [],
  };
};
