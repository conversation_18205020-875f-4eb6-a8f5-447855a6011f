<!--
* @Description: 道具展示列表
-->
<template>
  <Resizable
    v-model:x="setting.list.x"
    v-model:y="setting.list.y"
    v-model:width="setting.list.width"
    v-model:height="setting.list.height"
    class="items"
    :style="{
      backgroundImage: `url(${setting.list.bgImage})`,
    }"
  >
    <ResizableProvider>
      <div
        v-for="(item, index) in setting.items"
        :key="`${item.imgConfig.imgLink}_${index}`"
        class="items-item"
      >
        <!-- 道具盒子 -->
        <Resizable
          v-model:x="item.box.x"
          v-model:y="item.box.y"
          v-model:width="item.box.width"
          v-model:height="item.box.height"
          :style="{
            backgroundImage: `url(${item.box.bgImage})`,
          }"
          class="items-item-box"
        >
          <ResizableProvider>
            <!-- 道具图片 -->
            <UiImage v-if="item.imgConfig.imgLink" v-model:setting="item.imgConfig" />
            <!-- 道具名称 -->
            <UiText v-if="item.name.content" v-model:setting="item.name" :confined="false" />
          </ResizableProvider>
        </Resizable>
      </div>
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
import type { CommonBackgroundSetting } from '@bish/types/src';
import type { ImageSetting } from '../ui/image/index.vue';
import type { TextSetting } from '../ui/text/index.vue';

export interface ItemsSetting {
  /**
   * 道具配置
   */
  list: CommonBackgroundSetting
  /**
   * 道具列表项
   */
  items: {
    /**
     * 盒子
     */
    box: CommonBackgroundSetting
    /**
     * 道具图片配置
     */
    imgConfig: ImageSetting
    /**
     * 道具名称配置
     */
    name: TextSetting
  }[]
}

export interface ItemsProps {
  /**
   * 配置
   */
  setting: ItemsSetting
}

export const defaultConfigItem = (): ItemsSetting['items'][0] => ({
  box: {
    x: 0,
    y: 0,
    width: 92,
    height: 96,
    bgImage: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/20240906/1725611011341-%E7%9F%A9%E5%BD%A2%202%20%E6%8B%B7%E8%B4%9D%202%20%281%29.png',
  },
  imgConfig: {
    x: 23,
    y: 21,
    width: 45,
    height: 45,
    imgLink: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/20240906/1725615683238-6.png',
  },
  name: {
    x: 0,
    y: 78,
    width: 92,
    height: 18,
    fontSize: 12,
    color: '#FFF4D1',
    align: 'center',
    alignItems: 'center',
    content: '道具名',
  },
});

export const defaultConfig = (): ItemsSetting => {
  return {
    list: {
      x: 37,
      y: 21,
      width: 301,
      height: 96,
      bgImage: '',
    },
    items: [
      {
        box: {
          x: 0,
          y: 0,
          width: 92,
          height: 96,
          bgImage: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/20240906/1725611011341-%E7%9F%A9%E5%BD%A2%202%20%E6%8B%B7%E8%B4%9D%202%20%281%29.png',
        },
        imgConfig: {
          x: 23,
          y: 21,
          width: 45,
          height: 45,
          imgLink: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/20240906/1725615683238-6.png',
        },
        name: {
          x: 0,
          y: 78,
          width: 92,
          height: 18,
          fontSize: 12,
          color: '#FFF4D1',
          align: 'center',
          alignItems: 'center',
          content: '绑玉*100',
        },
      },
      {
        box: {
          x: 105,
          y: 0,
          width: 92,
          height: 96,
          bgImage: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/20240906/1725611011341-%E7%9F%A9%E5%BD%A2%202%20%E6%8B%B7%E8%B4%9D%202%20%281%29.png',
        },
        imgConfig: {
          x: 23,
          y: 21,
          width: 45,
          height: 45,
          imgLink: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/20240906/1725615683238-6.png',
        },
        name: {
          x: 0,
          y: 78,
          width: 92,
          height: 18,
          fontSize: 12,
          color: '#FFF4D1',
          align: 'center',
          alignItems: 'center',
          content: '绑玉*100',
        },
      },
      {
        box: {
          x: 210,
          y: 0,
          width: 92,
          height: 96,
          bgImage: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/20240906/1725611011341-%E7%9F%A9%E5%BD%A2%202%20%E6%8B%B7%E8%B4%9D%202%20%281%29.png',
        },
        imgConfig: {
          x: 23,
          y: 21,
          width: 45,
          height: 45,
          imgLink: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/20240906/1725615683238-6.png',
        },
        name: {
          x: 0,
          y: 78,
          width: 92,
          height: 18,
          fontSize: 12,
          color: '#FFF4D1',
          align: 'center',
          alignItems: 'center',
          content: '绑玉*100',
        },
      },
    ],
  };
};

export default {
  name: 'items',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults } from 'vue';
import { useVModel } from '@vueuse/core';
import useAdminStore from '@bish/store/src/modules/admin';
import Resizable from '../ui/resizable/index.vue';
import ResizableProvider from '../ui/resizable/resizable-provider.vue';
import UiImage from '../ui/image/index.vue';
import UiText from '../ui/text/index.vue';

const props = withDefaults(defineProps<ItemsProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: ItemsSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
}>();

const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);
</script>

<style lang="less">
.items {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.items-item {}

.items-item-box {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>