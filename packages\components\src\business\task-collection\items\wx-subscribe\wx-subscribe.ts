import type { TaskCollectionCommon } from '../index'
import type { ImageSetting } from '../../../../ui/image/index.vue'
import type { PopupSetting } from '../../../../common/popup.vue';
import type { WxSubscribeTemplateList } from '../../../we-subscribe-modal';
import type { WxSubscribeGuideSetting } from '../../../wx-subscribe-guide';

/**
 * 任务-微信消息订阅配置
 */
export type TaskWxSubscribeSetting = TaskCollectionCommon & {
  /**
   * 去完成按钮
   */
  claimBtn: ImageSetting
  /**
   * 已完成按钮
   */
  achievedBtn: ImageSetting
  /**
   * 是否在微信小程序环境中运行，1是 0否
   */
  // weapp: number
  /**
   * 微信消息订阅模板
   */
  wxSubscribeTemp: WxSubscribeTemplateList
  /**
   * 微信小程序订阅提示弹窗
   */
  wxSubscribeGuide: WxSubscribeGuideSetting
  /**
   * 微信小程序订阅弹窗
   */
  wxSubscribe: PopupSetting
}

export type GuideModalSetting = PopupSetting & {}