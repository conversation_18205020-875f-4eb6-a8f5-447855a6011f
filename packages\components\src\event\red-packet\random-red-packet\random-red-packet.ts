import type { CommonSetting } from '@bish/types/src';
import type { ImageSetting } from '../../../ui/image/index.vue';
import type { TextSetting } from '../../../ui/text/index.vue';

export interface RandomRedPacketSetting extends CommonSetting {
  /**
   * 任务id
   */
  taskId: number
  /**
   * 是否需要绑定角色，默认 1
   */
  bindRole: number
  /**
   * 背景图
   */
  bgImage: ImageSetting
  /**
   * 红包
   */ 
  redPacket: ImageSetting
  /**
   * 打开按钮
   */
  openBtn: ImageSetting
  /**
   * 红包打开按钮
   */
  redPacketOpened: ImageSetting
  /**
   * 红包金额
   */
  amount: TextSetting
  /**
   * 暴击按钮
   */
  hitBtn: ImageSetting
  /**
   * 暴击任务id
   */
  taskIdHit: number
  /**
   * 暴击提示
   */
  hitTips: string
}