import type { CollectCardsGiveSetting } from './collect-cards-give';

export * from './collect-cards-give';

export const defaultConfig = (): CollectCardsGiveSetting => {
  return {
    actionBtn: {
      x: 196,
      y: 327,
      width: 161,
      height: 39,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744363553039-gift-btn.png',
      enabled: true,
    },
    modal: {
      x: 0,
      y: 0,
      width: 375,
      height: 382,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744289519671-scratch-bg.png',
      closeBtn: {
        x: 59,
        y: 349,
        width: 119,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744363550206-close-1.png',
      },
      okBtn: {
        x: 199,
        y: 349,
        width: 119,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744363556627-claim.png',
      },
      title: {
        x: 56,
        y: -29,
        width: 264,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744363617109-gift-title.png',
        enabled: true,
      }, 
      box: {
        x: 78,
        y: 5,
        width: 217,
        height: 303,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744363647725-card-box.png',
        enabled: true,
      },
      card: {
        x: 94,
        y: 14,
        width: 186,
        height: 287,
      },
      name: {
        x: 0,
        y: 316,
        width: 375,
        height: 20,
        fontSize: 13,
        color: '#FFF8DC',
        align: 'center',
        fontWeight: true,
        enabled: true,
      },
    },
    ownedModal: {
      x: 0,
      y: 0,
      width: 375,
      height: 382,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744289519671-scratch-bg.png',
      closeBtn: {
        x: 119,
        y: 349,
        width: 139,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744364083452-close-2.png',
      },
      title: {
        x: 56,
        y: -29,
        width: 264,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744363617109-gift-title.png',
        enabled: true,
      }, 
      box: {
        x: 78,
        y: 5,
        width: 217,
        height: 303,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744363647725-card-box.png',
        enabled: true,
      },
      card: {
        x: 94,
        y: 14,
        width: 186,
        height: 287,
      },
      name: {
        x: 0,
        y: 316,
        width: 375,
        height: 20,
        fontSize: 13,
        color: '#FFF8DC',
        align: 'center',
        fontWeight: true,
        enabled: true,
      },
      tip: {
        x: 0,
        y: 118,
        width: 186,
        height: 40,
        fontSize: 11,
        color: '#FFFFFF',
        align: 'center',
        alignItems: 'center',
        content: '<div style="line-height: 1.5;">该卡片已拥有</br>把机会留给其他人吧~</div>',
        enabled: true,
      },
    },
    claimedModal: {
      x: 0,
      y: 0,
      width: 295,
      height: 194,
      bgImage: '',
      closeBtn: {
        x: 80,
        y: 203,
        width: 139,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744364083452-close-2.png',
      },
      title: {
        x: 16,
        y: -29,
        width: 264,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744364104219-claimed-title.png',
        enabled: true,
      }, 
      icon: {
        x: 0,
        y: 10,
        width: 295,
        height: 168,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744364124278-claimed-empty.png',
        enabled: true,
      },
    },
    copyTemplates: [
      {
        cardId: 0,
        text: '出一张“灵宠貔貅卡”，速领~',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770560057-give-poster-1.jpg',
      },
      {
        cardId: 0,
        text: '出一张“招财猫卡”，速领~',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770562487-give-poster-2.jpg',
      },
      {
        cardId: 0,
        text: '出一张“西域商人卡”，速领~',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770565159-give-poster-3.jpg',
      },
      {
        cardId: 0,
        text: '出一张“易宝生财卡”，速领~',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770614848-give-poster-4.jpg',
      },
      {
        cardId: 0,
        text: '出一张“黄金时装卡”，速领~',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770617286-give-poster-5.jpg',
      },
      {
        cardId: 0,
        text: '出一张“财宝瓜分卡”，速领~',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770619656-give-poster-6.jpg',
      },
      {
        cardId: 0,
        text: '出一张“一鉴暴富卡”，速领~',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770648376-give-poster-7.jpg',
      },
      {
        cardId: 0,
        text: '出一张“寻金西市卡”，速领~',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770650762-give-poster-8.jpg',
      },
    ],
  };
}; 
