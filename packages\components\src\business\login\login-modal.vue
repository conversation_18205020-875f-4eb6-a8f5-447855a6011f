<template>
  <Popup
    :show="show"
    v-model:setting="setting.modalSetting"
    z-index="101"
    @close="handleClose"
    @ok="handleLoginClick"
  >
    <template v-for="key in setting.loginType" :key="key">
      <Resizable
        v-model:x="setting[key]!.position.x"
        v-model:y="setting[key]!.position.y"
        v-model:width="setting[key]!.position.width"
        v-model:height="setting[key]!.position.height"
      >
        <img
          v-if="currentLoginType === key ? setting[key]?.activeBg : setting[key]?.bg"
          class="login-tab-img"
          :src="currentLoginType === key ? setting[key]?.activeBg : setting[key]?.bg"
          @click="currentLoginType = key"
        />
      </Resizable>

      <Resizable
        v-model:x="setting.form.position.x"
        v-model:y="setting.form.position.y"
        v-model:width="setting.form.position.width"
        v-model:height="setting.form.position.height"
      >
        <div class="login-form-area">
          <template v-if="currentLoginType === 'phone'">
            <div :style="inputWrapperStyle" class="login-input-warpper">
              <div v-if="!form.phone" :style="placeholderStyle" class="login-input-placeholder">请输入手机号</div>
              <input
                v-model="form.phone"
                :style="inputStyle"
                class="login-input"
                @blur="handlePhoneBlur"
              />
            </div>
            <div class="sms-form-area">
              <div :style="{ ...inputWrapperStyle, flex: 1 }" class="login-input-warpper">
                <div v-if="!form.smsCode" :style="placeholderStyle" class="login-input-placeholder">请输入验证码</div>
                <input
                  v-model="form.smsCode"
                  :style="inputStyle"
                  class="login-input"
                  @blur="handleCodeBlur"
                />
              </div>
              <div @click="handleGetCaptcha" :style="smsStyle" class="sms-area">
                {{ count ? `${count}s` : '获取验证码' }}
              </div>
            </div>
            
          </template>
          <template v-else>
            <div :style="inputWrapperStyle" class="login-input-warpper">
              <div v-if="!form.account" :style="placeholderStyle" class="login-input-placeholder">请输入账号</div>
              <input
                v-model="form.account"
                :style="inputStyle"
                class="login-input"
              />
            </div>
            <div :style="inputWrapperStyle" class="login-input-warpper">
              <div v-if="!form.password" :style="placeholderStyle" class="login-input-placeholder">请输入密码</div>
              <input
                v-model="form.password"
                type="password"
                :style="inputStyle"
                class="login-input"
              />
            </div>
          </template>
        </div>
      </Resizable>

      <Resizable
        v-model:x="setting.form.agreement.position.x"
        v-model:y="setting.form.agreement.position.y"
        v-model:width="setting.form.agreement.position.width"
        v-model:height="setting.form.agreement.position.height"
        v-if="setting.form.agreement.show"
      >
        <div class="login-agreement" :style="agreementStyle">
          <van-checkbox
            :icon-size="pxTransform(12)"
            v-model="agreementChecked"
            shape="square"
            :checked-color="theme"
            style="display: inline-flex;"
          >
            <span :style="agreementTxtStyle">我已阅读并同意</span>
          </van-checkbox>
          <span :style="agreementLinkStyle" @click="handleShowAgreement(1)">《用户协议》</span>
          <span :style="agreementTxtStyle">及</span>
          <span :style="agreementLinkStyle" @click="handleShowAgreement(2)">《隐私协议》</span>
        </div>
      </Resizable>
    </template>
  </Popup>
</template>

<script lang="ts">
import type { ResizableProps } from '../../ui/resizable/index.vue'
import type { PopupSetting } from '../../common/popup.vue'
import { watch } from 'vue'
import { computed } from 'vue'
import type { CSSProperties } from 'vue'
import { pxTransform } from '@bish/utils/src/viewport'
import { reactive } from 'vue'
import { showToast } from '@bish/ui/src/toast';

import { parseQueryString } from '@bish/utils/src/utils'

type LoginType = 'phone' | 'account'

export interface LoginModalSetting {
  modalSetting: PopupSetting // 弹窗基础配置
  loginType: Array<LoginType> // 登录类型[手机, 账号]
  phone?: { // 手机登录的tab配置
    position: ResizableProps
    bg: string // 默认背景
    activeBg: string // 选中时背景
  }
  account?: LoginModalSetting['phone'] // 账号登录时tab的配置
  form: { // 表单配置
    position: ResizableProps // 位置
    input: { // 输入框配置
      height?: number // 高度
      background?: string // 背景
      color?: string // 颜色
    }
    sms: { // 获取验证码按钮配置
      width?: number
      background?: string
      color?: string
    },
    agreement: { // 用户隐私协议配置
      position: ResizableProps
      show: boolean // 是否展示
      /**
       * 字体颜色
       */
      textColor: string
      /**
       * 协议颜色
       */
      linkColor: string
    }
  }
}

export interface LoginModalProps {
  setting: LoginModalSetting
  theme?: string
  /**
   * 是否显示
   */
  show: boolean
}

export const defaultConfig = (): LoginModalSetting => ({
  modalSetting: {
    x: 0,
    y: 0,
    width: 373,
    height: 353,
    bgImage: "https://cms-**********.cos.ap-shanghai.myqcloud.com/public/2024-07-31/*************-%E5%BA%95%E6%A1%866%20%E6%8B%B7%E8%B4%9D%204.png",
    okBtn: {
      x: 117,
      y: 270,
      width: 141,
      height: 39,
      imgLink: 'https://cms-**********.cos.ap-shanghai.myqcloud.com/public/2024-07-31/*************-%E6%8C%89%E9%92%AE-%E7%99%BB%E5%BD%95%20%E6%8B%B7%E8%B4%9D.png'
    },
    closeBtn: {
      x: 324,
      y: -35,
      width: 35,
      height: 35,
      imgLink: 'https://cms-**********.cos.ap-shanghai.myqcloud.com/public/2024-07-31/*************-%E5%85%B3%E9%97%AD%20%E6%8B%B7%E8%B4%9D%202.png'
    },
  },
  loginType: ['phone', 'account'],
  phone: {
    position: {
      x: 32,
      y: 26,
      width: 158,
      height: 38,
    },
    bg: 'https://cms-**********.cos.ap-shanghai.myqcloud.com/public/2024-07-31/*************-%E6%89%8B%E6%9C%BA%E9%AA%8C%E8%AF%81%E7%A0%81.png',
    activeBg: 'https://cms-**********.cos.ap-shanghai.myqcloud.com/public/2024-07-31/*************-%E6%89%8B%E6%9C%BA%E9%AA%8C%E8%AF%81%E7%A0%81-%E9%80%89%20%E6%8B%B7%E8%B4%9D.png'
  },
  account: {
    position: {
      x: 190,
      y: 26,
      width: 158,
      height: 38,
    },
    bg: 'https://cms-**********.cos.ap-shanghai.myqcloud.com/public/2024-07-31/*************-%E8%B4%A6%E5%AF%86%E7%99%BB%E5%BD%95%20%E6%8B%B7%E8%B4%9D.png',
    activeBg: 'https://cms-**********.cos.ap-shanghai.myqcloud.com/public/2024-07-31/*************-%E8%B4%A6%E5%AF%86%E7%99%BB%E5%BD%95-%E9%80%89%20%E6%8B%B7%E8%B4%9D.png'
  },
  form: {
    position: {
      x: 56,
      y: 125,
      width: 263,
      height: 80,
    },
    input: {
      height: 31,
      background: 'rgba(26, 36, 84, 0.1)',
      color: '#1A2454'
    },
    sms: {
      width: 90,
      background: '#5E67C0',
      color: '#ffffff'
    },
    agreement: {
      position: {
        x: 56,
        y: 215,
        width: 274,
        height: 32,
      },
      show: true,
      textColor: '#999999',
      linkColor: 'rgb(36, 43, 69)',
    }
  },
})

export default {
  name: 'LoginModal'
}
</script>
<script setup lang="ts">
import Popup from '../../common/popup.vue'
import useActivityStore from '@bish/store/src/modules/activity'
import useUserStore from '@bish/store/src/modules/user'
import { useVModel } from '@vueuse/core'
import type { LoginSetting } from './index.vue'
import Resizable from '../../ui/resizable/index.vue'
import useCountDown from '@bish/hooks/src/useCountDown'
import { ref } from 'vue'
import showTencentCaptcha, { type CallbackData } from '../../__utils/tencentCaptcha'
import { postWebSmsSend, postWebPhoneLogin, postWebLogin } from '@bish/api/src/user'
import storage from '@bish/utils/src/storage/localStorage'

const props = withDefaults(defineProps<LoginModalProps>(), {
  setting: defaultConfig,
  theme: 'rgb(36,43,69)',
  show: false,
})
const emits = defineEmits<{
  (event: 'update:setting', value: LoginSetting): void;
  (event: 'after-login'): void;
  (event: 'close'): void;
}>();

const PHONE_STORAGE_KEY = '__phoneLoginKey__'
const ACCOUNT_STORAGE_KEY = '__accountLoginKey__'

const setting = useVModel(props, 'setting', emits)

const activityStore = useActivityStore()
const userStore = useUserStore()
const { count, startCountDown, clearTimer } = useCountDown()

// 登录类型
const currentLoginType = ref<LoginType | undefined>(props.setting.loginType[0])
// 登录表单值
const form = reactive({
  phone: storage.getLocalStorage(PHONE_STORAGE_KEY) || '',
  smsCode: '',
  account: storage.getLocalStorage(ACCOUNT_STORAGE_KEY) || '',
  password: '',
})
// 是否已勾选协议
const agreementChecked = ref(false)

const inputWrapperStyle = computed<CSSProperties>(() => ({
  height: pxTransform(setting.value.form.input.height!),
  background: setting.value.form.input.background,
  padding: `0 ${pxTransform(12)}`,
}))
const inputStyle = computed<CSSProperties>(() => ({
  padding: `0 ${pxTransform(12)}`,
  border: 'none',
  outline: 'none',
  color: setting.value.form.input.color,
  background: 'transparent',
}))
const placeholderStyle = computed<CSSProperties>(() => ({
  height: pxTransform(setting.value.form.input.height!),
  color: setting.value.form.input.color,
}))
const smsStyle = computed<CSSProperties>(() => ({
  flexBasis: pxTransform(setting.value.form.sms.width!),
  color: setting.value.form.sms.color,
  background: setting.value.form.sms.background,
}))
const agreementStyle = computed<CSSProperties>(() => ({
  fontSize: pxTransform(12),
}))
const agreementTxtStyle = computed<CSSProperties>(() => {
  return {
    color: setting.value.form.agreement.textColor || '#999999',
  }
})
const agreementLinkStyle = computed<CSSProperties>(() => {
  return {
    color: setting.value.form.agreement.linkColor || props.theme,
  }
})

// 修改登录类型配置时，将当前登录类型设置为已选的第一项
watch(() => props.setting.loginType, (val) => {
  currentLoginType.value = val[0]
})

// 请求手机验证码
const fetchSmsCode = async (data: CallbackData) => {
  try {
    const res = await postWebSmsSend({
      type: 'phone_login',
      phone: form.phone,
      randstr: data.randstr,
      ticket: data.ticket,
      projectId: activityStore.activityInfo?.init?.project_id || 9999
    })
    if (res.code === 0) {
      showToast('已发送')
      startCountDown()
    }
  } catch (error) {
    console.warn('获取验证码失败', error)
  }
}

// 点击获取验证码时，进行表单校验及人机校验
const handleGetCaptcha = async () => {
  if (count.value !== 0) {
    return
  }
  if (!form.phone) {
    showToast('请输入手机号')
    return
  }
  if (!/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(form.phone)) {
    showToast('请输入正确格式的手机号')
    return
  }
  showTencentCaptcha(
    'normal',
    (res) => {
      fetchSmsCode(res)
    },
  )
}

// 请求登录接口
const handleLogin = async (data?: CallbackData & { rand_code?: string }) => {
  try {
    // 区分手机&账号登录
    const request = currentLoginType.value === 'account' ? postWebLogin : postWebPhoneLogin
    const res = await request(
      {
        phone: form.phone,
        code: form.smsCode,
        account: form.account,
        password: form.password,
        randstr: data?.randstr,
        ticket: data?.ticket,
        randcode: data?.rand_code
      },
    )
    // 自行处理错误提示
    switch (res.code) {
      case 0:
        break
      // 账号不存在
      case 1101:
        break
      default:
        showToast(res.message)
    }
    // 账号不存在
    if (res.code === 1101) {
      // 唤起人机校验后再调用接口自动注册
      showTencentCaptcha('strongest', (captchares) => {
        handleLogin({ ...captchares, rand_code: res.data.rand_code })
      })
    }
    // 如果登录成功，则清除验证码定时器
    if (res.code === 0) {
      userStore.setUserData(res.data)
      emits('close')
      clearTimer()
      storage.removeLocalStorage('openid')
      if (currentLoginType.value === 'phone') {
        storage.setLocalStorage(PHONE_STORAGE_KEY, form.phone)
      } else {
        storage.setLocalStorage(ACCOUNT_STORAGE_KEY, form.account)
      }
      // 执行登录成功回调队列
      userStore.workLoggedTask()
      // 获取活动账号信息
      await activityStore.getActivityAccountInfo()
      emits('after-login')
    }
  } catch (error) {
    console.warn('手机登录失败', error)
  }
}
// 点击登录
const handleLoginClick = async () => {
  if (currentLoginType.value === 'phone') {
    if (!form.phone) {
      showToast('请输入手机号')
      return
    }
    if (!/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(form.phone)) {
      showToast('请输入正确格式的手机号')
      return
    }
    if (!form.smsCode) {
      showToast('请输入验证码')
      return
    }
    if (!/^\d{4}$/.test(form.smsCode)) {
      showToast('请输入正确格式的验证码')
      return
    }
    if (!agreementChecked.value && setting.value.form.agreement.show) {
      showToast('请阅读并同意协议')
      return
    }
    handleLogin()
  } else if (currentLoginType.value === 'account') {
    if (!form.account) {
      showToast('请输入账号')
      return
    }
    if (!form.password) {
      showToast('请输入密码')
      return
    }
    if (!agreementChecked.value && setting.value.form.agreement.show) {
      showToast('请阅读并同意协议')
      return
    }
    showTencentCaptcha('normal', (res) => {
      handleLogin(res)
    })
  }
}
// 点击用户协议
const handleShowAgreement = (type: 1 | 2) => {
  if (type == 1) {
    window.open('https://www.shiyue.com/m/article_detail_10_34.html')
  } else if (type == 2) {
    window.open('https://www.shiyue.com/m/article_detail_10_37.html')
  }
}
const handleClose = () => {
  // 用户主动关闭弹窗时，清空队列
  userStore.scheduler.clear()

  emits('close')
}
const handlePhoneBlur = () => {
  const reg = /[^0-9]/g
  form.phone = form.phone.replace(reg, '').slice(0, 11)
}
const handleCodeBlur = () => {
  const reg = /[^0-9]/g
  form.smsCode = form.smsCode.replace(reg, '').slice(0, 4)
}
</script>

<style lang="less">
.login-popup {
  background-color: transparent;
  overflow: visible;
}
.login-bg {
  background-size: cover;
}
.login-tab-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.sms-form-area {
  display: flex;
}
.login-form-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
  flex-grow: 1;
  .sms-area {
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
}
.login-input-warpper {
  display: flex;
  align-items: center;
  position: relative;
}
.login-input {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.login-input-placeholder {
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
}
.login-agreement {
  color: #999;
  line-height: 1.1;

  .van-checkbox {
    align-items: baseline;
  }

  .van-checkbox__icon .van-icon {
    border-color: #999;
  }
}
</style>
