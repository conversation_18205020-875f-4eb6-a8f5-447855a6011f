type anyObject = Record<string, any>

/**
 * 过滤对象属性值为 undefined、null 的属性
 * @param {*} obj 
 * @returns 
 */
export const filterObject = (obj: anyObject) => {
  const newObj: anyObject = {};
  for (const key in obj) {
    // 判断属性值是否为 undefined、null
    if (obj[key] !== undefined && obj[key] !== null) {
    // 如果属性值是对象，则递归调用该函数
      if (typeof obj[key] === 'object') {
        newObj[key] = filterObject(obj[key]);
      } else {
        newObj[key] = obj[key];
      }
    }
  }
  return newObj;
};