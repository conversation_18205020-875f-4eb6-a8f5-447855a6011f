<!--
 * @description: 登录组件弹窗-微信小程序版本
 * @author: xiezhixiong
-->
<template>
  <Popup
    :show="show"
    v-model:setting="setting.modalSetting"
    z-index="101"
    @close="handleClose"
    @ok="handleLogin"
  >
    <!-- 微信小程序登录按钮 -->
    <button
      v-if="showLoginBtn"
      :style="loginBtnStyle"
      class="login-btn"
      open-type="getPhoneNumber"
      @getphonenumber="handleGetPhoneNumber"
    />

    <!-- 用户协议 -->
    <Resizable
      v-model:x="setting.agreement.position.x"
      v-model:y="setting.agreement.position.y"
      v-model:width="setting.agreement.position.width"
      v-model:height="setting.agreement.position.height"
      v-if="setting.agreement.show"
    >
      <div class="login-agreement" :style="agreementStyle">
        <view class="login-radio" @click="handleChecked">
          <span :style="radioIconStyle" class="login-radio-icon">
            <span v-if="agreementChecked" :style="radioIconInnerStyle" class="login-radio-icon-inner" />
          </span>
          <span :style="agreementTxtStyle">已阅读并同意</span>
        </view>
        <span :style="agreementLinkStyle" @click="() => handleJumpAgreement(1)">
          《用户协议》
        </span>
        <span :style="agreementTxtStyle">及</span>
        <span :style="agreementLinkStyle" @click="() => handleJumpAgreement(2)">
          《隐私协议》
        </span>
      </div>
    </Resizable>
  </Popup>
</template>

<script lang="ts">
export default {
  name: 'login-weapp-modal',
}
</script>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { CSSProperties } from 'vue'
import { useVModel } from '@vueuse/core'
import { pxTransform } from '@bish/utils/src/viewport'
import { bishInst } from '@bish/core/src/models/Bish'
import { userAgent } from '@bish/utils/src/utils'
import useActivityStore from '@bish/store/src/modules/activity'
import { showToast } from '@bish/ui/src/toast'
import Resizable from '../../../ui/resizable/index.vue'
import Popup from '../../../common/popup.vue'
import { defaultModalConfig } from './index'
import type { LoginWeappModalSetting } from './login-weapp'

export interface LoginWeappModalProps {
  setting: LoginWeappModalSetting
  /**
   * 主题色
   */
  theme?: string
  /**
   * 是否展示
   */
  show: boolean
}

const props = withDefaults(defineProps<LoginWeappModalProps>(), {
  setting: defaultModalConfig
})

const emits = defineEmits<{
  (event: 'update:setting', value: LoginWeappModalSetting): void;
  (event: 'close'): void;
  (event: 'login'): void;
}>()

// 是否已勾选协议
const agreementChecked = ref(false)

const activityStore = useActivityStore()

const setting = useVModel(props, 'setting', emits)

const { isUserAgentType } = userAgent()

// 微信小程序环境
const isWeapp = computed(() => {
  return isUserAgentType === 'WX_MINI'
})

const showLoginBtn = computed(() => {
  return isWeapp.value && !activityStore.activityAccountInfo?.account_id && agreementChecked.value;
})

const radioIconStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(12),
    height: pxTransform(12),
    marginRight: pxTransform(6),
    borderColor: setting.value.agreement.linkColor || props.theme,
  }
})

const radioIconInnerStyle = computed<CSSProperties>(() => {
  return {
    backgroundColor: setting.value.agreement.linkColor || props.theme,
  }
})

const agreementStyle = computed<CSSProperties>(() => ({
  fontSize: pxTransform(12),
}))

const agreementTxtStyle = computed<CSSProperties>(() => {
  return {
    color: setting.value.agreement.textColor || '#999999',
  }
})

const agreementLinkStyle = computed<CSSProperties>(() => {
  return {
    color: setting.value.agreement.linkColor || props.theme,
  }
})

const loginBtnStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.modalSetting.okBtn?.x || 0),
    top: pxTransform(setting.value.modalSetting.okBtn?.y || 0),
    width: pxTransform(setting.value.modalSetting.okBtn?.width || 0),
    height: pxTransform(setting.value.modalSetting.okBtn?.height || 0),
  }
})

const handleChecked = () => {
  agreementChecked.value = !agreementChecked.value
}

const handleLogin = async () => {
  if (!agreementChecked.value) {
    showToast('请先阅读并同意协议~')
    return
  }
}

const handleClose = () => {
  emits('close')
  // 重置勾选
  agreementChecked.value = false
}

const handleGetPhoneNumber = (e: any) => {
  if (e.detail.errMsg !== 'getPhoneNumber:fail user deny') {
    // 通知登录
    bishInst.weappObserver.notify({
      type: 'phoneLogin',
      detail: e,
    })
    emits('login')
  }
}

const handleJumpAgreement = (type: number) => {
  // 通知登录
  bishInst.weappObserver.notify({
    type: 'jumpAgreement',
    detail: type,
  })
}; 
</script>

<style>
.login-agreement {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  line-height: 1.3;
}

.login-radio {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.login-radio-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  border-width: 1px;
  border-style: solid;
}

.login-radio-icon-inner {
  display: block;
  width: 60%;
  height: 60%;
  border-radius: 100%;
}

.login-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: transparent;
}

.login-btn::after {
  border: none;
}
</style>