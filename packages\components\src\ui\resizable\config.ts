export interface StartStateType {
  x: number;
  y: number;
  mouseX: number;
  mouseY: number;
  width: number;
  height: number;
  direction: string;
}
export interface SizeLimitType {
  minWidth: number;
  minHeight: number;
  maxWidth: number;
  maxHeight: number;
}
export interface PositionLimitType {
  left: number;
  right: number;
  top: number;
  bottom: number;
}

export const contextKey = Symbol()

// 根据限制条件重置偏移值
export const getLimitDelta = (
  config: {
    startPoint: number;
    mouseDelta: number;
    width: number;
    direction?: 'start' | 'end';
  },
  sizeLimit?: {
    min: number;
    max: number;
  },
  positionLimit?: {
    min: number;
    max: number;
  },
  adsorptionPointList?: number[],
  precision = 2
) => {
  // 最终偏移值
  let delta = config.mouseDelta;
  // 匹配到吸附的点坐标
  let adsorptionPoint: number | undefined;

  const currentStartPoint = config.startPoint + (config.direction === 'end' ? 0 : delta);
  const currentEndPoint =  config.startPoint + config.width + delta;
  const currentCenterPoint = currentStartPoint + (currentEndPoint - currentStartPoint) / 2;

  if (config.direction && sizeLimit) {
    // 根据操作的轴决定是+/-
    const rate = config.direction === 'start' ? -1 : 1
    // 操作后的理论宽度
    const afterWidth = config.width + delta * rate;
    // 超过边界时，限定delta
    if (afterWidth  > sizeLimit.max) {
      return { delta: (sizeLimit.max - config.width) * rate };
    }
    if (afterWidth < sizeLimit.min) {
      return { delta: -config.width * rate };
    }
  }

  if (positionLimit) {
    let deltaPadding = 0;
    if (config.direction === 'end' || !config.direction) {
      deltaPadding = config.width;
    }
    // 操作的轴的x实际坐标
    const positionX = config.startPoint + delta + deltaPadding;
    if (positionX - deltaPadding < positionLimit.min && !(config.direction === 'end')) {
      return { delta: positionLimit.min - config.startPoint };
    }
    if (positionX > positionLimit.max && !(config.direction === 'start')) {
      return { delta: positionLimit.max - config.startPoint - deltaPadding }
    }
  }
  if (adsorptionPointList?.length) {
    let matchList: number[] = [];
    if (config.direction === 'start') {
      matchList = [currentStartPoint];
    } else if (config.direction === 'end') {
      matchList = [currentEndPoint];
    } else {
      matchList = [currentCenterPoint, currentStartPoint, currentEndPoint];
    }
    let minDiff = precision;
    adsorptionPointList.forEach((point) => {
      matchList.forEach((slide) => {
        const diff = point - slide;
        // 找出最靠近的一个点
        if (Math.abs(diff) <= Math.abs(minDiff)) {
          minDiff = diff;
          adsorptionPoint = point;
        }
      });
    });
    if (typeof adsorptionPoint === 'number') {
      return { delta: delta + minDiff, adsorptionPoint };
    }
  }
  return { delta };
}
