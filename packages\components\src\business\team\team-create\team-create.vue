<!--
* @Description: 基础组队-创建队伍
-->
<template>
  <Resizable
    v-if="showCreateTeam"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
  >
    <!-- 入口 -->
    <div
      class="team-create"
      :class="{
        'team-create-affix': setting.affix && !adminStore.editable,
        'team-create-btn__disabled': !teamConfig,
      }"
      :style="entryStyle"
      @click="handleOpen"
    />

    <!-- 弹窗 -->
    <Popup
      z-index="99"
      :show="open"
      v-model:setting="setting.modal"
      @close="handleClose"
      @ok="handleCreateTeam"
    >
      <!-- 文本内容 -->
      <UiText
        v-if="setting.modal.content.enabled"
        v-model:setting="setting.modal.content"
        :interpolation="interpolation"
        class="team-create-content"
      />

      <!-- 取消按钮 -->
      <UiImage
        v-if="setting.modal.cancelBtn.enabled"
        v-model:setting="setting.modal.cancelBtn"
        @click.stop="handleClose"
      />
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'team-create',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, watch } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import usePopupStore from '@bish/store/src/modules/popup';
import useAdminStore from '@bish/store/src/modules/admin';
import { useLog } from '@bish/hooks/src/useLog';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { pxTransform } from '@bish/utils/src/viewport';
import { createTeam } from '@bish/api/src/team-gift';
import Resizable from '../../../ui/resizable/index.vue';
import UiImage from '../../../ui/image/index.vue';
import UiText from '../../../ui/text/index.vue';
import Popup from '../../../common/popup.vue';
import type { TeamCreateSetting } from './team-create';
import { defaultConfig } from './index';

export interface TeamCreateProps {
  /**
   * 配置
   */
  setting: TeamCreateSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TeamCreateProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TeamCreateSetting): void;
}>();

const setting = useVModel(props, 'setting', emits);

const [open, toggle] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showTeamCreateModal' });

const activityStore = useActivityStore();
const popupStore = usePopupStore();
const adminStore = useAdminStore();

const { uploadLog } = useLog();

const bindRoleInfo = computed(() => {
  return activityStore.bindRoleInfo;
});

const interpolation = computed(() => {
  return {
    teamName: `${bindRoleInfo.value.role_name || '未知'}的队伍`,
  };
});

const entryStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.x!),
    top: pxTransform(setting.value.y!),
    width: pxTransform(setting.value.width),
    height: pxTransform(setting.value.height),
    backgroundImage: `url(${setting.value.bgImage})`,
  };
});

// 队伍信息配置信息
const teamConfig = computed(() => {
  const { team } = activityStore.activityInfo.config || {};
  return team?.find(item => item.id === setting.value.teamId);
});

// 与配置对应的队伍信息
const currenTeam = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.team?.find(item => item.id === setting.value.teamId);
});

const showCreateTeam = computed(() => {
  return adminStore.editable || !currenTeam.value?.team_id;
});

watch(
  () => popupStore.showTeamCreate,
  (newVal) => {
    if (newVal) {
      handleOpen();
    } else {
      toggle(false);
    }
  },
);

const handleOpen = () => {
  // 事件上报:创建队伍
  uploadLog({
    event_name: 'click',
    click_id: 13,
    click_type: 3,
  });

  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!teamConfig.value) {
    showToast('活动已结束~');
    return;
  }

  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }

  toggle(true);
};

const handleClose = () => {
  // 同步数据
  if (open && !popupStore.showTeamCreate) {
    toggle(false);
  }
  popupStore.setShowTeamCreate(false);
};

const handleCreateTeam = async () => {
  try {
    if (!teamConfig.value) {
      showToast('缺少队伍配置，请稍候再试~');
      return;
    }
    const { activityAccountInfo, bindRoleInfo } = activityStore;
    const res = await createTeam({
      act_id: activityAccountInfo.activity_id,
      act_acc_id: activityAccountInfo.act_acc_id,
      team_config_id: setting.value.teamId,
      server_name: bindRoleInfo.server_name,
      role_name: bindRoleInfo.role_name,
      sex: bindRoleInfo.sex,
      career: +bindRoleInfo.career,
      team_name: bindRoleInfo.role_name + '的队伍',
    });
    if (res.code === 0) {
      showToast('创建成功');
    }
    handleClose();
    activityStore.getComponentWithUserInfo();
  } catch (error) {
    console.warn('创建队伍失败', error);
  }
};
</script>

<style>
.team-create {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.team-create-affix {
  position: fixed;
  left: auto !important;
  z-index: 98;
}

.team-create-content {
  font-weight: 400;
}

.team-create-btn__disabled {
  filter: grayscale(100%);
}
</style>