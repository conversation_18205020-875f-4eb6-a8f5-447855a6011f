<template>
  <Resizable
    class="box-lottery"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <ResizableProvider class="box-lottery-list">
      <ResizableProvider
        v-for="(item, index) in front"
        :key="item"
        :data-index="item"
        class="box-lottery-list-item"
        :style="boxItemStyle"
      >
        <Resizable
          :x="position(index).x"
          :y="position(index).y"
          v-model:width="setting.box.width"
          v-model:height="setting.box.height"
          :movable="false"
          :style="{
            backgroundImage: `url(${setting.box.bgImage})`,
          }"
          class="box-lottery-list-item-content"
          :class="{ 'box-lottery-list-item-content__active': item === track[activeIndex] }"
        >
          <!-- 奖品图片 -->
          <Resizable
            v-model:x="setting.box.prize.x"
            v-model:y="setting.box.prize.y"
            v-model:width="setting.box.prize.width"
            v-model:height="setting.box.prize.height"
            :class="{ 'box-lottery-list-item-prize-placeholder': adminStore.editable }"
          >
            <img
              v-if="providedPrizes[index + 0]?.icon"
              :src="providedPrizes[index + 0]?.icon"
              class="box-lottery-list-item-prize-img"
            />
            <div v-else class="box-lottery-list-item-prize-slot" />
          </Resizable>

          <!-- 奖品名称 -->
          <UiText
            v-model:setting="setting.box.prizeName"
            :confined="false"
            :scrollspy="false"
            :editableContent="false"
            class="box-lottery-list-item-prize-name"
          >
            {{ providedPrizes[index + 0]?.name || '--' }}
          </UiText>
        </Resizable>
      </ResizableProvider>

      <!-- 抽奖按钮-占位元素 -->
      <div v-if="boxGrid === 9" class="box-lottery-list-item">
        <Resizable
          :x="setting.box.x"
          :y="setting.box.y"
          :width="setting.box.width"
          :height="setting.box.height"
          :movable="false"
          :resizable="false"
        />
      </div>
      <!-- 抽奖按钮 -->
      <Resizable
        v-if="setting.drawBtn"
        v-model:x="setting.drawBtn.x"
        v-model:y="setting.drawBtn.y"
        v-model:width="setting.drawBtn.width"
        v-model:height="setting.drawBtn.height"
        :confined="false"
        :style="{
          backgroundImage: `url(
            ${setting.drawBtn?.noDrawNumBtnImg && !remaining ? setting.drawBtn.noDrawNumBtnImg : setting.drawBtn.bgImage}
          )`,
          zIndex: 2,
        }"
        class="box-lottery-list-item"
        :class="{ 'box-lottery-list-item__disabled': !remaining && !setting.drawBtn.noDrawNumBtnImg }"
        @click="handleDraw"
      >
        <!-- 剩余抽奖次数 -->
        <UiText
          v-if="setting.drawBtn?.remaining.show"
          v-model:setting="setting.drawBtn.remaining"
          :interpolation="remainingInterpolation"
          :confined="false"
        />
      </Resizable>

      <ResizableProvider
        v-for="(item, index) in tail"
        :key="item"
        :data-index="item"
        class="box-lottery-list-item"
        :style="boxItemStyle"
      >
        <Resizable
          :x="position(index + front.length).x"
          :y="position(index + front.length).y"
          v-model:width="setting.box.width"
          v-model:height="setting.box.height"
          :movable="false"
          :style="{
            backgroundImage: `url(${setting.box.bgImage})`,
          }"
          class="box-lottery-list-item-content"
          :class="{ 'box-lottery-list-item-content__active': item === track[activeIndex] }"
        >
          <!-- 奖品图片 -->
          <Resizable
            v-model:x="setting.box.prize.x"
            v-model:y="setting.box.prize.y"
            v-model:width="setting.box.prize.width"
            v-model:height="setting.box.prize.height"
            :class="{ 'box-lottery-list-item-prize-placeholder': adminStore.editable }"
          >
            <img
              v-if="providedPrizes[index + front.length]?.icon"
              :src="providedPrizes[index + front.length]?.icon"
              class="box-lottery-list-item-prize-img"
            />
          </Resizable>

          <!-- 奖品名称 -->
          <UiText
            v-model:setting="setting.box.prizeName"
            :confined="false"
            :scrollspy="false"
            :editableContent="false"
            class="box-lottery-list-item-prize-name"
          >
            {{ providedPrizes[index + front.length]?.name || '--' }}
          </UiText>
        </Resizable>
      </ResizableProvider>
    </ResizableProvider>

    <!-- 恭喜获得 -->
    <CongratulationsModal
      v-if="providedRequired && setting.congratulations"
      v-model:setting="setting.congratulations"
      :show="showCongratulations"
      :prize="prize"
      @close="handleClose"
    />
  </Resizable>
</template>

<script lang="ts">
import type { CSSProperties } from 'vue';
import type { CommonBackgroundSetting, CommonSetting } from '@bish/types/src'
import type { StatefulComponent } from '@bish/types/src/admin';
import type { ActivityInfoConfigLottery } from '@bish/api/src/activity';
import type { LotteryPrizeDrawData } from '@bish/api/src/lottery';
import type { TextSetting } from '../../ui/text/index.vue';
import type { CongratulationsModalSetting } from '../congratulations-modal.vue';

export type LotteryPrize = ActivityInfoConfigLottery[0]['lottery_prize']
export type BoxPositionItem = {
  x: 0
  y: 0
}

export interface BoxLotterySetting extends CommonBackgroundSetting {
  /**
   * 盒子
   */
  box: CommonBackgroundSetting & {
    /**
     * 奖励配置
     */
    prize: CommonSetting
    /**
     * 奖励名称配置
     */
    prizeName: TextSetting
  }
  /**
   * 抽奖按钮
   */
  drawBtn?: CommonBackgroundSetting & {
    /**
     * 没有抽奖次数按钮图片
     */
    noDrawNumBtnImg?: string
    /**
     * 剩余次数
     */
    remaining: TextSetting & {
      /**
       * 是否显示
       */
      show: boolean
    }
    /**
      * 没有抽奖次数提示
      */
    notDrawNumTip?: string
    /**
      * 没有抽奖次数跳转链接
      */
    notDrawNumUrl?: string
  }
  /**
   * 恭喜弹窗
   */
  congratulations?: CongratulationsModalSetting
  /**
   * 格子位置，用于调整格子位置，随意调整
   */
  boxPosition?: BoxPositionItem[]
}

export interface BoxLotteryProps {
  /**
   * 配置
   */
  setting: BoxLotterySetting
  /**
   * 奖品列表
   */
  lotteryPrizes: LotteryPrize
  /**
   * 剩余抽奖次数
   */
  remaining: number
  /**
   * 当前奖品
   */
  prize: LotteryPrizeDrawData
  /**
   * 开始抽奖前
   */
  beforeStart: () => Promise<boolean>
  /**
   * 几宫格，默认 9，合法值 9、6、3、1
   */
  boxGrid?: number
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export type BoxLotteryIns = {
  handleDraw: () => void,
}

export default {
  name: 'box-lottery',
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, computed, defineExpose } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import useAdminStore from '@bish/store/src/modules/admin';
import Resizable from '../../ui/resizable/index.vue';
import ResizableProvider from '../../ui/resizable/resizable-provider.vue';
import UiText from '../../ui/text/index.vue';
import CongratulationsModal from '../congratulations-modal.vue';
import { navigateScroll } from '../../__utils/location';

const boxMap: Record<number, number[]> = {
  9: [0, 1, 2, 3, 5, 6, 7, 8],
  6: [0, 1, 2, 3, 4, 5],
  3: [0, 1, 2],
  1: [0],
};
const trackMap: Record<number, number[]> = {
  9: [0, 1, 2, 5, 8, 7, 6, 3],
  6: [0, 1, 2, 5, 4, 3],
  3: [0, 1, 2],
  1: [0],
};

const props = withDefaults(defineProps<BoxLotteryProps>(), {
  setting: () => ({} as BoxLotterySetting),
  lotteryPrizes: () => ([]),
  remaining: 0,
  prize: () => ({} as LotteryPrizeDrawData),
  beforeStart: undefined,
  boxGrid: 9,
});

const emits = defineEmits<{
  (event: 'update:setting', value: BoxLotterySetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
  (event: 'end'): void;
}>();

// 当前激活的 box
const activeIndex = ref(-1);
// 锁
const drawLock = ref(false);

const setting = useVModel(props, 'setting', emits);

const adminStore = useAdminStore();

const [showCongratulations, toggleShowCongratulations] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showCongratulations' });

const boxGrid = computed(() => {
  return props.boxGrid || 9;
});

const box = computed(() => {
  return boxMap[boxGrid.value];
});

// 这里记录顺时针运动轨迹
const track = computed(() => {
  return trackMap[boxGrid.value];
});

const front = computed(() => {
  return box.value.slice(0, 4);
});

const tail = computed(() => {
  return box.value.slice(4);
});

const boxItemStyle = computed<CSSProperties>(() => {
  return {
    height: 'auto',
  };
});

const providedPrizes = computed(() => {
  return props.lotteryPrizes;
});

const remainingInterpolation = computed(() => {
  return {
    num: props.remaining || '0',
  };
});

const providedRequired = computed(() => {
  // 兼容旧数据
  return props.setting.congratulations?.required ?? true;
});

const handleDrawEnd = () => {
  if (providedRequired.value) {
    toggleShowCongratulations(true);
  } else {
    showToast(`恭喜获得 ${props.prize.name}`);
  }
  emits('end');
};

/**
 * 时间间隔
 */
const INTERVAL = 30;
/**
 * 匀速时间
 */
const CONSTANT_SPEED_TIME = 80;
/**
 * 时间临界值
 */
const THRESHOLD = 300;

let timer1: number | null = null;
let time1 = THRESHOLD; // 初始时间间隔长度
/**
 * 从慢到快
 */
const start = () => {
  if (drawLock.value) {
    return;
  }
  drawLock.value = true;
  // 重置数据
  time1 = 300;
  activeIndex.value = -1;

  const loop = () => {
    timer1 = setTimeout(() => {
      if (time1 > CONSTANT_SPEED_TIME) {
        activeIndex.value = activeIndex.value < track.value.length - 1 ? activeIndex.value + 1 : 0;
        time1 -= INTERVAL;
        loop();
      } else {
        // 结束加速运动
        if (timer1) {
          clearTimeout(timer1);
          timer1 = null;
        }
        // 进入匀速运动
        uniformMotion();
      }
    }, time1);
  }
  loop();
};

let timer2: number | null = null;
let timeCount2 = 0;
/**
 * 匀速运动
 */
const uniformMotion = () => {
  // 重置数据
  timeCount2 = 0;

  const loop = () => {
    timeCount2 += CONSTANT_SPEED_TIME;

    // 1.5秒后结束匀速运动
    if (timeCount2 >= 1500 && timer2) {
      clearTimeout(timer2);
      timer2 = null;
      // 进入减速运动
      end();
      return;
    }
    activeIndex.value = activeIndex.value < track.value.length - 1 ? activeIndex.value + 1 : 0;
    timer2 = setTimeout(() => {
      loop();
    }, CONSTANT_SPEED_TIME);
  }
  loop();
};

let timer3: number | null = null;
let time3 = CONSTANT_SPEED_TIME; // 初始时间间隔长度
/**
 * 减速运动
 */
const end = () => {
  // 重置数据
  time3 = CONSTANT_SPEED_TIME;

  const loop = () => {
    timer3 = setTimeout(() => {
      // 到达临界时间
      if (time3 > THRESHOLD - 120) {
        // 结束运动
        if (timer3) {
          // 这里当达到临界时间之后，再运动到中奖奖品位置
          const prizeIndex = props.lotteryPrizes?.findIndex((item) => item.id === props.prize.id) ?? -1;
          const trackIndex = track.value.findIndex((item) => item === box.value[prizeIndex]);

          activeIndex.value = activeIndex.value < track.value.length - 1 ? activeIndex.value + 1 : 0;

          if (prizeIndex !== -1 && activeIndex.value === trackIndex) {
            clearTimeout(timer3);
            timer3 = null;
            drawLock.value = false;
            // 派发抽奖结束事件到外部
            setTimeout(() => {
              handleDrawEnd();
            }, 500);
            return;
          }
          loop();
        }
      } else {
        activeIndex.value = activeIndex.value < track.value.length - 1 ? activeIndex.value + 1 : 0;
        time3 += INTERVAL * 2;
        loop();
      }
    }, time3);
  }
  loop();
};

const run = async () => {
  // 先执行接口获取奖品id，之后再跑动画
  // 没有抽奖次数也可以在 beforeStart 中自行处理
  const res = await props.beforeStart();
  // 没有抽奖次数
  if (!res || !props.remaining) {
    if (setting.value.drawBtn?.notDrawNumTip) {
      showToast(setting.value.drawBtn?.notDrawNumTip)
    }
    // 没有抽奖次数，滚动到指定位置
    const cid = setting.value.drawBtn?.notDrawNumUrl
    if (cid) {
      navigateScroll(cid)
    }
    return;
  }
  // 格子数为1，直接结束，这里类似领取任务奖励
  if (props.boxGrid === 1) {
    handleDrawEnd();
    return;
  }
  start();
};

const handleDraw = () => {
  run();
};

const handleClose = () => {
  toggleShowCongratulations(false);
};

const position = (index: number) => {
  return props.setting.boxPosition?.[index] || { x: 0, y: 0 };
};

defineExpose({
  handleDraw,
});
</script>

<style lang="less">
.box-lottery {
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-list {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-around;

    &-item {
      background-repeat: no-repeat;
      background-size: 100% 100%;

      &-content {
        position: relative;
        background-repeat: no-repeat;
        background-size: 100% auto;

        &__active {
          filter: brightness(115%);
        }
      }

      &__disabled {
        filter: grayscale(100%);
      }

      &-prize {
        &-img,
        &-slot {
          width: 100%;
          height: 100%;
        }

        &-name {}

        &-placeholder {
          background-color: #f4f4f4;
        }
      }
    }
  }

  &-prize {
    &-img {
      width: 100%;
      height: 100%;
    }

    &-name {}
  }
}
</style>