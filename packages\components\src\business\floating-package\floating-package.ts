import type { CommonBackgroundSetting } from '@bish/types/src';
import type { PopupSetting } from '../../common/popup.vue';
import type { TextSetting } from '../../ui/text/index.vue';
import type { ImageSetting } from '../../ui/image/index.vue';
import type { ServerRolePickerSetting } from '../server-role-picker.vue';

export interface FloatingPackageSetting extends CommonBackgroundSetting {
  /**
   * 任务id
   */
  taskId: number
  /**
   * 是否固定在页面，1是 0否，默认 1，设置后将使用 fixed 布局
   */
  affix: number
  /**
   * 是否要求领取账号是流失用户，默认 0
   */
  accountStatus: number
  /**
   * 弹窗配置
   */
  modal: PopupSetting & {
    /**
     * 提示
     */
    tip: TextSetting
    /**
     * 道具图片
     */
    item: ImageSetting
    /**
     * 区服-角色选择器
     */
    serverRolePicker: ServerRolePickerSetting
  }
} 
