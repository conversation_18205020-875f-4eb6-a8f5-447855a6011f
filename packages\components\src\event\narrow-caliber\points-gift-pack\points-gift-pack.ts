import type { CommonBackgroundSetting } from '@bish/types/src';
import type { ImageSetting } from '../../../ui/image/index.vue';
import type { ItemsSetting } from '../../../business/items.vue';
import type { ServerRolePickerSetting } from '../../../business/server-role-picker.vue';
import type { DownloadGameSetting as DownloadGameBtnSetting } from '../../../business/download-game.vue';

export interface PointsGiftPackSetting extends CommonBackgroundSetting {
  /**
   * 礼包 id
   */
  gifPackId: number
  /**
   * 道具展示
   */
  items: ItemsSetting
  /**
   * 区服-角色选择器
   */
  serverRolePicker: ServerRolePickerSetting
  /**
   * 立即领取按钮
   */
  receiveBtnImage: ImageSetting
  /**
   * 已领取按钮
   */
  receivedBtnImage: ImageSetting
  /**
   * 下载游戏按钮
   */
  downloadBtn: DownloadGameBtnSetting
  /**
   * 是否需要绑定角色，默认 0，若是则使用活动全局绑定角色组件进行绑角操作
   */
  bindRole: number
}