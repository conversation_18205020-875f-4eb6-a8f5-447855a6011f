<!--
* @Description: 游戏下载-弹窗
-->
<template>
  <Popup
    z-index="99"
    :show="show"
    :lock-scroll="false"
    v-model:setting="setting.modalSetting"
    @close="handleClose"
  >
    <!-- 内容文案 -->
    <UiText
      v-if="setting.modalSetting.content?.content"
      v-model:setting="setting.modalSetting.content"
      class="download-game-modal-content"
    >
      <div>{{ popupStore.gameDownloadGuideText || contentText }}</div>
    </UiText>

    <!-- 下载按钮 -->
    <DownloadGame
      v-if="setting.modalSetting.downloadBtn?.imgLink"
      v-model:setting="setting.modalSetting.downloadBtn"
      :status="status"
    />
  </Popup>
</template>

<script lang="ts">
import type { StatefulComponent } from '@bish/types/src/admin';
import type { TextSetting } from '../ui/text/index.vue';
import type { PopupSetting } from '../common/popup.vue';
import { defaultConfig as downloadGameDefaultConfig } from './download-game.vue';
import type { DownloadGameSetting as DownloadGameBtnSetting } from './download-game.vue';

export interface DownloadGameModalSetting extends PopupSetting {
  /**
   * 内容
   */
  content: TextSetting
  /**
   * 下载按钮配置
   */
  downloadBtn: DownloadGameBtnSetting
}

export interface DownloadGameSetting {
  /**
   * 弹窗
   */
  modalSetting: DownloadGameModalSetting
}

export interface DownloadGameProps {
  /**
   * 配置
   */
  setting: DownloadGameSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): DownloadGameSetting => {
  return {
    modalSetting: {
      x: 0,
      y: 0,
      width: 375,
      height: 343,
      bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724933359753_.png',
      content: {
        x: 74,
        y: 142,
        width: 229,
        height: 60,
        fontSize: 12,
        color: '#732508',
        align: 'center',
        content: '您在[{{serverName}}]区服未有角色，请点击下载游戏按钮，前往指定区服完成创角~',
      },
      closeBtn: {
        x: 315,
        y: 31,
        width: 19,
        height: 19,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724917975880_.png',
      },
      downloadBtn: {
        ...downloadGameDefaultConfig(),
        x: 86,
        y: 248,
        width: 205,
        height: 57,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724933362543_.png',
      },
    },
  };
};

export default {
  name: 'download-game-modal',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import usePopupStore from '@bish/store/src/modules/popup';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import UiText from '../ui/text/index.vue';
import { interpolateString } from '../__utils/text';
import Popup from '../common/popup.vue';
import DownloadGame from './download-game.vue';

const props = withDefaults(defineProps<DownloadGameProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: DownloadGameSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityStore = useActivityStore();
const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);

const [show, toggleShow] = useControllableStatus(props, emits, { fieldName: 'showDownloadGameModal' });

const serverInterpolation = computed(() => {
  const { init: { server_names = [] } = {} } = activityStore.activityInfo;
  const serverName = server_names?.join(',') || '--';
  return {
    serverName,
  }
});

const contentText = computed(() => {
  return interpolateString(props.setting.modalSetting.content.content || '', serverInterpolation.value);
});

watch(
  () => popupStore.showDownloadGameModal,
  (newVal) => {
    toggleShow(newVal);
  },
);

const handleClose = () => {
  if (!popupStore.showGameDownloadGuide) {
    toggleShow(false);
  }
  popupStore.setDownloadGameModal(false);
};
</script>

<style lang="less">
.download-game-modal-content {
  font-weight: 600;
  line-height: 1.6;
}
</style>