import type { ExtractPropTypes } from 'vue'
import { makeArrayProp, makeNumericProp, makeObjectProp, makeStringProp, truthProp } from '@bish/ui/src/utils/props'

export interface PickerOption {
  text?: string | number
  value?: string | number
  disabled?: string
  children?: PickerOption[]
  className?: string | number
  [key: PropertyKey]: any
}

export interface PickerTouchParams {
  startY: number
  endY: number
  startTime: number
  endTime: number
  lastY: number
  lastTime: number
}

export interface PickerFieldNames {
  text?: string
  value?: string
  children?: string
  className?: string
}

export interface PickerBaseEvent {
  selectedValues: (string | number)[]
  selectedOptions: PickerOption[]
}

export interface PickerChangeEvent extends PickerBaseEvent {
  columnIndex: number
}

export const pickerColumnsType = ['single', 'multiple', 'cascade'] as const
export type PickerColumnsType = (typeof pickerColumnsType)[number]

export const pickerProps = {
  /**
   * @description 默认选中项
   */
  modelValue: makeArrayProp<string | number>([]),
  /**
   * @description 对象数组，配置每一列显示的数据
   */
  columns: makeArrayProp<PickerOption | PickerOption[]>([]),
  /**
   * @description 自定义 columns 中的字段
   */
  columnsFieldNames: makeObjectProp<PickerFieldNames>({}),
  /**
   * @description 是否显示顶部导航
   */
  showToolbar: truthProp,
  /**
   * @description 设置标题
   */
  title: makeStringProp(''),
  /**
   * @description 确定按钮文案
   */
  confirmButtonText: makeStringProp('确认'),
  /**
   * @description 取消按钮文案
   */
  cancelButtonText: makeStringProp('取消'),
  /**
   * @description 是否开启3D效果
   */
  threeDimensional: Boolean,
  /**
   * @description 惯性滚动时长
   */
  swipeDuration: makeNumericProp(1000),
  /**
   * @description 可见的选项个数
   */
  visibleOptionNum: makeNumericProp(6),
  /**
   * @description 选项高度
   */
  optionHeight: makeNumericProp(44),
}

export type PickerProps = ExtractPropTypes<typeof pickerProps>

export const pickerEmits = {
  'update:modelValue': (val: (string | number)[]) => val instanceof Object,
  'change': (evt: PickerChangeEvent) => evt instanceof Object,
  'confirm': (evt: PickerBaseEvent) => evt instanceof Object,
  'cancel': (evt: PickerBaseEvent) => evt instanceof Object,
}

export type PickerEmits = typeof pickerEmits