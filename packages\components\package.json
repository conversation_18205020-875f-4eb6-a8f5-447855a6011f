{"name": "@bish/components", "version": "0.0.26", "description": "", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "files": ["src"], "keywords": [], "author": "", "license": "ISC", "dependencies": {"@bish/api": "workspace:^", "@bish/core": "workspace:^", "@bish/hooks": "workspace:^", "@bish/lang": "workspace:^", "@bish/shared": "workspace:^", "@bish/store": "workspace:^", "@bish/types": "workspace:^", "@bish/ui": "workspace:^", "@bish/utils": "workspace:^", "@vueuse/core": "10.9.0", "copy-to-clipboard": "^3.3.3", "modern-screenshot": "^4.5.5", "qrcode": "^1.5.4", "swiper": "^11.2.6"}, "peerDependencies": {"pinia": "2.0.36", "vue": ">=3.2.0"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@types/qrcode": "^1.5.5", "@vue/tsconfig": "^0.1.3"}, "optionalDependencies": {"vant": "4.9.3"}, "publishConfig": {"registry": "http://sy-registry.shiyue.com"}}