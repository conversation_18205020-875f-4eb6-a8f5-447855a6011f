<!--
* @Description: 完成活动提示弹窗
-->
<template>
  <Popup
    z-index="99"
    :show="show"
    :resizable="false"
    v-model:setting="setting.modalSetting"
    @close="handleClose"
  />
</template>

<script lang="ts">
import type { StatefulComponent } from '@bish/types/src/admin';

export interface PeakClimbingProps {
  /**
   * 配置
   */
  setting: PeakClimbingSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export default {
  name: 'peak-climbing',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import usePopupStore from '@bish/store/src/modules/popup';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import Popup from '../../common/popup.vue';
import type { PeakClimbingSetting } from './peak-climbing';
import { defaultConfig } from './index';

const props = withDefaults(defineProps<PeakClimbingProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: PeakClimbingSetting): void;
  (event: 'close'): void;
}>();

const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);

const [show, toggleShow] = useControllableStatus(props, emits, { fieldName: 'showPeakClimbingModal' });

watch(
  () => popupStore.showPeakClimbing,
  (newVal) => {
    toggleShow(newVal);
  },
);

const handleClose = () => {
  // 同步数据
  if (show && !popupStore.showPeakClimbing) {
    toggleShow(false);
  }
  popupStore.setShowPeakClimbing(false);
};
</script>
