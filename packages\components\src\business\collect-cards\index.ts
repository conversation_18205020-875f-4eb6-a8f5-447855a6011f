import type { CollectCardsSetting } from './collect-cards';
import { defaultConfig as defaultShareGuideConfig } from '../share-guide';
import { defaultConfig as collectCardsGroupDefaultConfig } from './components/collect-cards-group';
import { defaultConfig as collectCardsListDefaultConfig } from './components/collect-cards-list';
import { defaultConfig as collectCardsScratchDefaultConfig } from './components/collect-cards-scratch';
import { defaultConfig as collectCardsGiveDefaultConfig } from './components/collect-cards-give';
import { defaultConfig as collectCardsAskForDefaultConfig } from './components/collect-cards-ask-for';

export * from './collect-cards';

export const defaultConfig = (): CollectCardsSetting => {
  const shareGuideConfig = defaultShareGuideConfig();
  const scratchConfig = collectCardsScratchDefaultConfig();
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 565,
    lotteryId: 0,
    needBindRole: 1,
    group: collectCardsGroupDefaultConfig(),
    list: collectCardsListDefaultConfig(),
    scratch: {
      ...scratchConfig,
      title: {
        ...scratchConfig.title,
        enabled: false,
      },
      closeBtn: {
        x: 174,
        y: 353,
        width: 28,
        height: 29,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744288469350-close.png',
      },
    },
    give: collectCardsGiveDefaultConfig(),
    askFor: collectCardsAskForDefaultConfig(),
    completeTip: {
      x: 0,
      y: 526,
      width: 375,
      height: 30,
      fontSize: 12,
      color: '#FFF48C',
      align: 'center',
      fontWeight: true,
      enabled: true,
      content: '恭喜成为第 <span style="font-size: 1.6em; color: #FFFADC;">{{num}} 位</span> 集齐全套卡片的御灵师！',
    },
    shareGuide: {
      ...shareGuideConfig,
      copyLinkBtn: {
        ...shareGuideConfig.copyLinkBtn,
        enabled: false,
      },
      title: {
        x: 15,
        y: 41,
        width: 336,
        height: 134,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250412/1744453173499-share-guide-title.png',
      },
      closeBtn: {
        x: 160,
        y: 160,
        width: 61,
        height: 44,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250412/1744453176843-share-guide-close.png',
        enabled: true,
      },
    },
  };
}; 
