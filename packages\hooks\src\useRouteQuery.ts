import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { replaceQueryProperties } from '@bish/utils/src/utils'
import useActivityPageStore from '@bish/store/src/modules/activityPage'
import { useEnv } from './useEnv'

/**
 * 获取路由参数 hook，适配 h5 及 uni-app 小程序
 * @returns 
 */
export default function useRouteQuery<R extends Record<string, any>>() {
  const queryVal = ref<R>({} as R);

  const activityPageStore = useActivityPageStore()

  const { VITE_APP_TYPE } = useEnv()

  if (VITE_APP_TYPE !== 'wx') {
    const route = useRoute()
    const query = replaceQueryProperties(route)
    queryVal.value = query
  }

  if (VITE_APP_TYPE === 'wx') {
    const query = replaceQueryProperties({ query: activityPageStore.routeQuery })
    queryVal.value = query
  }

  return {
    query: queryVal,
  }
}
