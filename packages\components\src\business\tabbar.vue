<!--
* @Description: 标签栏
-->
<template>
  <Resizable
    class="tabbar"
    :class="{ 'tabbar__fixed': !adminStore.editable }"
    :x="0"
    :y="0"
    :width="setting.width"
    :height="setting.height"
    :resizable="false"
    :movable="false"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <div
      v-for="(item, index) in setting.items"
      class="tabbar-item"
      @click="() => handleToPage(item, index)"
    >
      <img class="tabbar-item-icon" :src="index !== active ? item.defaultIcon : item.activeIcon" />
    </div>
  </Resizable>
</template>

<script lang="ts">
import type { CommonBackgroundSetting } from '@bish/types/src';

export interface TabbarSetting extends CommonBackgroundSetting {
  /**
   * 导航项
   */
  items: {
    /**
     * 导航名称
     */
    name: string
    /**
     * 导航地址
     */
    path: string
    /**
     * 默认图标
     */
    defaultIcon: string
    /**
     * 激活图标
     */
    activeIcon: string
    /**
     * 链接类型，1：站内链接 2：站外链接
     */
    pathType: 1 | 2
  }[]
}

export interface TabbarProps {
  /**
   * 配置
   */
  setting: TabbarSetting
}

/**
 * 站内地址
 */
export const PATH_TYPE_ON_SITE = 1;

/**
 * 站外地址
 */
export const PATH_TYPE_OFF_SITE = 2;

export const PATH_TYPE_OPTIONS = [
  {
    label: '站内',
    value: 1,
  },
  {
    label: '站外',
    value: 2,
  },
]

export const defaultConfigItem = (): TabbarSetting['items'][0] => {
  return {
    name: '导航项',
    path: '',
    defaultIcon: '',
    activeIcon: '',
    pathType: 1,
  };
};

export const defaultConfig = (): TabbarSetting => {
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 1,
    bgImage: '',
    items: [
      {
        name: '导航项1',
        path: '',
        defaultIcon: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-2/1722592869495_.png',
        activeIcon: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-2/1722592790460_.png',
        pathType: 1,
      },
      {
        name: '导航项2',
        path: '',
        defaultIcon: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-2/1722592770318_.png',
        activeIcon: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-2/1722592778044_.png',
        pathType: 1,
      },
      {
        name: '导航项3',
        path: '',
        defaultIcon: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-2/1722592889416_.png',
        activeIcon: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-2/1722592897573_.png',
        pathType: 1,
      },
    ],
  };
};

export default {
  name: 'tabbar',
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, watchEffect } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { parseQueryString, additionalLinkParameters } from '@bish/utils/src/utils';
import Resizable from '../ui/resizable/index.vue';

// 毕昇活动地址域名
const BISH_URL_LIST = [
  'test-web.shiyue.com',
  'bish.shiyue.com',
];

const props = withDefaults(defineProps<TabbarProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TabbarSetting): void;
}>();

const active = ref(0);

const activityStore = useActivityStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

let currentPage = '';
let initialQuery: Record<string, any> = {};
const initializeCurrentPage = () => {
  // #ifdef H5
  currentPage = window.location.href;
  initialQuery = parseQueryString(window.location.href);
  // #endif

  // TODO: 小程序兼容
};
initializeCurrentPage();

watchEffect(() => {
  const index = props.setting.items.findIndex(item => currentPage.includes(item.path));
  if (index !== -1) {
    active.value = index;
  }
});

const openPage = (path: string) => {
  // 后台端编辑模式下，点击跳转链接时，弹出确认框，避免误操作
  if (adminStore.editable) {
    if (window.confirm('是否确认进行跳转？')) {
      window.open(path, '_blank');
    }
    return;
  }
  window.open(path, '_blank');
};

const handleToPage = (item: TabbarSetting['items'][0], index: number) => {
  // active.value = index;
  if (item.path === '') {
    return;
  }
  let newPath = item.path;
  // 站内链接，共享参数
  if (item.pathType === PATH_TYPE_ON_SITE) {
    newPath = additionalLinkParameters(initialQuery, item.path, false);
  }

  if (!adminStore.editable) {
    if (item.pathType === PATH_TYPE_ON_SITE) {
      window.location.replace(newPath);
    } else {
      window.open(newPath, '_blank');
    }
    return;
  }

  const regex = /^https?:\/\/([\w.-]+)\//;
  const match = item.path.match(regex);
  
  // 若是毕昇活动地址，则跳转相关的后台配置页面
  if (
    match &&
    item.pathType === PATH_TYPE_ON_SITE &&
    BISH_URL_LIST.includes(match[1])
  ) {
    const params = parseQueryString(item.path);
    const { event_page, ...rest } = params;
    if (event_page) {
      const adminPath = additionalLinkParameters(
        {
          ...rest,
          id: event_page,
        },
        window.location.href,
      );
      // TODO: 询问有内容未保存，是否需要保存后再进行跳转
      // window.location.replace(adminPath);
    } else {
      openPage(item.path);
    }
  } else {
    openPage(newPath);
  }
};
</script>

<style lang="less">
.tabbar {
  display: flex;
  align-items: flex-end;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: absolute !important;
  top: auto !important;
  bottom: 0;
  height: auto !important;

  &-item {
    flex: 1;
    cursor: pointer;
    font-size: 0;

    &-icon {
      width: 100%;
      height: auto;
    }
  }

  &__fixed {
    position: fixed !important;
    left: 50% !important;
    z-index: 98;
    transform: translate3d(-50%, 0, 0);
  }
}
</style>