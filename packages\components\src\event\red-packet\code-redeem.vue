<!--
 * @Description: 红包口令兑换
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="code-redeem"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 表单配置 -->
      <Resizable
        v-model:x="setting.form.position.x"
        v-model:y="setting.form.position.y"
        v-model:width="setting.form.position.width"
        v-model:height="setting.form.position.height"
        class="code-redeem-form"
      >
        <div class="code-redeem-form-item">
          <div class="code-redeem-form-item-control">
            <div class="code-redeem-form-input-wrapper">
              <input
                v-model="gameCode"
                placeholder="请输入兑换口令"
                class="code-redeem-form-input"
                :style="inputStyle"
              />
              <div
                v-if="gameCode.length"
                class="code-redeem-form-input-clear"
                @click="handleClearInput"
              >
                <div class="code-redeem-form-input-clear-icon" :style="clearIconStyle">
                  <van-icon name="clear" :size="pxTransform(16)" :color="themeColor" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </Resizable>
  
      <!-- 粘贴内容按钮 -->
      <UiImage
        v-if="setting.pasteBtn?.imgLink"
        v-model:setting="setting.pasteBtn"
        @click.stop="handlePaste"
      />

      <!-- 区服-角色选择器 -->
      <ServerRolePicker
        v-model:setting="setting.serverRolePicker"
        v-model:open="showServerRolePicker"
        v-model="serverRole"
        placeholder="请选择兑换角色"
        @click="handleShowServerRolePicker"
      >
        <template #default="{ selectedServer, selectedRole }">
          {{ `兑换角色：${selectedServer?.text || ''}-${selectedRole?.text || ''}` }}
        </template>
      </ServerRolePicker>
  
      <!-- 领取按钮 -->
      <UiImage v-model:setting="setting.confirmBtn" @click="handleCheckReceive" />
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
import type { ImageSetting } from '../../ui/image/index.vue';
import type { CommonBackgroundSetting } from '@bish/types/src/index';
import type { ResizableProps } from '../../ui/resizable/index.vue';
import type { ServerRolePickerSetting } from '../../business/server-role-picker.vue';
import { defaultConfig as serverRolePickerDefaultConfig } from '../../business/server-role-picker.vue';

export interface CodeRedeemSetting extends CommonBackgroundSetting {
  /**
   * 表单配置
   */
  form: {
    /**
     * 位置
     */
    position: ResizableProps
    /**
     * 输入框配置
     */
    input: {
      /**
       * 高度
       */
      height?: number
      /**
       * 背景
       */
      background?: string
      /**
       * 颜色
       */
      color?: string
    }
  }
  /**
   * 粘贴内容按钮
   */
  pasteBtn?: ImageSetting
  /**
   * 领取按钮
   */
  confirmBtn: ImageSetting
  /**
   * 区服-角色选择器
   */
  serverRolePicker: ServerRolePickerSetting
}

export default {
  options: {},
  name: 'recharge-activity-code-redeem',
}

export const defaultConfig = (): CodeRedeemSetting => {
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 219,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-30/1724996101652_.png',
    form: {
      position: {
        x: 40,
        y: 37,
        width: 212,
        height: 32,
      },
      input: {
        height: 32,
        background: '#FAD86A',
        color: '#95740C',
      },
    },
    pasteBtn: {
      x: 258,
      y: 37,
      width: 79,
      height: 33,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-30/1724996109005_.png',
    },
    confirmBtn: {
      x: 86,
      y: 117,
      width: 205,
      height: 57,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-30/1724996105622_.png',
    },
    serverRolePicker: serverRolePickerDefaultConfig(),
  }
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, ref, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useToggle, useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useActivityStore from '@bish/store/src/modules/activity';
import usePopupStore from '@bish/store/src/modules/popup';
import { pxTransform } from '@bish/utils/src/viewport';
import { exchangeRedeemCode } from '@bish/api/src/redEnvelope';
import { useAccountBindWx } from '@bish/hooks/src/business/useAccountBindWx';
import Resizable from '../../ui/resizable/index.vue';
import ResizableProvider from '../../ui/resizable/resizable-provider.vue';
import UiImage from '../../ui/image/index.vue';
import ServerRolePicker from '../../business/server-role-picker.vue';
import type { ServerRolePickerValue } from '../../business/server-role-picker.vue';

export interface CodeRedeemProps {
  /**
   * 配置
   */
  setting: CodeRedeemSetting
}

const props = withDefaults(defineProps<CodeRedeemProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: CodeRedeemSetting): void
}>();

const gameCode = ref('');
const serverRole = ref<ServerRolePickerValue>([]);

const activityPageStore = useActivityPageStore();
const activityStore = useActivityStore();
const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);
const [showServerRolePicker, toggleShowServerRolePicker] = useToggle();

const { handleBindGuide } = useAccountBindWx();

const inputStyle = computed<CSSProperties>(() => {
  const { setting } = props;
  return {
    color: setting.form.input.color,
    backgroundColor: setting.form.input.background,
    height: pxTransform(setting.form.input.height!),
    padding: `0 ${pxTransform(32)} 0 ${pxTransform(8)}`,
    width: '100%',
    fontSize: pxTransform(16),
    fontWeight: 700,
    border: 'none',
    outline: 'none',
  };
});

const themeColor = computed(() => {
  return activityPageStore.mergedPageConfig.themeColor;
});

const clearIconStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(16),
    height: pxTransform(16),
  };
});

const handleClearInput = () => {
  gameCode.value = '';
};

const handlePaste = () => {
  const clipboard = navigator.clipboard;
  if (!clipboard) {
    showToast('当前浏览器不支持一键粘贴功能');
    return;
  }
  clipboard
    .readText()
    .then((clipText) => {
      gameCode.value = clipText;
    })
    .catch((err) => {
      showToast('粘贴失败');
    });
};

const handleShowServerRolePicker = () => {
  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }

  if (!serverRole.value.length) {
    if (!activityStore.getServerOpenState()) {
      // TODO: 暂时没有这个组件
      showToast('缺少活动引导弹窗~');
      // popupStore.setActivityUrlGuide(true);
      return;
    } else {
      // 已开服、指定区服未创过角
      if (!activityStore.hasLimitServerRole) {
        popupStore.setDownloadGameModal(true);
        return;
      }
    }
  }
  toggleShowServerRolePicker(true);
};

const handleReceive = async () => {
  try {
    const [server_id, role_id] = serverRole.value;
    const params = {
      act_id: activityStore.activityInfo?.init?.id,
      act_acc_id: activityStore.activityAccountInfo?.act_acc_id,
      role_id: server_id && role_id ? `${role_id}_${server_id}` : activityStore.activityAccountInfo?.role_id,
      red_code: gameCode.value,
    };
    const { code } = await exchangeRedeemCode(params);
    if (code === 0) {
      showToast('领取成功，请注意查收');
      activityStore.getComponentWithUserInfo();
    }
  } catch (error) {
    console.warn('口令兑换失败', error);
  }
}

/**
 * 校验兑换流程
 */
const handleCheckReceive = () => {
  const pass = activityStore._checkIn();
  // 1、校验登录
  if (!pass) {
    return;
  }
  // 2、校验微信绑定 0未绑定微信 1已绑定微信
  if (!activityStore.activityAccountInfo?.wx_bind_status) {
    handleBindGuide();
    return;
  }
  // 3、校验兑换角色
  if (!serverRole.value?.length && !activityStore.bindRoleInfo?.server_name) {
    handleShowServerRolePicker();
    return;
  }
  // 4、校验兑换口令
  if (gameCode.value === '') {
    showToast('请先输入口令');
    return;
  }
  handleReceive();
}
</script>

<style lang="scss" scoped>
.code-redeem {
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-form {
    &-input-wrapper {
      position: relative;
    }

    &-input {
      &-clear {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
        padding: 8PX;

        &-icon {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      &::placeholder {
        color: #ffffff;
      }

      // uni-app
      .uni-input-placeholder {
        color: #ffffff;
      }
    }
  }
}
</style>
