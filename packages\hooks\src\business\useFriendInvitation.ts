import { ref, watch, onMounted } from 'vue'
import useUserStore from '@bish/store/src/modules/user'
import useActivityStore from '@bish/store/src/modules/activity'
import usePopupStore from '@bish/store/src/modules/popup'
import { postActivityAcceptInvite } from '@bish/api/src/activity'
import { postRedEnvelopeTaskInvite } from '@bish/api/src/redEnvelope'
import { allowMultipleToast, showToast, showLoadingToast } from '@bish/ui/src/toast'
import i18n from '@bish/lang/src';
import useRouteQuery from '@bish/hooks/src/useRouteQuery'
import useControllableStatus from './useControllableStatus'

/**
 * 接受好友邀请 hook
 * @param keyName 邀请弹窗标识
 * @param checkIn 活动用户状态检查方法
 * @param initiative 是否主动接受邀请，若是 则会调用 接受邀请 接口，否则会把邀请人的信息 带入 活动账号信息 接口
 * @returns
 */
export function useFriendInvitation(
  keyName: string,
  checkIn: (needBindRole: boolean) => boolean,
  initiative: boolean,
  callback: () => void,
  ...args: Parameters<typeof useControllableStatus>
) {

  const showInvitationFlag = ref(true)
  const joinFlag = ref(false)
  const showAuthModal = ref(false)

  const userStore = useUserStore()
  const activityStore = useActivityStore()
  const popupStore = usePopupStore()
  
  const { query } = useRouteQuery()

  const [showInvitation, toggleInvitation] = useControllableStatus(...args)
  

  const isInvited = () => {
    return query.value[keyName] === '1' && query.value.invite_code && query.value.invite_type
  }

  const friend = query.value.invite_user ? decodeURIComponent(query.value.invite_user as string) : ''

  /**
   * 是否是邀请人本身
   */
  const isInviter = () => {
    const inviteCode = query.value.invite_code ? decodeURIComponent(query.value.invite_code as string) : ''
    return activityStore.activityAccountInfo?.invite_code && activityStore.activityAccountInfo.invite_code === inviteCode
  }

  /**
   * 初始化显示邀请组队弹窗
   * @param delayed 是否即刻执行的
   * @returns
   */
  const initShowInvitation = async (delayed: boolean) => {
    if (isInvited()) {
      // 未登录，直接显示弹窗
      if (!delayed && !userStore.userData.token) {
        toggleInvitation(true)
        showInvitationFlag.value = false
        return
      }
      allowMultipleToast()
      // 已登录，自己不能邀请自己
      if (query.value.invite_code && userStore.userData.token && isInviter()) {
        showInvitationFlag.value = false
        // 这里加个延迟，缓解一下在小程序环境下 hideLoading 会打 toast 也一起隐藏的问题
        setTimeout(() => {
          showToast(i18n.global.t('box-inviteFriend-cantInviteSelf', '不能邀请自己'))
        }, 1500);
        // updateUrl()
      }
      // 如果已经被邀请过，则不显示邀请弹窗
      if (delayed && showInvitationFlag.value && activityStore.activityAccountInfo?.invited_status === 1) {
        showInvitationFlag.value = false
        // 这里加个延迟，缓解一下在小程序环境下 hideLoading 会打 toast 也一起隐藏的问题
        setTimeout(() => {
          showToast('已接受过邀请，无法多次接受好友邀请')
        }, 1500);
        return
      }
      // invite_user 默认 角色名-区服名
      // invite_user 可以是手机号，这个时候 [1] 为 undefined，这个时候取 [0]
      const splitArr = (query.value.invite_user as string).split('-')
      // decodeURIComponent(undefined) => 'undefined'
      const inviteUserName = query.value.invite_user && (splitArr[1] || splitArr[0])
        ? decodeURIComponent(splitArr[1] || splitArr[0])
        : ''
      // 未绑定角色，或者绑定角色名称与分享者的名称不一致则显示邀请弹窗
      if (delayed && showInvitationFlag.value && inviteUserName) {
        if ((splitArr[1] && !activityStore.bindRoleInfo?.role_name) || !isInviter()) {
          showInvitationFlag.value = false
          toggleInvitation(true)
        }
        if (isInviter()) {
          showInvitationFlag.value = false
        }
      }
    }
  }

  watch(
    () => activityStore.bindRoleInfo,
    () => {
      initShowInvitation(true)
    }
  )

  onMounted(() => {
    // 未登录状态，弹出邀请窗口
    initShowInvitation(false)
  })

  watch(
    () => activityStore.activityAccountInfo,
    (newVal) => {
      if (newVal?.account_id) {
        if (joinFlag.value) {
          acceptInvite()
          joinFlag.value = false
        }
      }
    }
  )

  const acceptInvite = async () => {
    allowMultipleToast()

    // 已经被邀请过
    if (activityStore.activityAccountInfo?.invited_status === 1) {
      setTimeout(() => {
        toggleInvitation(false)
      }, 500)
      if (!isInviter()) {
        setTimeout(() => {
          showToast('已接受过邀请，无法多次接受好友邀请')
        }, 800)
      }
      return
    }
    if (isInviter()) {
      toggleInvitation(false)
      // showToast('不能邀请自己')
      return
    }
    if (!initiative) {
      await activityStore.initActivityInfo(false, {
        inviteCode: query.value.invite_code as string,
        inviteType: query.value.invite_type ? +query.value.invite_type : undefined,
      })
      showToast('已接受好友邀请')
      toggleInvitation(false)
      // 执行接受好友邀请回调
      userStore.workAcceptInviteTask();
      return
    }
    // 接受邀请红包任务
    // if (keyName === 'invite_red_envelope') {
    //   acceptRedEnvelopeTaskInvite()
    //   return
    // }
    try {
      const res = await postActivityAcceptInvite({
        act_id: activityStore.activityInfo?.init?.id,
        act_acc_id: activityStore.activityAccountInfo?.act_acc_id,
        invite_code: query.value.invite_code as string,
        is_poster: 0,
      })
      if (res.code === 0) {
        showToast('已接受好友邀请')
        activityStore.initActivityInfo(false)
        // 执行接受好友邀请回调
        userStore.workAcceptInviteTask();
        callback?.()
      }
      toggleInvitation(false)
      // updateUrl()
    } catch (error) {
      console.warn('接受邀请失败')
    }
  }

  /**
   * 接受好友邀请
   */
  const handleAccept = async () => {
    const pass = checkIn(false)
    const callback = () => {
      // 这里为了防止重复添加任务之后一直执行回调
      if (showInvitation.value) {
        // 重新请求 init，并携带邀请参数
        // 如果已经登录并且已经进行绑定角色，直接接受邀请，否则等待登录 or 绑定角色之后再进行入队
        if (activityStore.activityAccountInfo?.account_id) {
          acceptInvite()
        } else {
          joinFlag.value = true
        }
      }
      return Promise.resolve()
    }
    if (!pass) {
      userStore.scheduler.add(callback)
      return
    }
    callback()
  }

  /**
   * 拒绝好友邀请
   */
  const handleReject = () => {
    toggleInvitation(false)
    // updateUrl()
  }

  /**
   *  更新地址链接
   */
  // const updateUrl = () => {
  //   // 3、更新当前活动URL
  //   const redirectUrl = additionalLinkParameters(
  //     {
  //       event_page: query.event_page,
  //     },
  //     window.location.href.split('?')[0]
  //   )
  //   window.location.href = redirectUrl
  // }

  /**
   * 邀请红包任务 - 接受邀请接口
   */
  const acceptRedEnvelopeTaskInvite = async () => {
    console.log('重新接受邀请')
    // 绑角校验
    // if (!activityStore.bindRoleInfo?.server_name) {
    //   // 未开服
    //   if (!activityStore.getServerOpenState()) {
    //     popupStore.setActivityUrlGuide(true)
    //     showInvitation.value = false
    //   } else {
    //     await activityStore.fetchAllRoleInfo()
    //     // 已开服、指定区服未创过角
    //     if (!activityStore.hasLimitServerRole) {
    //       popupStore.setGameDownloadGuide(true)
    //       showInvitation.value = false
    //     } else {
    //       activityStore.visibleBindOldRole = true
    //       joinFlag.value = true
    //     }
    //   }
    //   return
    // }
    const pass = checkIn(false)
    const callback = async () => {
      // 这里为了防止重复添加任务之后一直执行回调
      // 重新请求 init，并携带邀请参数
      // 如果已经登录并且已经进行绑定角色，直接接受邀请，否则等待登录 or 绑定角色之后再进行入队
      if (activityStore.activityAccountInfo?.account_id) {
        const toast = showLoadingToast({
          duration: 0,
          message: '正在接受邀请',
          forbidClick: true,
        })
        const params = {
          act_id: activityStore.activityInfo?.init?.id,
          act_acc_id: activityStore.activityAccountInfo?.act_acc_id,
          invite_code: query.value.invite_code as string,
        }
        try {
          const res = await postRedEnvelopeTaskInvite(params)
          toast.close()
          if (res.code === 0) {
            showToast('接受邀请成功')
            activityStore.initActivityInfo(true)
            // updateUrl()
          } else if ([13030, 13031].includes(res.code)) {
            // popupStore.setGameDownloadGuide(
            //   true,
            //   `请前往完成游戏下载，并在【${activityStore.activityInfo?.init?.server_names[0]}】完成${res.code === 13031 ? '预创角' : '创角'}`
            // )
          } else if (res.code === 3039) {
            // 需要实名
            showAuthModal.value = true
          } else {
            showToast(res.message)
          }
          toggleInvitation(false)
        } catch (error) {
          console.warn('接受邀请失败')
        }
      } else {
        joinFlag.value = true
      }
      return Promise.resolve()
    }
    if (!pass) {
      userStore.scheduler.add(callback)
      return
    }
    callback()
  }

  const handleCloseAuthModal = () => {
    showAuthModal.value = false
  }

  const handleAuthConfirm = () => {
    console.log('已完成实名认证')
    showAuthModal.value = false
    acceptRedEnvelopeTaskInvite()
  }

  return {
    showInvitation,
    toggleInvitation,
    handleAccept,
    handleReject,
    acceptRedEnvelopeTaskInvite,
    friend,
    showAuthModal,
    handleCloseAuthModal,
    handleAuthConfirm,
  }
}
