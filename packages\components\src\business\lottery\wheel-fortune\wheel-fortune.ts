import type { CommonBackgroundSetting, CommonSetting } from '@bish/types/src'
import type { ActivityInfoConfigLottery } from '@bish/api/src/activity';
import type { TextSetting } from '../../../ui/text/index.vue';
import type { ImageSetting } from '../../../ui/image/index.vue';
import type { CongratulationsModalSetting } from '../../congratulations-modal.vue';

export type LotteryPrize = ActivityInfoConfigLottery[0]['lottery_prize']

export type BoxPositionItem = {
  x: 0
  y: 0
}

export interface WheelFortuneSetting extends CommonBackgroundSetting {
  /**
   * 抽奖id
   */
  lotteryId: number
  /**
   * 宫格数
   */
  boxGrid: number
  /**
   * 转盘大小
   */
  size: number
  /**
   * 转盘激活颜色
   */
  activeColor: string
  /**
   * 是否需要绑定角色
   */
  needBindRole?: number
  /**
   * 完成进度
   */
  process: TextSetting
  /**
   * 盒子
   */
  box: CommonBackgroundSetting &{
    /**
     * 奖励配置
     */
    prize: CommonSetting
    /**
     * 奖励名称配置
     */
    prizeName: TextSetting
  }
  /**
   * 抽奖按钮
   */
  drawBtn?: CommonBackgroundSetting & {
    /**
     * 没有抽奖次数按钮图片
     */
    noDrawNumBtnImg?: string
    /**
     * 剩余次数
     */
    remaining: TextSetting
    /**
      * 没有抽奖次数提示
      */
    notDrawNumTip?: string
    /**
      * 没有抽奖次数跳转链接
      */
    notDrawNumUrl?: string
  }
  /**
   * 恭喜弹窗
   */
  congratulations?: CongratulationsModalSetting
  /**
   * 偏移角度，默认 0
   */
  angle: number
  /**
   * 转盘激活背景图片，与转盘激活颜色冲突，优先级更高
   */
  activeBg?: string
  /**
   * 指针图片
   */
  pointer?: ImageSetting
  /**
   * 格子位置，用于调整格子位置，随意调整
   */
  boxPosition?: BoxPositionItem[]
}