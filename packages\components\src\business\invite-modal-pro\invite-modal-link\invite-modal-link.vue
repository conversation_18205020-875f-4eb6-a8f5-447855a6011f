<template>
  <div class="invite-modal-link">
    <Popup
      z-index="99"
      :show="show"
      :lock-scroll="false"
      v-model:setting="setting"
      @close="handleClose"
      @ok="handleCopy"
      @click-overlay="handleClose"
    >
      <!-- 邀请文案 -->
      <UiText v-if="setting.content?.content" v-model:setting="setting.content" class="invite-modal-link-content" />
      <!-- 链接 -->
      <UiText
        v-if="setting.linkText?.content"
        v-model:setting="setting.linkText"
        :interpolation="linkInterpolation"
        class="invite-modal-link-link"
        :textStyle="linkTextStyle"
      />
      <!-- 复制按钮 -->
      <UiImage
        v-if="setting.copyLinkBtn?.imgLink"
        v-model:setting="setting.copyLinkBtn"
        @click="handleCopy"
      />
      <!-- 去邀请：微信小程序环境 -->
      <button
        v-if="showShareBtn"
        :style="shareBtnStyle"
        class="invite-modal-link-wx-btn"
        open-type="share"
        @click="uploadLogInvite"
      />

      <!-- 生成海报按钮 -->
      <UiImage
        v-if="setting.createPosterBtn?.imgLink"
        v-model:setting="setting.createPosterBtn"
        @click="handleCreatePoster"
      />
    </Popup>
  </div>
</template>

<script lang="ts">
export default {
  name: 'invite-modal-link',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import { useLog } from '@bish/hooks/src/useLog';
import { pxTransform } from '@bish/utils/src/viewport';
import { userAgent } from '@bish/utils/src/utils';
import { interpolateString, copyText } from '../../../__utils/text';
import UiText from '../../../ui/text/index.vue';
import UiImage from '../../../ui/image/index.vue';
import Popup from '../../../common/popup.vue';
import type { InviteModalLinkSetting } from './invite-modal-link';
import { defaultConfig } from './index';

export interface InviteModalLinkProps {
  /**
   * 配置
   */
  setting: InviteModalLinkSetting
  /**
   * 是否显示
   */
  show: boolean
  /**
   * 短链
   */
  shortLink: string
}

const props = withDefaults(defineProps<InviteModalLinkProps>(), {
  setting: defaultConfig,
  show: false,
  bindRole: true,
});

const emits = defineEmits<{
  (event: 'update:setting', value: InviteModalLinkSetting): void;
  (event: 'close'): void;
  (event: 'createPoster'): void;
}>();

const setting = useVModel(props, 'setting', emits);

const activityStore = useActivityStore();

const { uploadLog } = useLog();

const { isUserAgentType } = userAgent();

// 微信小程序环境
const isWeapp = computed(() => {
  return isUserAgentType === 'WX_MINI';
});

const showShareBtn = computed(() => {
  return isWeapp.value && activityStore.activityAccountInfo?.account_id && !activityStore.isSummit(false);
});

const linkTextStyle = computed(() => {
  return {
    padding: `0 ${pxTransform(6)}`,
  } as CSSProperties;
});

const linkInterpolation = computed(() => {
  return {
    link: props.shortLink,
  };
});

const shareBtnStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.okBtn?.x || 0),
    top: pxTransform(setting.value.okBtn?.y || 0),
    width: pxTransform(setting.value.okBtn?.width || 0),
    height: pxTransform(setting.value.okBtn?.height || 0),
  };
});

const uploadLogInvite = () => {
  // 事件上报：点击复制分享链接
  uploadLog({
    event_name: 'click',
    click_id: 15,
    click_type: 3,
  });
};

/**
 * 复制链接
 */
const handleCopy = () => {
  uploadLogInvite();

  // 登录态拦截
  const pass = activityStore._checkIn(props.bindRole);
  if (!pass) {
    return;
  }

  // 随机选择一条邀请文案
  const textList = setting.value.copyTemplates?.map(item => item.text) || []
  const randomIndex = Math.floor(Math.random() * textList.length)
  const randomText = textList[randomIndex] ? `${textList[randomIndex]}{{link}}` : ''
  const mergeText = randomText
  const url = mergeText
    ? interpolateString(mergeText, { link: props.shortLink })
    : props.shortLink;
  copyText(url, { success: '链接已复制，快去分享给好友吧~', error: '复制失败' });
};

const handleClose = () => {
  emits('close');
};

const handleCreatePoster = () => {
  // 事件上报：点击生成海报
  uploadLog({
    event_name: 'click',
    click_id: 11,
    click_type: 3,
  });
  emits('createPoster');
};
</script>

<style>
.invite-modal-link-content {
  font-weight: 600;
  line-height: 1.3;
}

.invite-modal-link-link .ui-text > div {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
  text-align: center
}

.invite-modal-link-wx-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: transparent;
}

.invite-modal-link-wx-btn::after {
  border: none;
}
</style>