import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'

const { VITE_PAY_URL, VITE_ACTIVITY_URL } = useEnv()

export type SubmitPreOrderParams = {
  /**
   * 商品名称，如：60钻石
   */
  subject: string
  /**
   * 研发游戏商品ID（cp_goods_id），如：45478
   */
  subject_id: number
  /**
   * 项目（游戏）ID，如：11
   */
  project_id: number
  /**
   * 充值金额（元），必须大于0.01，如：6
   */
  amount: number
  /**
   * 平台 区服，如：symlf_3248
   */
  srv_id: string
  /**
   * 区服名，如：冰刃幻境
   */
  srv_name: string
  /**
   * 角色ID，如：4483
   */
  role_id: number
  /**
   * 角色name，如：坚硬的小钢针
   */
  role_name: string
  /**
   * 角色等级，如：45
   */
  role_lev: number
  /**
   * 角色vip等级，如：12
   */
  role_vip_lev: number
  /**
   * 设备类型,注意:严格区分普通H5和PC两种环境,,会返回不一样的支付信息,H5=>’H5’,PC=>’PC’
   */
  device_type: string
  /**
   * 支付类型,微信支付=>’wechat’,支付宝=>’alipay’
   */
  pay_type: string
  /**
   * 是否为微信浏览器打开，0否、1是
   */
  is_wechat_web: number
  /**
   * 为微信浏览器打开，需要获取
   */
  openid: string
}

export type WeChartPayInfo = {
  appId: string
  nonceStr: string
  package: string
  signType: string
  timeStamp: number
  paySign: string
}

export type SubmitPreOrderData = {
  /**
   * 根据不同的支付场景和支付方式,会返回不一样的数据,注意使用场景
   */
  pay_info: any
  /**
   * 订单号,同步查询的PC端场景可用这个订单号来轮询支付状态
   */
  order_id: string
  /**
   * 如何处理pay_info的内容：
   * url：pay_info返回的是url，需要跳转到该url 、 
   * image：pay_info返回的是图片url地址，显示到一个img标签 、
   * from：pay_info返回的是from表单，直接加载运行 、 
   * iframe：pay_info返回的是iframe，加载到一个iframe里会自动显示一个支付二维码 
   * jsapi：pay_info返回的是调起微信jsapi支付的参数
   */
  pay_info_type: string | WeChartPayInfo
}

/**
 * 创建订单接口(h5端预充值)
 * @param params SubmitPreOrderParams
 * @returns 返回值参考文档：https://developer.shiyuegame.com/showdoc/web/#/16/5913
 */
export function submitPreOrder(params: SubmitPreOrderParams) {
  return http.post<SubmitPreOrderData>(`${VITE_PAY_URL}/preCharge/submitPreOrder`, params)
}

export type GetChargeSumParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 加密acc_id
   */
  act_acc_id: string
}

/**
 * 活动账号累计充值金额
 * @param data GetChargeSumParams
 * @returns 
 */
export function getChargeSum(data: GetChargeSumParams) {
  return http.post(`${VITE_ACTIVITY_URL}/activity/charge/sum`, data)
}
