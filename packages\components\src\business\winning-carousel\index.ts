import type { WinningCarouselSetting } from './winning-carousel';

export * from './winning-carousel';

export const defaultConfig = (): WinningCarouselSetting => {
  return {
    width: 354,
    height: 25,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241218/1734505848246-bg.png',
    trumpet: {
      x: 52,
      y: 5,
      width: 17,
      height: 15,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241218/1734505850333-trumpet.png',
    },
    content: {
      x: 73,
      y: 0,
      width: 229,
      height: 25,
      fontSize: 10,
      color: '#B85454',
      alignItems: 'center',
    },
    contentTemp: '恭喜 {{phoneNumber}} 抽到 【{{prize}}】 ！来二〇二五一起爽抽大奖！',
  }
};