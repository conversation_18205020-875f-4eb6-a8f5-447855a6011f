import { shallowRef } from 'vue'
import { useEnv } from '@bish/hooks/src/useEnv'
import useActivityPageStore from '@bish/store/src/modules/activityPage'

export function useShop() {
  const { VITE_SHOP_URL } = useEnv()
  const activityPageStore = useActivityPageStore()
  const shopUrl = shallowRef('')
  const projectId = activityPageStore.activityPageConfig.project_id
  shopUrl.value = VITE_SHOP_URL.replace(/projectId/g, projectId)
  return {
    shopUrl,
  }
}
