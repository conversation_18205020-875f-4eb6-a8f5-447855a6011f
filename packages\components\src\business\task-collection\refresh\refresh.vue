<!--
* @Description: 任务状态刷新按钮
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="task-refresh"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
    @click="handleRefresh"
  >
    <img
      :src="icon_refresh_img"
      :class="['task-refresh-icon', spin ? 'spin' : undefined]"
    />
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'task-refresh',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, ref } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import { defaultConfig } from './index';
import type { TaskRefreshSetting } from './refresh';
import Resizable from '../../../ui/resizable/index.vue';
import icon_refresh_img from './icon-refresh.png';

export interface TaskCollectionProps {
  /**
   * 配置
   */
  setting: TaskRefreshSetting
}

const props = withDefaults(defineProps<TaskCollectionProps>(), {
  setting: () => defaultConfig(),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskRefreshSetting): void;
}>();

const spin = ref(false);

const activityStore = useActivityStore();

const setting = useVModel(props, 'setting', emits);

const handleRefresh = async () => {
  const pass = activityStore._checkIn(false);
  if (!pass) {
    return;
  }
  try {
    spin.value = true;
    await activityStore.initActivityInfo(false);
    showToast('任务进度已刷新~');
  } catch (error) {
  } finally {
    spin.value = false;
  }
};
</script>

<style>
.task-refresh {
  display: flex;
  align-items: center;
  justify-content: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.task-refresh-icon {
  width: 57%;
  height: 57%;
}

.task-refresh-icon.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
