import { ref, readonly } from 'vue';
import { postMiniRemindStatus, postMiniRemindAuthSave } from '@bish/api/src/miniProgram';
import type { MiniRemindAuthSaveParams } from '@bish/api/src/miniProgram';
import { userAgent } from '@bish/utils/src/utils';
import useControllableStatus from './useControllableStatus'

type TemplateList = {
  /**
   * 后台接口用
   * 4是预约通知 5是活动开始通知 6是公测上线通知
   */
  remindTemplateId: string
  /**
   * 微信订阅模板id
   */
  templateId: string
}[]

function subscribeStatus(templateList: TemplateList, result: Record<string, any>) {
  const acceptItems: string[] = []; // 同意订阅消息项
  const rejectItems: string[] = []; // 拒绝订阅消息项
  const banItems: string[] = []; // 后台封禁消息项

  // 数据分组
  for (let i = 0; i < templateList.length; i++) {
    const item = templateList[i].templateId;
    if (result[item] === 'accept') {
      acceptItems.push(item);
    }
    if (result[item] === 'reject') {
      rejectItems.push(item);
    }
    if (result[item] === 'ban') {
      banItems.push(item);
    }
  }
  return {
    acceptItems,
    rejectItems,
    banItems,
    someAccept: acceptItems.length > 0 && acceptItems.length !== templateList.length, // 是否部分同意订阅消息
    allAccept: acceptItems.length === templateList.length, // 是否全部同意订阅消息
    allReject: rejectItems.length === templateList.length, // 是否全部拒绝订阅消息
    allBan: banItems.length === templateList.length, // 是否全部订阅消息被封禁
  };
}

/**
 * 检查是否已经授权过订阅消息
 * @param templateList 订阅消息模板ids
 * @param itemSettings 设置
 * @returns 
 */
function checkSubscriptionAuthorized(templateList: TemplateList, itemSettings: Record<string, any>) {
  let authorized = false; // 已授权

  // 数据分组
  for (let i = 0; i < templateList.length; i++) {
    const item = templateList[i].templateId;
    if (item in itemSettings) {
      authorized = true;
    }
  }
  return authorized;
}

/**
 * 微信消息订阅 hook
 * @param templateList 订阅消息模板ids，最多支持同时订阅 3 条消息
 * @param submitClock 订阅消息成功后回调
 * @returns 
 */
export default function useWxSubscribeMessage(
  submitClock: () => void,
  openSubscribeStatus: Parameters<typeof useControllableStatus>,
) {
  const showGuideSubscription = ref(false); // 是否显示 勾选总是保持选择，及时接收息 弹窗
  
  const subscribed = ref(false); // 是否订阅过微信消息，默认未订阅
  const remindLoading = ref(false);

  // 是否显示引导 打开订阅消息 设置弹窗
  const [showOpenSubscribeMessage, toggleOpenSubscribeMessage] = useControllableStatus(...openSubscribeStatus);

  const { isUserAgentType } = userAgent();

  let status: ReturnType<typeof subscribeStatus> | undefined = undefined

  const getSetting = (): Promise<any> => {
    return new Promise((resolve, reject) => {
      uni.getSetting({
        withSubscriptions: true,
        success(res: any) {
          resolve(res);
        },
        fail(error) {
          reject(error);
        },
      });
    });
  };

  const guideOpenSubscribeMessage = () => {
    toggleOpenSubscribeMessage(true);
  };

  const guidSubscribeMessageAuthAfter = async (templateList: TemplateList) => {
    // 引导用户 开启订阅消息 之后，「openSetting」 接口暂时不会返回，用户手动设置后的状态，所以采用「getSetting」接口重新进行查询
    try {
      const setting = await getSetting();
      const {
        authSetting = {},
        subscriptionsSetting: { mainSwitch = false, itemSettings = {} } = {},
      } = setting;
      const { allAccept, someAccept, allReject } = subscribeStatus(templateList, itemSettings);

      if (
        (authSetting['scope.subscribeMessage'] || mainSwitch) &&
        (allAccept || someAccept)
      ) {
        // submitClock();
        // console.log('用户手动开启同意了，订阅消息');
      }
      if (allReject) {
        uni.showToast({
          title: `您没有同意授权订阅消息`,
          icon: 'none',
          duration: 3000,
        });
      }
      // 如果用户打开主开关，但是没有订阅设置某条消息
      if (mainSwitch && !checkSubscriptionAuthorized(templateList, itemSettings)) {
        // 这里不能直接通过拉起订阅，等用户通过点击行为才行
        // TODO：弹出一个确定订阅的引导弹窗，让用户点击
        // setClock();
      }

    } catch (error) {
      console.warn('获取设置失败', error);
    }
  };

  const setClock = (templateList: TemplateList) => {
    if (!uni.requestSubscribeMessage) {
      uni.showToast({
        title: '请更新您微信版本，来获取订阅消息功能',
        icon: 'none',
        duration: 3000,
      });
      return;
    }

    // 没有订阅次数，才显示 勾选总是保持选择 弹窗
    // 并且开启主开关（接收消息）
    if (!subscribed.value) {
      getSetting().then((setting) => {
        const {
          subscriptionsSetting: { itemSettings = {}, mainSwitch = false } = {},
        } = setting;
        if (!checkSubscriptionAuthorized(templateList, itemSettings) && mainSwitch) {
          showGuideSubscription.value = true;
        }
      });
    }
    const tmplIds: string[] = templateList.map((item) => item.templateId);
    uni.requestSubscribeMessage({
      tmplIds,
      async success(res: any) {
        status = subscribeStatus(templateList, res);
        const { allAccept, someAccept, allReject, allBan } = status;
        if (allAccept || someAccept) {
          submitClock?.();
        }

        if (allReject) {
          const setting = await getSetting();
          const {
            subscriptionsSetting: { itemSettings = {} } = {},
          } = setting;
          // 用户点击拒绝，勾选总是保持以上选择，这个时候设置页面是有订阅消息的配置选项的，这个时候须引导
          // 用户点击拒绝，但没有勾选总是保持以上选择，这个时候设置页面是没有订阅消息的配置选项的，这个时候无须引导
          if (checkSubscriptionAuthorized(templateList, itemSettings)) {
            // 用户历史操作有设置了拒绝 or 关闭了订阅消息的主（总）开关，导致无法推送
            guideOpenSubscribeMessage();
          }
        }
        if (allBan) {
          uni.showToast({
            title: '授权订阅消息有误',
            icon: 'none',
            duration: 3000,
          });
        }
      },
      fail(res) {
        // 20004:用户关闭了主开关，无法进行订阅,引导开启
        if (res.errCode == 20004) {
          // console.log(res, 'fail:用户关闭了主开关，无法进行订阅,引导开启---');
          guideOpenSubscribeMessage();
        }
      },
      complete() {
        showGuideSubscription.value = false;
      },
    });
  };

  /**
   * 获取后台微信消息订阅状态
   * @param param MiniRemindAuthSaveParams
   * @returns 
   */
  const fetchRemindStatus = async (param: MiniRemindAuthSaveParams) => {
    // 微信小程序、微信小程序内嵌
    if (
      isUserAgentType !== 'WX_WEBVIEW' &&
      isUserAgentType !== 'WX_MINI'
    ) {
      return;
    }
    if (!param.act_id ||
      !param.act_acc_id ||
      !param.remind_template_id
    ) {
      return;
    }
    try {
      remindLoading.value = true;
      const res = await postMiniRemindStatus(param);
      if (res.code === 0) {
        if (res.data.list) {
          // 这里判断是否全部模板都有订阅次数，否则还是允许发起订阅
          subscribed.value = res.data.list.every(item => item.auth_number > 0);
        }
        remindLoading.value = false; // 这里故意不放在 finally 处理
      }
    } catch (error) {
      console.warn('获取订阅消息提醒状态失败', error);
    }
  };

  /**
   * 更新后台微信订阅消息提醒
   * @param param MiniRemindAuthSaveParams
   */
  const updateSubscriptionCount = async (
    param: Omit<MiniRemindAuthSaveParams, 'remind_template_id'>,
    templateList: TemplateList,
  ) => {
    try {
      const mergedParam: MiniRemindAuthSaveParams = {
        ...param,
        remind_template_id: '',
      };
      if (!status) {
        return;
      }
      const { acceptItems } = status;
      // 只更新勾选订阅的模板
      const remindTemplateList = acceptItems
        .map(item => templateList.find(template => template.templateId === item))
        .filter(Boolean);
      // 微信开发者工具拿不到对应数据
      if (!remindTemplateList.length) {
        return;
      }
      mergedParam.remind_template_id = remindTemplateList.map(item => item!.remindTemplateId).join(',');
      const res = await postMiniRemindAuthSave(mergedParam);
      if (res.code === 0) {
        uni.showToast({
          title: '订阅成功~',
          icon: 'none',
          duration: 3000,
        });
      }
      // 重新获取订阅状态
      fetchRemindStatus(mergedParam);
    } catch (error) {
      console.warn('更新订阅消息授权次数失败', error);
    }
  };

  /**
   * 重置微信消息订阅相关状态
   */
  const resetSubscribeState = () => {
    subscribed.value = false;
    remindLoading.value = false;
  };

  return {
    showGuideSubscription,
    showOpenSubscribeMessage,
    setClock,
    subscribed: readonly(subscribed),
    remindLoading,
    guidSubscribeMessageAuthAfter,
    fetchRemindStatus,
    updateSubscriptionCount,
    resetSubscribeState,
    toggleOpenSubscribeMessage,
  };
}
