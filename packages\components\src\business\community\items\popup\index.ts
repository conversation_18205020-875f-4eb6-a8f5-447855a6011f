import type { CommunityPopupSetting } from './popup';
import { COMMUNITY_COMMON_NOOP } from '../const';

export * from './popup';

export const defaultConfig = (): CommunityPopupSetting => {
  return {
    x: 0,
    y: 0,
    width: 94,
    height: 94,
    imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241108/1731047365224-BUTTON%E5%85%AC%E4%BC%97%E5%8F%B7.png',
    popup: {
      x: 0,
      y: 0,
      width: 375,
      height: 288,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241108/1731050007132-gzh.png',
      closeBtn: {
        x: 327,
        y: 3,
        width: 27,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730944693773-%E5%85%B3%E9%97%AD%E6%8C%89%E9%92%AE%20%E6%8B%B7%E8%B4%9D%203.png',
      },
    },
    type: COMMUNITY_COMMON_NOOP,
    affix: 0,
  };
};
