<!--
* @Description: 微信小程序内嵌：消息订阅中间页
-->
<template>
  <Popup
    z-index="99"
    :show="show"
    :resizable="false"
    v-model:setting="setting.modalSetting"
    @close="handleClose"
  />
</template>

<script lang="ts">
export interface WeSubscribeModalProps {
  /**
   * 配置
   */
  setting: WeSubscribeModalSetting
  /**
   * 是否显示
   */
  show: boolean
}

export default {
  name: 'we-subscribe-modal',
}
</script>

<script lang="ts" setup>
import { withDefaults } from 'vue';
import { useVModel } from '@vueuse/core';
import Popup from '../../common/popup.vue';
import type { WeSubscribeModalSetting } from './we-subscribe-modal';
import { defaultConfig } from './index';

const props = withDefaults(defineProps<WeSubscribeModalProps>(), {
  setting: defaultConfig,
  show: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: WeSubscribeModalSetting): void;
  (event: 'close'): void;
}>();

const setting = useVModel(props, 'setting', emits);

const handleClose = () => {
  emits('close');
};
</script>

<style>
.we-subscribe-modal-content {}
</style>