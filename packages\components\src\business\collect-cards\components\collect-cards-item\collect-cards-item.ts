import type { CommonSetting } from '@bish/types/src';
import type { ImageSetting } from '../../../../ui/image/index.vue';

export interface CollectCardsItemSetting extends CommonSetting {
  /**
   * 背景图片链接
   */
  bgImage?: string
  /**
   * 未激活背景图
   */
  inactiveBgImage?: string
  /**
   * 卡片
   */
  card: CommonSetting
  /**
   * 好友赠送标志
   */
  gift?: ImageSetting
  /**
   * 已刮奖标志
   */
  scratched?: ImageSetting
  /**
   * 刮奖按钮
   */
  scratch?: ImageSetting
} 
