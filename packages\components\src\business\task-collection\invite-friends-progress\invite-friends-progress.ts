import type { CommonImageSetting, CommonSetting, CommonBackgroundSetting } from '@bish/types/src';
import type { TextSetting } from '../../../ui/text/index.vue';
import type { MilestoneSetting } from '../../../common/milestone/index.vue';

export type InviteFriendsProgressSetting =  MilestoneSetting & {
  /**
   * 任务id
   */
  taskId: string
  /**
   * 标记配置
   */
  mark: {
    /**
     * 道具配置
     */
    item: CommonSetting
    /**
     * 文本容器配置
     */
    content: CommonSetting
    /**
     * 节点值配置
     */
    point: TextSetting
    /**
     * 节点名称配置
     */
    name: TextSetting
    /**
     * 已达成
     */
    achieved: CommonSetting

  }
  /**
   * 好友头像背景及默认头像
   */
  avatar: CommonBackgroundSetting & {
    defaultAvatar: string
  }
  /**
   * 列表项
   */
  marks: {
    /**
     * 节点名称
     */
    name?: string
    /**
     * 节点值
     */
    point: number
    /**
     * 节点完成icon
     */
    completed?: string,
    /**
     * 节点未完成icon
     */
    incomplete: string,
  }[]
  /**
   * 热度倍数，默认 1，用于夸张热度
   */
  multiple: number
  /**
   * 是否显示节点名称，默认 true
   */
  showMarkName: boolean
  /**
   * 是否显示节点值，默认 true
   */
  showMarkPoint: boolean
  /**
   * 是否显示道具，默认 true
   */
  showMarkItem?: boolean
}