<!--
* @Description: 基础组队-解散队伍
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    @click="handleOpen"
  >
    <!-- 入口 -->
    <div
      v-if="showEntry"
      class="team-disband"
      :class="{ 'team-disband-affix': setting.affix && !adminStore.editable }"
      :style="entryStyle"
    />

    <!-- 弹窗 -->
    <Popup
      z-index="99"
      :show="open"
      v-model:setting="setting.modal"
      @close="handleClose"
      @ok="handleConfirm"
    >
      <!-- 文本内容 -->
      <UiText
        v-if="setting.modal.content.enabled"
        v-model:setting="setting.modal.content"
        :interpolation="interpolation"
        class="team-disband-content"
      />
    </Popup>
    
    <!-- 确认弹窗 -->
    <Popup
      z-index="99"
      :show="openConfirm"
      v-model:setting="setting.confirmModal"
      @close="handleCloseConfirm"
      @ok="handleDisband"
    >
      <!-- 文本内容 -->
      <UiText
        v-if="setting.confirmModal.content.enabled"
        v-model:setting="setting.confirmModal.content"
        :interpolation="interpolation"
        class="team-disband-content"
      />

      <!-- 取消按钮 -->
      <UiImage
        v-if="setting.confirmModal.cancelBtn.enabled"
        v-model:setting="setting.confirmModal.cancelBtn"
        @click.stop="handleCloseConfirm"
      />
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'team-disband',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, nextTick } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { useLog } from '@bish/hooks/src/useLog';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { pxTransform } from '@bish/utils/src/viewport';
import { postTeamDisband } from '@bish/api/src/team-gift';
import Resizable from '../../../ui/resizable/index.vue';
import UiImage from '../../../ui/image/index.vue';
import UiText from '../../../ui/text/index.vue';
import Popup from '../../../common/popup.vue';
import type { TeamDisbandSetting } from './team-disband';
import { defaultConfig } from './index';

export interface TeamDisbandProps {
  /**
   * 配置
   */
  setting: TeamDisbandSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TeamDisbandProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TeamDisbandSetting): void;
}>();

const setting = useVModel(props, 'setting', emits);

const [open, toggle] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showTeamDisbandModal' });
const [openConfirm, toggleConfirm] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showTeamDisbandConfirmModal' });

const userStore = useUserStore();
const activityStore = useActivityStore();
const adminStore = useAdminStore();

const { uploadLog } = useLog();

const entryStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.x!),
    top: pxTransform(setting.value.y!),
    width: pxTransform(setting.value.width),
    height: pxTransform(setting.value.height),
    backgroundImage: `url(${setting.value.bgImage})`,
  };
});

// 队伍信息配置信息
const teamConfig = computed(() => {
  const { team } = activityStore.activityInfo.config;
  return team?.find(item => item.id === setting.value.teamId);
});

// 与配置对应的队伍信息
const currenTeam = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.team?.find(item => item.id === setting.value.teamId);
});

const interpolation = computed(() => {
  return {
    teamName: `${currenTeam.value?.team_name || '未知的队伍'}`,
  };
});


const members = computed(() => {
  return currenTeam.value?.members || [];
});

/**
 * 是否是队长
 */
const isCaptain = computed(() => {
  const current = members.value.find(item => +item.account_id === +userStore.userData.account_id);
  return current?.change_type === 1;
});

const showEntry = computed(() => {
  return isCaptain.value || adminStore.editable;
});

const handleOpen = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }

  // 事件上报:创建队伍
  // uploadLog({
  //   event_name: 'click',
  //   click_id: 13,
  //   click_type: 3,
  // });

  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }

  toggle(true);
};

const handleClose = () => {
  toggle(false);
};

const handleConfirm = async () => {
  toggle(false);
  await nextTick();
  toggleConfirm(true);
};

const handleCloseConfirm = () => {
  toggleConfirm(false);
};

const handleDisband = async () => {
  try {
    if (!teamConfig.value) {
      showToast('缺少队伍配置，请稍候再试~');
      return;
    }
    const { activityAccountInfo } = activityStore;
    const res = await postTeamDisband({
      act_id: activityAccountInfo.activity_id,
      team_config_id: setting.value.teamId,
      act_acc_id: activityAccountInfo.act_acc_id,
      team_id: currenTeam.value?.team_id!,
    });
    if (res.code === 0) {
      showToast('解散成功');
    }
    toggleConfirm(false);
    activityStore.getComponentWithUserInfo();
  } catch (error) {
    console.warn('解散队伍失败', error);
  }
};
</script>

<style>
.team-disband {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.team-disband-affix {
  position: fixed;
  left: auto !important;
  z-index: 98;
}

.team-disband-content {
  font-weight: 400;
}
</style>