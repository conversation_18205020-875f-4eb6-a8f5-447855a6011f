<!--
 * @Description: 通用-宫格抽奖
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="box-lottery"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 完成进度 -->
      <UiText
        v-if="setting.process"
        v-model:setting="setting.process"
        :interpolation="processInterpolation"
        :confined="false"
      />
      
      <BoxLottery
        v-model:setting="setting.boxLottery"
        :lotteryPrizes="lotteryConfig?.lottery_prize"
        :prize="drawData"
        :remaining="lotterySchedule?.lottery_remain_num"
        :beforeStart="fetchDraw"
        :box-grid="setting.boxLottery.boxGrid"
        @end="fetchDrawEnd"
        v-bind="attrs"
      />
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
import type { BoxLotterySetting } from './box-lottery';

export interface BoxLotteryProps {
  /**
   * 配置
   */
  setting: BoxLotterySetting
}

export default {
  options: {},
  name: 'lottery-box',
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, useAttrs, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { useBoxLottery } from '@bish/hooks/src/business/useBoxLottery';
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import UiText from '../../../ui/text/index.vue';
import BoxLottery from '../../box-lottery/index.vue';
import { defaultConfig } from './index';

const props = withDefaults(defineProps<BoxLotteryProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: BoxLotterySetting): void
}>();

const attrs = useAttrs();
const setting = useVModel(props, 'setting', emits);

const {
  fetchDraw,
  fetchDrawEnd,
  drawData,
  lotteryConfig,
  lotterySchedule,
  process,
} = useBoxLottery(setting.value.lotteryId, setting.value.needBindRole);

const processInterpolation = computed(() => {
  return {
    process: process.value,
  };
});

</script>

<style lang="scss" scoped>
.box-lottery {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
