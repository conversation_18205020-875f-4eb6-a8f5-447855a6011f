import { ref } from 'vue'
import { getShortLink as fetchShoreLink } from '@bish/api/src/team'
import useActivityStore from '@bish/store/src/modules/activity'
import useUserStore from '@bish/store/src/modules/user'

export default function useShortLink() {
  const shortLink = ref('')
  const activityStore = useActivityStore()
  const user = useUserStore()

  const getShortLink = async (originLink: string) => {
    try {
      const res = await fetchShoreLink({
        origin_link: originLink,
        link_name: `${activityStore.activityInfo.init?.activity_name}-${user.userData?.account_id}`,
        group_name: activityStore.activityInfo.init?.activity_name,
      })
      if (res.code === 0) {
        shortLink.value = res.data.short_link
      } else {
        shortLink.value = originLink
      }
    } catch (error) {
      console.warn('获取短链失败', error)
      shortLink.value = originLink
    }
  }

  return {
    shortLink,
    getShortLink,
  }
}
