<!--
* @Description: 社区链接
-->
<template>
  <UiImage
    class="community-link"
    v-model:setting="setting"
    @click="handleJump"
  />
</template>

<script lang="ts">
import type { CommunityLinkSetting } from './link';

export interface CommunityLinkProps {
  /**
   * 配置
   */
  setting: CommunityLinkSetting
}

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'community-link',
}
</script>

<script lang="ts" setup>
import { withDefaults } from 'vue';
import { useVModel } from '@vueuse/core';
import { useLog } from '@bish/hooks/src/useLog';
import UiImage from '../../../../ui/image/index.vue';
import { defaultConfig } from './index';

const props = withDefaults(defineProps<CommunityLinkProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: CommunityLinkSetting): void;
}>();

const setting = useVModel(props, 'setting', emits);

const { uploadLog } = useLog();

const handleJump = () => {
  // 数据上报
  uploadLog({
    event_name: 'click',
    click_id: setting.value.type,
    click_type: 3,
  });
};
</script>

<style lang="less">
.community-link {}
</style>
