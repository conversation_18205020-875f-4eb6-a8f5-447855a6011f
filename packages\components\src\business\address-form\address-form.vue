<!--
* @Description: 填写地址弹窗
-->
<template>
  <Popup
    z-index="101"
    :show="show"
    :resizable="false"
    v-model:setting="setting.modal"
    @close="handleClose"
    @ok="handleSubmit"
  >
    <!-- 表单 -->
    <Resizable
      v-model:x="setting.modal.form.position.x"
      v-model:y="setting.modal.form.position.y"
      v-model:width="setting.modal.form.position.width"
      v-model:height="setting.modal.form.position.height"
      class="address-form"
    >
      <div class="address-form-item">
        <div class="address-form-item-label" :style="labelStyle">
          收件人:
        </div>
        <div class="address-form-item-control">
          <div class="address-form-item-input-wrap" :style="inputWrapStyle">
            <input
              class="address-form-item-input"
              :style="inputStyle"
              :placeholder-style="cssPropertiesToString(inputStyle)"
              v-model="formValue.name"
              placeholder="请输入收件人名称"
              autocomplete="off"
            />
          </div>
        </div>
      </div>
      <div class="address-form-item">
        <div class="address-form-item-label" :style="labelStyle">
          收件电话:
        </div>
        <div class="address-form-item-control">
          <div class="address-form-item-input-wrap" :style="inputWrapStyle">
            <input
              class="address-form-item-input"
              :style="inputStyle"
              :placeholder-style="cssPropertiesToString(inputStyle)"
              v-model="formValue.phone_number"
              placeholder="请输入收件人电话"
              type="number"
            />
          </div>
        </div>
      </div>
      <div class="address-form-item">
        <div class="address-form-item-label" :style="labelStyle">
          收件地址:
        </div>
        <div class="address-form-item-control">
          <div class="address-form-item-input-wrap" :style="inputWrapStyle">
            <textarea
              class="address-form-item-input address-form-item-textarea"
              :style="[inputStyle, textareaStyle]"
              :placeholder-style="cssPropertiesToString(inputStyle)"
              v-model="formValue.address"
              placeholder="请输入收件地址（具体至省县市区街道门牌号）"
              maxlength="50"
              rows="4"
              autocomplete="off"
            />
          </div>
        </div>
      </div>
    </Resizable>
  </Popup>
</template>

<script lang="ts">
import type { StatefulComponent } from '@bish/types/src/admin';

export interface PeakClimbingProps {
  /**
   * 配置
   */
  setting: AddressFormSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export default {
  name: 'address-form',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, watch, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import usePopupStore from '@bish/store/src/modules/popup';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { pxTransform } from '@bish/utils/src/viewport';
import { showToast } from '@bish/ui/src/toast';
import { postActivitySaveAddress } from '@bish/api/src/activity';
import Popup from '../../common/popup.vue';
import Resizable from '../../ui/resizable/index.vue';
import type { AddressFormSetting } from './address-form';
import { defaultConfig } from './index';

const props = withDefaults(defineProps<PeakClimbingProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: AddressFormSetting): void;
  (event: 'close'): void;
}>();

const formValue = ref({
  name: '',
  phone_number: '',
  address: '',
});

const activityStore = useActivityStore();
const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);

const [show, toggleShow] = useControllableStatus(props, emits, { fieldName: 'showAddressFormModal' });

const labelStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(70),
    paddingRight: pxTransform(10),
    color: setting.value.modal.form.label.color,
    fontSize: pxTransform(12),
  };
});

const inputStyle = computed<CSSProperties>(() => {
  return {
    fontSize: pxTransform(10),
    color: '#FFFFFF',
  };
});

const textareaStyle = computed<CSSProperties>(() => {
  return {
    height: pxTransform(72),
  };
});

const inputWrapStyle = computed<CSSProperties>(() => {
  return {
    padding: `${pxTransform(8)} ${pxTransform(11)}`,
  };
});

watch(
  () => popupStore.showAddressForm,
  (newVal) => {
    toggleShow(newVal);
  },
);

const initAddressInfo = () => {
  const addressInfo = activityStore.activityAccountInfo.extra?.address_info;
  if (addressInfo) {
    formValue.value.name = addressInfo.name;
    formValue.value.phone_number = addressInfo.phone_number;
    formValue.value.address = addressInfo.address;
  }
};

watch(
  () => popupStore.showAddressForm,
  (newVal) => {
    if (newVal) {
      initAddressInfo();
    }
  },
);

const cssPropertiesToString = (obj: CSSProperties) => {
  return Object.keys(obj)
    .map((key) => `${key}:${obj[key as keyof CSSProperties]}`)
    .join(';');
};

const handleSubmit = async () => {
  try {
    let title = '';
    const str_reg = /^[\u4e00-\u9fa5a-zA-Z0-9▪·-]+$/;
    const phone_reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
    let { name, phone_number, address } = formValue.value;
    // 校验详细地址
    if (!str_reg.test(address)) {
      title = '收货地址请勿填写特殊符号';
    }
    if (address.length > 50) {
      title = '收货地址最多50个字';
    }
    if (address.length < 5) {
      title = '请输入收货地址，不少于5个字';
    }
    // 校验手机号
    if (!phone_reg.test(phone_number)) {
      title = '请填写有效的收件人手机号';
    }
    // 校验姓名
    if (!str_reg.test(name)) {
      title = '收件人名称请勿填写特殊符号';
    }
    if (name.length > 5) {
      title = '收件人最多5个字';
    }
    if (name.length < 2) {
      title = '请填写不小于2个字符的收件人名称';
    }
    if (title) {
      showToast(title);
      return;
    }
    const res = await postActivitySaveAddress({
      name,
      phone_number: phone_number,
      address,
      act_id: activityStore.activityInfo.init?.id,
      act_acc_id: activityStore.activityAccountInfo.act_acc_id,
    });
    if (res.code === 0) {
      showToast('提交成功，请留意后续消息！')
      activityStore.initActivityInfo(false);
      popupStore.setShowAddressForm(false);
    }
  } catch (error) {
    console.warn('保存地址失败', error);
  }
};


const handleClose = () => {
  // 同步数据
  if (show && !popupStore.showAddressForm) {
    toggleShow(false);
  }
  popupStore.setShowAddressForm(false);
};
</script>

<style>
.address-form {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.address-form-item {
  display: flex;
  align-items: flex-start;
}

.address-form-item-label {
  text-align: right;
  font-weight: 600;
  color: #7d4f1e;
}

.address-form-item-control {
  flex: 1;
}

.address-form-item-input-wrap {
  display: flex;
  background-color: #7E7764;
  border-radius: 2px;
}

.address-form-item-input {
  background-color: transparent;
  border: none;
}

.address-form-item-input::placeholder {
  color: #FFFFFF;
}

.address-form-item-textarea {
  flex: 1;
  width: 100%;
  resize: none;
}

/* 隐藏 number 输入框的上下箭头 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>
