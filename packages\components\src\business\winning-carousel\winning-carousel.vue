<!--
* @Description: 中奖轮播
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="winning-carousel"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
    v-if="providedData.length"
  >
    <ResizableProvider>
      <!-- 喇叭 -->
      <UiImage v-if="setting.trumpet.imgLink" v-model:setting="setting.trumpet" />

      <!-- 内容 -->
      <UiText v-model:setting="setting.content" :scrollspy="false">
        <div class="winning-carousel-content" :style="contentStyle">
          <div class="winning-carousel-scroll" :style="scrollStyle">
            <span v-for="item in providedData" :key="item.created_at">
              <span class="winning-carousel-content-circle" />
              {{ content(item) }}
            </span>
          </div>
        </div>
      </UiText>
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'winning-carousel',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, ref, computed, onMounted, watchEffect } from 'vue';
import type { CSSProperties } from 'vue';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useAdminStore from '@bish/store/src/modules/admin';
import type { StatefulComponent } from '@bish/types/src/admin';
import { pxTransform } from '@bish/utils/src/viewport';
import { postLotterySlide } from '@bish/api/src/lottery';
import type { LotterySlideData } from '@bish/api/src/lottery';
import { defaultConfig } from './index';
import type { WinningCarouselSetting } from './winning-carousel';
import { interpolateString } from '../../__utils/text';
import Resizable from '../../ui/resizable/index.vue';
import ResizableProvider from '../../ui/resizable/resizable-provider.vue';
import UiImage from '../../ui/image/index.vue';
import UiText from '../../ui/text/index.vue';

export interface WinningCarouselProps {
  /**
   * 配置
   */
  setting: WinningCarouselSetting
}

const DURATION = 5;

const props = withDefaults(defineProps<WinningCarouselProps>(), {
  setting: () => defaultConfig(),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: WinningCarouselSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const slideData = ref<LotterySlideData>([]);

const activityPageStore = useActivityPageStore();
const adminStore = useAdminStore();

const contentStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(props.setting.content.width),
    height: pxTransform(props.setting.content.height),
    lineHeight: pxTransform(props.setting.content.height),
  }
});

const providedData = computed<LotterySlideData>(() => {
  if (adminStore.editable) {
    return [
      {
        created_at: '2024-12-18 16:43:00',
        item_name: '特斯拉Modal3',
        account: {
          wx_openid: '',
          phone_number: '177****4832',
          portrait: '',
          client_extra: '',
          account_id: '********',
        },
      },
      {
        created_at: '2024-12-18 16:44:00',
        item_name: 'PS5',
        account: {
          wx_openid: '',
          phone_number: '191****4832',
          portrait: '',
          client_extra: '',
          account_id: '********',
        },
      },
    ]
  }
  return slideData.value;
});

const scrollStyle = computed<CSSProperties>(() => {
  return {
    animationDuration: `${providedData.value.length * DURATION}s`,
  }
});

const fetchLotterySlideData = async () => {
  try {
    if (!activityPageStore.activityPageConfig.activity_id) {
      return;
    }
    const res = await postLotterySlide({
      act_id: activityPageStore.activityPageConfig.activity_id,
      page_size: 20,
    });
    if (res.code === 0) {
      slideData.value = res.data;
    }
  } catch (error) {
    console.warn('获取中奖信息轮播失败', error);
  }
};

watchEffect(() => {
  fetchLotterySlideData();
});

const content = (item: LotterySlideData[0]) => {
  return interpolateString(props.setting.contentTemp, {
    phoneNumber: item.account.phone_number || item.account_id || '',
    prize: item.item_name,
    accountId: item.account_id,
  });
};
</script>

<style>
.winning-carousel {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.winning-carousel-content {
  position: relative;
  overflow: hidden;
}

.winning-carousel-scroll {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  white-space: nowrap;
  animation-name: winningCarouselScroll;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.winning-carousel-content-circle {
  display: inline-block;
  width: 40px;
}

@keyframes winningCarouselScroll {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(-100%, 0, 0);
  }
}
</style>
