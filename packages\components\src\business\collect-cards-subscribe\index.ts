import type { CollectCardsSubscribeSetting } from './collect-cards-subscribe';
export const defaultConfig = (): CollectCardsSubscribeSetting => {
  return { 
    x: 0,
    y: 0,
    width: 375,
    height: 199,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744377768633-submint-btn.png',
    optionBtn: {
      subscribe: {
        x: 124,
        y: 122,
        width: 143,
        height: 31,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744378219485-subminted.png',
      },
      unsubscribe: {
        x: 124,
        y: 122,
        width: 143,
        height: 31,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744378207146-submit.png',
      },
      award: {
        x: 124,
        y: 122,
        width: 143,
        height: 31,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744378260012-log.png',
      },

    },
    award: {
      x: 138,
      y: 32,
      width: 97,
      height: 97,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744378622913-icon-bg.png',
    },
    weappEmbedSubscribeSetting:{
      modalSetting: {
        x: 0,
        y: 0,
        width: 375,
        height: 667,
        bgImage: "https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241115/1731639109319-%E7%BB%84%20204.png",
        okBtn: {
          x: 84,
          y: 534,
          width: 206,
          height: 63,
          imgLink: "https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241115/1731639118640-%E5%89%8D%E5%BE%80%E8%AE%A2%E9%98%85.png"
        }
      },
      templateList: [],
    },
    wxSubscribeModal: {
      x: 0,
      y: 0,
      width: 375,
      height: 451,
      bgImage: "https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241114/1731556851565-subscribe-tip-bg.png",
      closeBtn: {
        x: 313,
        y: 29,
        width: 30,
        height: 26,
        imgLink: "https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241114/1731556883921-close-btn.png"
      },
      okBtn: {
        x: 102,
        y: 285,
        width: 171,
        height: 52,
        imgLink: "https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241114/1731556848511-subscribe-btn.png"
      }
    },
    modalSetting: {
      x: 0,
      y: 0,
      width: 348,
      height: 354,
      bgImage: "https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713155411037_.png",
      content: {
        x: 70,
        y: 250,
        width: 208,
        height: 100,
        fontSize: 14,
        content: "提示",
        color: "#5A8B82",
        align: "center",
        fontWeight: true
      },
      table: {
        x: 70,
        y: 200,
        width: 208,
        height: 100,
        fontSize: 14,
        content: "",
        color: "#5A8B82",
        align: "center",
        fontWeight: true
      },
      okBtn: {
        x: 116,
        y: 251,
        width: 116,
        height: 33,
        imgLink: "https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713155434502_.png",
      },
      closeBtn: {
        x: 302,
        y: 15,
        width: 21,
        height: 20,
        imgLink: "https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713155242155_.png"
      }
    }
  }
}; 
