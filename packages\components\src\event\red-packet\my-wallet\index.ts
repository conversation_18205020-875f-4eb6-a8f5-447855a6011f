import type { MyWalletSetting, AmountSetting, AllSetting } from './my-wallet';

export * from './my-wallet';

/**
 * 金额提现模式-列表
 */
export const ACTION_MODE_LIST = 1;
/**
 * 金额提现模式-全部提现
 */
export const ACTION_MODE_ALL = 2;

export const defaultAmountConfig = (): AmountSetting => {
  return {
    list: {
      x: 47,
      y: 161,
      width: 281,
      height: 84,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241121/1732161233040-close.png',
    },
    item: {
      width: 68,
      height: 84,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241201/1733063456835-item-bg.png',
    },
    itemNum: {
      x: 0,
      y: 20,
      width: 68,
      height: 30,
      fontSize: 14,
      color: '#F4E1B9',
      align: 'center',
    },
    items: [
      {
        num: 1,
      },
      {
        num: 3,
      },
      {
        num: 5,
      },
      {
        num: 10,
      },
    ],
  }
};

export const defaultAllConfig = (): AllSetting => {
  return {
    num: {
      x: 35,
      y: 206,
      width: 169,
      height: 20,
      fontSize: 15,
      color: '#363636',
      align: 'right',
      alignItems: 'center',
      content: '立即提现：{{amount}} 元',
      fontWeight: true,
    },
    actionBtn: {
      x: 217,
      y: 206,
      width: 52,
      height: 20,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250114/1736844835377-all-btn.png',
    },
    max: 200,
  }
};

export const defaultConfig = (): MyWalletSetting => {
  return {
    x: 0,
    y: 0,
    width: 165,
    height: 69,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241201/1733061747972-action-btn.png',
    affix: 1,
    modal: {
      x: 0,
      y: 0,
      width: 375,
      height: 412,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241201/1733049998153-m-bg.png',
      closeBtn: {
        x: 303,
        y: 17,
        width: 25,
        height: 25,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241201/1733061727291-m-close.png',
      },
      okBtn: {
        x: 111,
        y: 277,
        width: 154,
        height: 46,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241201/1733049995229-withdraw-btn.png',
      },
      balance: {
        x: 57,
        y: 111,
        width: 262,
        height: 31,
        bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241201/1733061628677-amount-bg.png',
        num: {
          x: 115,
          y: 0,
          width: 88,
          height: 30,
          fontSize: 14,
          color: '#CD3B3B',
          alignItems: 'center',
          content: '{{num}}元'
        },
      },
      actionMode: 1,
      amount: defaultAmountConfig(),
      records: {
        x: 164,
        y: 332,
        width: 47,
        height: 14,
        bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241201/1733062170599-records-e.png',
      },
      refreshBtn: {
        x: 140,
        y: 367,
        width: 95,
        height: 25,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241201/1733049983058-refresh.png',
      },
    },
    alwaysShow: 1,
  };
};