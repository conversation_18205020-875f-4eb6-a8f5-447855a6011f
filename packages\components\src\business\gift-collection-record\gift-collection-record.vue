<!--
* @Description: 赠送/收取记录
-->
<template>
  <Resizable 
    class="git-collection-record" 
    v-model:x="setting.x" 
    v-model:y="setting.y" 
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
      backgroundSize: '100% 100%',
    }" 
    @click="showModal"
  >
    <Popup 
      z-index="99" 
      :show="show" 
      :lock-scroll="false" 
      :resizable="false"
      v-model:setting="setting.modal.modalSetting" 
      @close="handleClose" 
      @ok="handleClose">
      <template v-for="key in setting.modal.logType" :key="key">
        <Resizable v-model:x="setting.modal[key]!.position.x" v-model:y="setting.modal[key]!.position.y"
          v-model:width="setting.modal[key]!.position.width" v-model:height="setting.modal[key]!.position.height">
          <img v-if="currentLogType === key ? setting.modal[key]?.activeBg : setting.modal[key]?.bg" class="log-tab-img"
            :class="{ 'log-tab-img-active': currentLogType === key }"
            :src="currentLogType === key ? setting.modal[key]?.activeBg : setting.modal[key]?.bg"
            @click="currentLogType = key" />
        </Resizable>
      </template>
      <Resizable v-model:x="setting.content.x" v-model:y="setting.content.y" v-model:width="setting.content.width"
        v-model:height="setting.content.height" class="content-box">
        <div :style="listItemStyle" v-for="item in dataList" :key="item.id">
          <div class="item-content">
            <div v-if="currentLogType == 'gift'" class="item-text">您向【{{ item.assist_account_phone }}】 赠送 【{{ item.item.name }}】 X1</div>
            <div v-if="currentLogType == 'receive'" class="item-text">您收到 【{{ item.assist_account_phone }}】 赠送的 【{{ item.item.name }}】 X1</div>
            <div :style="itemTimeStyle">{{ item.finish_at }}</div>
          </div>
        </div>
        <div :style="listEmpteStyle" v-if="dataList.length === 0" >暂无{{ currentLogType == 'gift' ? '赠送' : '收取' }}记录</div>
      </Resizable>
      <UiText v-model:setting="setting.tips" :scrollspy="false" />
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'gift-collection-record',
}
</script>
<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import type { CSSProperties } from 'vue';
import type { LogType } from './gift-collection-record';
import type { StatefulComponent } from '@bish/types/src/admin';
import type { GiftCollectionRecordSetting } from './gift-collection-record';
import Resizable from '../../ui/resizable/index.vue';
import Popup from '../../common/popup.vue';
import UiText from '../../ui/text/index.vue';
import { useToggle, useVModel } from '@vueuse/core';
import { defaultConfig } from './index';
import useAdminStore from '@bish/store/src/modules/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { pxTransform } from '@bish/utils/src/viewport';
import { postCollectionRecord } from '@bish/api/src/collect-cards';
import useActivityStore  from '@bish/store/src/modules/activity';
import { showToast } from '@bish/ui/src/toast';


export interface GiftCollectionRecordProps {
  setting: GiftCollectionRecordSetting,
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<GiftCollectionRecordProps>(), {
  setting: () => defaultConfig(),
});
const emits = defineEmits<{
  (event: 'update:setting', value: GiftCollectionRecordSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const setting = useVModel(props, 'setting', emits);

const [show, toggleShow] = useControllableStatus(props, emits, { fieldName: 'showCollectionRecordPopup' });
const adminStore = useAdminStore();
const activityStore = useActivityStore();

// 记录类型
const currentLogType = ref<LogType | undefined>(props.setting.modal.logType[0])

watch(currentLogType, () => {
  getCollectionRecord();
})

const listItemStyle = computed(() => {
  return {
    color: props.setting.content.text.color,
    fontSize: pxTransform(props.setting.content.text.fontSize || 12),
    borderBottom: `1px dashed ${props.setting.content.text.color}`,
    padding: `${pxTransform(10)} ${pxTransform(5)}`,
  }
})
const itemTimeStyle = computed(() => {
  return {
    marginTop: pxTransform(5),
    fontSize: pxTransform((props.setting.content.text.fontSize || 12) - 2),
  }
})
const listEmpteStyle = computed<CSSProperties>(() => {
  return {
    textAlign: 'center',
    color: props.setting.content.text.color,
    fontSize: pxTransform((props.setting.content.text.fontSize || 12)),
    marginTop: pxTransform(40),
  }
})

// 记录项的类型
interface CollectionRecord {
  id: number;
  item_id: number;
  assist_account_id: string;
  finish_at: string;
  item: {
    id: number;
    name: string;
    is_limit_card: number;
  };
  assist_account_phone: string;
}
const dataList = ref<CollectionRecord[]>([]);

const showModal = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }
  const pass = activityStore._checkIn(false);
  if (!pass) {
    return;
  }
  toggleShow(true);
  getCollectionRecord();
};

const handleClose = () => {
  toggleShow(false);
};

const getCollectionRecord = () => {
  const actAccId = activityStore.activityAccountInfo?.act_acc_id
  postCollectionRecord({
    act_id: activityStore.activityInfo?.init?.id,
    act_acc_id: actAccId,
    type: currentLogType.value === 'gift' ? 1 : 2,
  }).then((res) => {
    if (res.code === 0) {
      dataList.value = (res.data as unknown) as CollectionRecord[];
    }
  })
}
</script>


<style>
.git-collection-record {
  z-index: 96;
}

.git-collection-record .log-tab-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.git-collection-record .log-tab-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.git-collection-record .log-tab-img-active {
  position: relative;
  scale: 1.1 1.2;
  z-index: 3;
}

.git-collection-record .content-box .item-content .item-text {
  font-weight: bold;
}
</style>
