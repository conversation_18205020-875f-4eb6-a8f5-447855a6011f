<!--
* @Description: 基础的任务组件，可在此之上叠加特殊的业务逻辑
-->
<template>
  <Resizable
    class="task-reward"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 道具展示 -->
      <Items v-model:setting="setting.items" />

      <!-- 操作按钮 -->
      <Resizable
        class="task-reward-action"
        v-model:x="setting.actionBtn.x"
        v-model:y="setting.actionBtn.y"
        v-model:width="setting.actionBtn.width"
        v-model:height="setting.actionBtn.height"
      >
        <!-- 领取按钮 -->
        <img :src="receiveBtn" class="task-reward-action-btn" @click="handleReceive" />
      </Resizable>
    </ResizableProvider>

    <!-- 恭喜获得 -->
    <CongratulationsModal
      v-if="providedRequired && setting.congratulations"
      v-model:setting="setting.congratulations"
      :show="showCongratulations"
      :prize="prize"
      @close="handleClose"
    />
  </Resizable>
</template>

<script lang="ts">
import type { CommonBackgroundSetting, CommonImageSetting, CommonSetting } from '@bish/types/src';
import type { StatefulComponent } from '@bish/types/src/admin';
import type { LotteryPrizeDrawData } from '@bish/api/src/lottery';
import { defaultConfig as itemsDefaultConfig } from './items.vue';
import type { ItemsSetting } from './items.vue';
import type { CongratulationsModalSetting } from './congratulations-modal.vue';
import { defaultConfig as congratulationsModalDefaultConfig } from './congratulations-modal.vue';

export interface TaskRewardSetting extends CommonBackgroundSetting {
  /**
   * 任务ids
   */
  taskIds: number[]
  /**
   * 是否限制用户切换登录，有些模块希望在用户领取过奖励之后，不想让用户切换其他账号再领取奖励
   * 0 否 1 是
   */
  limitLogin: number
  /**
   * 领取礼包时判断用户账号状态
   * 0 全部 1 正常 2 流失
   */
  accountStatus: number
  /**
   * 是否需要绑角色
   * 0否 1是
   */
  needBindRole: number
  /**
   * 是否伪任务
   */
  fakeTaks?: number
  /**
   * 伪任务延迟执行时间/毫秒
   */
  delayTime?: number
  /**
   * 任务未完成提示
   */
  notAccomplishTip: string
  /**
   * 领取成功提示
   */
  receivedSuccessTip: string
  /**
   * 领取失败提示
   */
  receivedFailedTip: string
  /**
   * 重复领取提示
   */
  receivedRepeatTip: string
  /**
   * 奖励图片
   */
  rewards?: CommonImageSetting
  /**
   * 操作按钮
   */
  actionBtn: CommonSetting & {
    /**
     * 领取按钮
     */
    claimBtn: string
    /**
     * 已领取按钮
     */
    claimedBtn: string
    /**
     * 未达成按钮
     */
    unfulfilledBtn: string
  }
  /**
   * 道具展示
   */
  items: ItemsSetting
  /**
   * 任务未完成跳转
   */
  notAccomplishUrl?: string
  /**
   * 恭喜弹窗
   */
  congratulations: CongratulationsModalSetting
}

export interface TaskRewardProps {
  /**
   * 配置
   */
  setting: TaskRewardSetting
  /**
   * 自定义领取逻辑
   */
  customReceive?: () => void

  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): TaskRewardSetting => {
  const itemsConfig = itemsDefaultConfig();
  return {
    x: 0,
    y: 0,
    width: 328,
    height: 190,
    bgImage: '',
    taskIds: [],
    fakeTaks: 0,
    delayTime: 0,
    notAccomplishTip: '请根据活动规则完成任务后领奖噢~',
    receivedSuccessTip: '领取成功~',
    receivedFailedTip: '领取失败~',
    receivedRepeatTip: '请勿重复领取~',
    limitLogin: 0,
    accountStatus: 0,
    needBindRole: 1,
    rewards: {
      x: 58,
      y: 0,
      width: 210,
      height: 100,
      imgLink: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-2-20/1708436372212_.png',
    },
    actionBtn: {
      x: 50,
      y: 120,
      width: 225,
      height: 60,
      claimBtn: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-2-20/1708437684361_.png',
      claimedBtn: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-2-20/1708437622236_.png',
      unfulfilledBtn: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-2-20/1708437701226_.png',
    },
    items: {
      ...itemsConfig,
      list: {
        ...itemsConfig.list,
        x: 12,
        y: 9,
      },
    },
    notAccomplishUrl: '',
    congratulations: congratulationsModalDefaultConfig(),
  };
};

export default {
  name: 'task-reward',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import useUserStore from '@bish/store/src/modules/user';
import { useLog } from '@bish/hooks/src/useLog';
import { postActivityTaskGetAwardTask, postActivityTaskUploadTask } from '@bish/api/src/activity';
import type { ComponentWithUserInfoTask, ActivityTaskUploadTaskParams, ActivityInfoConfigTask } from '@bish/api/src/activity';
import { sleep } from '@bish/utils/src/utils';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { navigateScroll } from '../__utils/location';
import Resizable from '../ui/resizable/index.vue';
import ResizableProvider from '../ui/resizable/resizable-provider.vue';
import Items from './items.vue';
import CongratulationsModal from './congratulations-modal.vue';

const props = withDefaults(defineProps<TaskRewardProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskRewardSetting): void;
}>();

const setting = useVModel(props, 'setting', emits);

const [showCongratulations, toggleShowCongratulations] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showCongratulations' });

const activityStore = useActivityStore();
const userStore = useUserStore();

let receiving = false;

const { uploadLog } = useLog();

const providedRequired = computed(() => {
  // 兼容旧数据
  return props.setting.congratulations?.required ?? true;
});

const prize = computed(() => {
  const name = props.setting.items?.items
    ?.map(item => item.name.content)
    .join('、');
  const res: LotteryPrizeDrawData = {
    id: 0,
    name,
    icon: '',
    type: 1,
    serial: '',
  };
  return res;
});

// 关联的任务列表
const relatedTaskList = computed(() => { 
  const ids = setting.value.taskIds || [];
  const taskList: ComponentWithUserInfoTask = [];
  ids.forEach((item) => {
    const task = activityStore.componentWithUserInfo.component_with_user_info?.task?.find((task) => task.id === item);
    if (task) {
      taskList.push(task);
    }
  });
  return taskList;
});

/**
 * 是否所有任务未完成
 */
const allNotCompleted = computed(() => {
  return relatedTaskList.value.every((item) => item.status === 1);
});

/**
 * 是否存在任务已完成
 */
const someCompleted = computed(() => {
  return relatedTaskList.value.some((item) => item.status === 2);
});

/**
 * 存在可领取的任务奖励
 */
const claimable = computed(() => {
  return relatedTaskList.value.find((item) => item.status === 3) && !relatedTaskList.value.find((item) => item.status === 2);
});

// 领取奖励按钮图片
const receiveBtn = computed(() => {
  const { actionBtn } = setting.value;
  if (allNotCompleted.value) {
    return actionBtn.unfulfilledBtn ?? actionBtn.claimBtn
  }
  // 存在已领取的任务，并且不存在已完成待领取的任务
  if (claimable.value) {
    return actionBtn.claimedBtn ?? actionBtn.claimBtn
  }
  if (someCompleted.value) {
    return actionBtn.claimBtn
  }
  return actionBtn.claimBtn
});

const setLimitLogin = () => {
  if (!setting.value.limitLogin) {
    return;
  }
  if (!relatedTaskList.value.length) {
    return;
  }
  // 用户任务状态为非进行时
  if (
    relatedTaskList.value.some(item => item.status !== 1) &&
    !userStore.limitLogin
  ) {
    userStore.setLimitLogin(1);
  }
};

watch(
  () => relatedTaskList.value,
  () => {
    setLimitLogin();
  },
  {
    immediate: true,
  },
);

/**
 * 获取任务配置
 * @param taskId 任务id
 */
const taskConfig = (taskId: number) => {
  return activityStore.activityInfo?.config?.task?.find(item => item.id === taskId);
}

/**
 * 是否有奖品的
 */
const hasPrize = (tConfig?: ActivityInfoConfigTask[0]) => {
  if (!tConfig) {
    return false;
  }
  const rewardType = tConfig.reward_type;
  // 虚拟道具
  if (rewardType === 1) {
    // 没有配置道具id，不需要领取任务奖励
    return !!tConfig.item_id
  }
  return true;
};

/**
 * 领取任务奖励
 */
const handleReceive = async () => {
  // 特殊逻辑处理
  if (props.customReceive) {
    props.customReceive();
    return;
  }
  if (receiving) {
    return;
  }
  // 数据上报
  uploadLog({
    event_name: 'click',
    click_id: 9,
    click_type: 3,
  });
  // 登录态拦截
  const pass = activityStore._checkIn(!!setting.value.needBindRole);
  if (!pass) {
    return;
  }
  // 领取用户账号状态判断
  if (
    setting.value.accountStatus !== 0
    && setting.value.accountStatus !== activityStore.activityAccountInfo.status
  ) {
    showToast('该账号暂不符合参与条件~');
    return;
  }
  // 先获取最新的用户与组件的数据，因为可以存在任务已经完成，但是用户没有进行刷新操作
  await activityStore.getComponentWithUserInfo()
  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!relatedTaskList.value.length) {
    showToast('活动已结束~');
    return;
  }
  await sleep(100);
  // 任务未完成
  if (allNotCompleted.value) {
    showToast(setting.value.notAccomplishTip);
    // 伪任务自动完成(仅支持只配置了一个任务的情况)
    if (setting.value.fakeTaks === 1 && relatedTaskList.value.length === 1) {
      // const delayTime = setting.value.delayTime || 0
      // setTimeout(() => {
        postActivityTaskUploadTask({
          act_id: activityStore.activityInfo.init?.id,
          act_acc_id: activityStore.activityAccountInfo.act_acc_id,
          task_code: 'fakeTask',
          task_id: relatedTaskList.value[0].id
        })
        .then(res => {
          // 任务执行完毕，再进行跳转，否则浏览器会取消请求
          if (res.code === 0) {
            if (setting.value.notAccomplishUrl) {
              navigateScroll(setting.value.notAccomplishUrl);
            }
          }
        })
        .finally(() => {
          // 在里在 ios 会报错，因为已经 window.open 跳转到其他页面了
          if (!setting.value.notAccomplishUrl) {
            activityStore.getComponentWithUserInfo()
          }
        })
      // }, delayTime)
      return;
    }
    if (setting.value.notAccomplishUrl) {
      navigateScroll(setting.value.notAccomplishUrl);
    }
    return;
  }
  // 重复领取奖励
  if (claimable.value) {
    showToast(setting.value.receivedRepeatTip);
    return;
  }

  // 判断任务是否是已完成状态 & 任务配置了商品id
  // 没有商品id的任务，有可能是为抽奖服务的，这个是不需要领取道具奖励
  const promises: Promise<void>[] = relatedTaskList.value
    .filter((item) => item.status === 2 && hasPrize(taskConfig(item.id)))
    .map((item) => {
      return new Promise((resolve, reject) => {
        const tConfig = taskConfig(item.id);
        postActivityTaskGetAwardTask(
          {
            act_id: activityStore.activityInfo.init?.id,
            act_acc_id: activityStore.activityAccountInfo.act_acc_id,
            // 这里处理一下月度礼包，本月如果有领取的话，那么到下个月 taskLogId 是不会进行重置成 0 的
            // 只有状态会重置成 1 进行中（未完成）
            // 所以当状态是 1 的时候，taskLogId 传 0
            task_log_id: item.status === 1 ? 0 : item.task_log_id, // 这里跟上边的 未完成任务冲突 了，对于月度活动建议使用 customReceive 复写
            select_type: (tConfig?.reward_type === 3 || tConfig?.reward_type === 4) ? 2 : 1,
          },
          false
        )
          .then((res) => {
            const { code, message } = res;
            switch (code) {
              case 0:
                resolve();
                break
              case 3032:
                reject(new Error(setting.value.receivedRepeatTip));
                break
              case 3042: // 错误的任务完成日志id，一般出现任务未完成 task_log_id 为 0 的场景
                reject(new Error(setting.value.receivedFailedTip + `${item.status}`));
                break
              default:
                reject(new Error(message));
                break
            }
          })
          .catch((error) => {
            console.warn('领取任务奖励出错', error);
            reject(new Error('领取任务奖励出错'));
          })
      })
    });

  if (!promises.length) {
    return;
  }
  // 提交奖励领取请求
  receiving = true;
  Promise.all(promises)
    .then(() => {
      receiving = false;
      // 重新获取用户与组件的数据
      activityStore.getComponentWithUserInfo();
      // 配置了恭喜获得弹窗
      if (setting.value.congratulations?.required) {
        toggleShowCongratulations(true);
      } else {
        showToast(setting.value.receivedSuccessTip);
      }
    })
    .catch((error) => {
      receiving = false;
      showToast(error.message);
    });
};

/**
 * web上报任务完成动作
 */
const handleUploadTask = (params: ActivityTaskUploadTaskParams) => {
  postActivityTaskUploadTask(params)
}

const handleClose = () => {
  toggleShowCongratulations(false);
};
</script>

<style lang="less">
.task-reward {
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-action {
    &-btn {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
