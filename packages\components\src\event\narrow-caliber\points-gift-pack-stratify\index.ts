import { defaultConfig as serverRolePickerDefaultConfig } from '../../../business/server-role-picker.vue';
import { defaultConfig as downloadGameDefaultConfig } from '../../../business/download-game.vue';
import type { PointsGiftPackStratifySetting, PointsGiftPackStratifyItems } from './points-gift-pack-stratify';

export * from './points-gift-pack-stratify';

export const itemsDefaultGiftId = -1; // 默认礼包id

export const itemsDefaultConfig = (): PointsGiftPackStratifyItems[0]['items'][0] => {
  return {
    imgLink: '',
    name: '',
  };
};

export const defaultItem = (): PointsGiftPackStratifyItems[0] => {
  return {
    gifPackId: 0,
    items: [itemsDefaultConfig()],
  }
};

export const defaultConfig = (): PointsGiftPackStratifySetting => {
  const serverRolePickerConfig = serverRolePickerDefaultConfig();
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 509,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250508/1746702712544-packet-bg.png',
    items: [
      {
        gifPackId: itemsDefaultGiftId,
        items: [
          {
            imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250508/1746709824907-%E7%A7%B0%E5%8F%B7120.png',
            name: '“盼君归来”称号',
          },
          {
            imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250513/1747134584664-%E5%AE%9D%E7%AE%B1.png',
            name: '月卡或元宝箱',
          },
        ],
      },
    ],
    list: {
      x: 40,
      y: 151,
      width: 300,
      height: 174,
      bgImage: '',
    },
    item: {
      x: 0,
      y: 0,
      width: 88,
      height: 127,
      itemImg: {
        x: 16,
        y: 18,
        width: 60,
        height: 60,
      },
      name: {
        x: 0,
        y: 107,
        width: 92,
        height: 20,
        fontSize: 10,
        color: '#FCFFC8',
        alignItems: 'center',
        align: 'center',
      },
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250508/1746700348078-packet-item-bg.png',
    },
    serverRolePicker: {
      ...serverRolePickerConfig,
      selector: {
        ...serverRolePickerConfig.selector,
        x: 0,
        y: 0,
        width: 0,
        height: 0,
        color: '#505924',
        fontSize: 0,
        switchColor: '#1C9BD0',
      },
    },
    receiveBtnImage: {
      x: 114,
      y: 353,
      width: 152,
      height: 48,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250506/1746523256886-claim-btn.png',
    },
    receivedBtnImage: {
      x: 114,
      y: 353,
      width: 152,
      height: 48,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250506/1746523259816-claimed-btn.png',
    },
    downloadBtn: {
      ...downloadGameDefaultConfig(),
      x: 198,
      y: 150,
      width: 127,
      height: 38,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-9-3/1725345444453_.png',
      enabled: false,
    },
    bindRole: 0,
  }
}
