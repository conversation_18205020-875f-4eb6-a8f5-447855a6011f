import type { CongratulationsModalSetting } from './congratulations-modal';
import { defaultConfig as defaultCongratulationsContentConfig } from './congratulations-content';

export * from './congratulations-modal';

export const defaultConfig = (): CongratulationsModalSetting => {
  return {
    required: true,
    x: 0,
    y: 0,
    width: 375,
    height: 376,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1722967826730_.png',
    closeBtn: {
      x: 330,
      y: -5,
      width: 33,
      height: 33,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1722967853959_.png',
    },
    ...defaultCongratulationsContentConfig(),
  };
}; 
