import { createI18n as createVueI18n } from 'vue-i18n';
import storage from '@bish/utils/src/storage/localStorage';
import { parseQueryString } from '@bish/utils/src/utils';
import zh_CN from './locales/zh-CN';
import en_US from './locales/en-US';

export const messages = {
  'zh-CN': zh_CN,
  'en-US': en_US,
};

export type LocaleType = keyof typeof messages;

export const localesOptions: { label: string, value: LocaleType }[] = [
  {
    label: '中文-简体',
    value: 'zh-CN',
  },
  {
    label: '英文',
    value: 'en-US',
  },
];

const storageKey = '__lang__';
let queryLocale: LocaleType | undefined = undefined;

// #ifdef H5
const query = parseQueryString(window.location.href);

if (query.lang) {
  queryLocale = query.lang as LocaleType;
}
// #endif

const i18n = createVueI18n<[typeof zh_CN], LocaleType, true>({
  // legacy: false, // 使用 composition API
  locale: queryLocale || storage.getLocalStorage(storageKey) || 'zh-CN', // 初始语言
  fallbackLocale: 'zh-CN',
  globalInjection: true,  // 表明使用全局t函数
  messages,
});

export const changeI18nLocale = (locale: LocaleType) => {
  i18n.global.locale = locale;

  storage.setLocalStorage(storageKey, locale);
}

export default i18n;
