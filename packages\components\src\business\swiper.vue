<template>
  <Resizable
    class="swipe"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <Resizable
      class="swipe-content"
      v-model:x="setting.content.x"
      v-model:y="setting.content.y"
      v-model:width="setting.content.width"
      v-model:height="setting.content.height"
    >
      <ResizableProvider>
        <van-swipe
          v-if="!isWeapp"
          ref="swipeRef"
          :style="swiperStyle"
          :show-indicators="false"
          :autoplay="3000"
          :stop-propagation="false"
          lazy-render
          @change="handleSwipeChange"
        >
          <van-swipe-item v-for="(item, index) in setting.items" :key="`${index}_${item.imgLink}_${item.link}`">
            <a class="swipe-items-item" @mousedown="handleMouseDown" @mouseup="(e) => handleMouseUp(e, item.link!, index)">
              <img class="swipe-items-item-img" :src="item.imgLink" :draggable="false" />
            </a>
          </van-swipe-item>
        </van-swipe>
        <swiper
          v-else
          circular
          :style="swiperStyle"
          :indicator-dots="false"
          :autoplay="true"
          :interval="3000"
          :current="active"
          @change="(e: any) => handleSwipeChange(e.detail.current)"
        >
          <swiper-item v-for="(item, index) in setting.items" :key="`${index}_${item.imgLink}_${item.link}`">
            <div class="swipe-items-item" @mousedown="handleMouseDown" @mouseup="(e) => handleMouseUp(e, item.link!, index)">
              <img class="swipe-items-item-img" :src="item.imgLink" :draggable="false" />
            </div>
          </swiper-item>
        </swiper>

        <!-- 指示器 -->
        <Resizable
          v-if="setting.indicator.show"
          class="swipe-indicator"
          v-model:x="setting.indicator.x"
          v-model:y="setting.indicator.y"
          v-model:width="setting.indicator.width"
          v-model:height="setting.indicator.height"
          :confined="false"
        >
          <div
            v-for="(item, index) in setting.items"
            :key="`${index}_${item.imgLink}_${item.link}`"
            class="swipe-indicator-item"
            @click="() => swipeTo(index)"
          >
            <template v-if="index === active">
              <!-- 激活节点 -->
              <UiImage
                v-if="setting.indicator.dotActive.imgLink"
                v-model:setting="setting.indicator.dotActive"
                :movable="false"
              />
            </template>
            <template v-else>
              <!-- 默认节点 -->
              <UiImage
                v-if="setting.indicator.dot.imgLink"
                v-model:setting="setting.indicator.dot"
                :movable="false"
              />
            </template>
          </div>
        </Resizable>
        <!-- 按钮-上一页 -->
        <UiImage
          v-if="setting.btn?.prev?.imgLink"
          v-model:setting="setting.btn.prev"
          :confined="false"
          @click="() => swipeTo(active - 1)"
        />
        <!-- 按钮-下一页 -->
        <UiImage
          v-if="setting.btn?.next?.imgLink"
          v-model:setting="setting.btn.next"
          :confined="false"
          @click="() => swipeTo(active + 1)"
        />
      </ResizableProvider>
    </Resizable>
  </Resizable>
</template>

<script lang="ts">
import type { CommonBackgroundSetting, CommonSetting } from '@bish/types/src';
import type { ImageSetting } from '../ui/image/index.vue';

export interface SwiperSetting extends CommonBackgroundSetting {
  /**
   * 内容
   */
  content: CommonSetting
  /**
   * 轮播列表
   */
  items: {
    /**
     * 图片地址
     */
    imgLink: string
    /**
     * 跳转链接
     */
    link?: string
  }[]
  /**
   * 指示器
   */
  indicator: CommonSetting & {
    /**
     * 是否显示
     */
    show: boolean
    /**
     * 默认节点
     */
    dot: ImageSetting
    /**
     * 激活节点
     */
    dotActive: ImageSetting
  }
  /**
   * 切换按钮
   */
  btn: {
    /**
     * 上一页按钮
     */
    prev: ImageSetting
    /**
     * 下一页按钮
     */
    next: ImageSetting
  }
}

export interface SwiperProps {
  /**
   * 配置
   */
  setting: SwiperSetting
}

export const defaultConfig = (): SwiperSetting => {
  return {
    x: 0,
    y: 0,
    width: 361,
    height: 207,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724898356271_.png',
    content: {
      x: 12,
      y: 9,
      width: 336,
      height: 187,
    },
    items: [
      {
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724897382834_.png',
        link: '',
      },
      {
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724897385423_.png',
        link: '',
      },
      {
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724897387775_.png',
        link: '',
      },
      {
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724897410685_.png',
        link: '',
      },
      {
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724897412701_.png',
        link: '',
      },
    ],
    indicator: {
      x: 114,
      y: 154,
      width: 108,
      height: 40,
      show: true,
      dot: {
        width: 18,
        height: 18,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724902982376_.png',
      },
      dotActive: {
        width: 18,
        height: 18,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724902978069_.png',
      },
    },
    btn: {
      prev: {
        x: 0,
        y: 100,
        width: 22,
        height: 22,
        // imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241108/1731035739302-%E8%89%B2%E7%9B%B8%EF%BC%8F%E9%A5%B1%E5%92%8C%E5%BA%A6%201%20%E6%8B%B7%E8%B4%9D.png'
      },
      next: {
        x: 200,
        y: 100,
        width: 22,
        height: 22,
        // imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241108/1731035741379-%E8%89%B2%E7%9B%B8%EF%BC%8F%E9%A5%B1%E5%92%8C%E5%BA%A6%201.png'
      },
    },
  };
};

export default {
  name: 'swiper',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, watch, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
// #ifdef H5
import { showImagePreview } from 'vant';
// #endif
import type { SwipeInstance } from 'vant';
import useAdminStore from '@bish/store/src/modules/admin';
import { useLog } from '@bish/hooks/src/useLog';
import { userAgent } from '@bish/utils/src/utils';
import { pxTransform } from '@bish/utils/src/viewport';
import UiImage from '../ui/image/index.vue';
import Resizable from '../ui/resizable/index.vue';
import ResizableProvider from '../ui/resizable/resizable-provider.vue';

const props = withDefaults(defineProps<SwiperProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: SwiperSetting): void;
}>();

const active = ref(0);
const swipeRef = ref<SwipeInstance | undefined>(undefined);

const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const { uploadLog } = useLog();

const { isUserAgentType } = userAgent();

// 微信小程序环境
const isWeapp = isUserAgentType === 'WX_MINI';

let memo = {
  x: 0,
  y: 0,
};

const swiperStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(setting.value.content.width),
    height: pxTransform(setting.value.content.height),
  }
});

watch(
  () => props.setting.content.width,
  () => {
    swipeRef.value?.resize();
  },
);

const swipeTo = (index: number) => {
  let next = index;
  if (next >= setting.value.items.length) {
    next = 0;
  }
  if (next < 0) {
    next = setting.value.items.length - 1;
  }
  if (!isWeapp) {
    if (swipeRef.value) {
      active.value = next;
      swipeRef.value.swipeTo(next);
    }
  } else {
    active.value = next;
  }
};

const handleSwipeChange = (index: number) => {
  active.value = index;
};

const handleMouseDown = (e: MouseEvent) => {
  memo.x = e.clientX;
  memo.y = e.clientY;
};

const handleMouseUp = (e: MouseEvent, link: string, index: number) => {
  if (e.clientX === memo.x && e.clientY === memo.y) {
    if (link) {
      // 微信小程序暂不支持跳转
      if (!isWeapp) {
        window.open(link, '_blank');
      }
    } else {
      // 图片预览
      if (adminStore.editable) {
        return;
      }
      const list = setting.value.items.map((item) => item.imgLink);
      if (!isWeapp) {
        showImagePreview(list, index);
      } else {
        uni.previewImage({
          urls: list,
          current: index,
        });
      }
    }

    // 事件上报--点击轮播图
    uploadLog({
      event_name: 'click',
      click_type: 2, // 轮播图
    });
  }
};

// const handleJump = () => {
//   // 事件上报--点击轮播图
//   uploadLog({
//     event_name: 'click',
//     click_type: 2, // 轮播图
//   });
// };
</script>

<style>
.swipe {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.swipe-content {}

.swipe-items {}

.swipe-items-item {
  width: 100%;
  height: 100%;
}

.swipe-items-item-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.swipe-indicator {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.swipe-indicator-item {}
</style>