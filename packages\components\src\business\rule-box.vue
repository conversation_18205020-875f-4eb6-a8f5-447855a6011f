<!--
* @Description: 活动规则
-->
<template>
  <Resizable
    class="rule-box"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <!-- 标题 -->
    <UiImage
      v-if="setting.title?.imgLink"
      v-model:setting="setting.title"
    />

    <Resizable
      class="rule-box-content"
      v-model:x="setting.content.x"
      v-model:y="setting.content.y"
      v-model:width="setting.content.width"
      v-model:height="setting.content.height"
    >
      <!-- 内容 -->
      <div v-if="!activityRule" class="rule-box-content-empty" :style="contentStyle">
        暂无内容
      </div>
      <div v-else v-html="activityRule" :style="contentStyle" />
    </Resizable>
  </Resizable>
</template>

<script lang="ts">
import type { CommonBackgroundSetting, CommonSetting } from '@bish/types/src'
import { pxTransform } from '@bish/utils/src/viewport';
import type { ImageSetting } from '../ui/image/index.vue';

export interface RuleBoxSetting extends CommonBackgroundSetting {
  /**
   * 标题
   */
  title?: ImageSetting
  /**
   * 内容
   */
  content: CommonSetting
}

export interface RuleBoxProps {
  /**
   * 配置
   */
  setting: RuleBoxSetting
}

export const defaultConfig = (): RuleBoxSetting => {
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 172,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-9-3/1725358947929_.png',
    title: {
      x: 147,
      y: 25,
      width: 80,
      height: 18,
      imgLink: '',
    },
    content: {
      x: 30,
      y: 58,
      width: 316,
      height: 77,
    },
  };
};

export default {
  name: 'rule-box',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import Resizable from '../ui/resizable/index.vue';
import UiImage from '../ui/image/index.vue';

const props = withDefaults(defineProps<RuleBoxProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: RuleBoxSetting): void;
}>();

const activityStore = useActivityStore();

const setting = useVModel(props, 'setting', emits);

const activityRule = computed(() => {
  return activityStore.activityInfo.init?.activity_rule || '';
});

const contentStyle = computed<CSSProperties>(() => {
  return {
    fontSize: `${pxTransform(16)}`,
    lineHeight: 1.2,
  };
});
</script>

<style>
.rule-box {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.rule-box-content {
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  text-align: justify;
  word-break: break-all;
}

.rule-box-content p {
  margin: 0;
  display: inline-block;
}

.rule-box-content span {
  white-space: normal !important;
}

.rule-box-content-empty {
  text-align: center;
}
</style>