<!--
 * @description: 登录组件-微信小程序版本
 * @author: xiezhixiong
-->
<template>
  <Resizable
    class="login"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    @click="handleLoginClick"
  >
    <div :style="loginStyle" class="login-root">
      {{ userName }}
    </div>

    <!-- 登录弹窗 -->
    <LoginWeappModal
      :show="popupStore.showLoginModal"
      v-model:setting="setting.modalSetting"
      @close="handleClose"
      @login="handleLogin"
    />
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'login-weapp',
}
</script>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useVModel } from '@vueuse/core'
import useActivityPageStore from '@bish/store/src/modules/activityPage'
import useAdminStore from '@bish/store/src/modules/admin'
import usePopupStore from '@bish/store/src/modules/popup'
import useUserStore from '@bish/store/src/modules/user'
import useActivityStore from '@bish/store/src/modules/activity'
import { maskPhoneNumber } from '@bish/utils/src/utils'
import { pxTransform } from '@bish/utils/src/viewport'
import { useLog } from '@bish/hooks/src/useLog'
import useRouteQuery from '@bish/hooks/src/useRouteQuery'
import { applyAlphaToColor } from '../../../__utils/color'
import Resizable from '../../../ui/resizable/index.vue'
import { defaultConfig } from './index'
import type { LoginWeappSetting } from './login-weapp'
import LoginWeappModal from './login-weapp-modal.vue';

export interface LoginWeappProps {
  setting: LoginWeappSetting
}

const props = withDefaults(defineProps<LoginWeappProps>(), {
  setting: defaultConfig
})

const emits = defineEmits<{
  (event: 'update:setting', value: LoginWeappSetting): void;
}>()

const activityPageStore = useActivityPageStore()
const userStore = useUserStore()
const popupStore = usePopupStore()
const activityStore = useActivityStore()
const adminStore = useAdminStore()

const setting = useVModel(props, 'setting', emits)
const { query } = useRouteQuery()

const { uploadLog } = useLog()

const isLimitSwitch = () => {
  return props.setting.limitSwitch === 1
}

const userName = computed(() => {
  const name = userStore.userData?.phone_number
    ? maskPhoneNumber(userStore.userData?.phone_number)
    : userStore.userData?.name
  return name ? `${name}${!isLimitSwitch() ? ' [退出登录]' : ''}` : '您好，请 [登录] '
})

const linearColor = computed(() => {
  return props.setting.theme ? applyAlphaToColor(props.setting.theme, 0.01) : props.setting.theme
})

const loginStyle = computed(() => ({
  'background-image': `linear-gradient(to ${props.setting.align === 'left' ? 'right' : 'left'}, ${props.setting.theme}, ${linearColor.value})`,
  'justify-content': props.setting.align === 'left' ? 'flex-start' : 'flex-end',
  padding: `0 ${pxTransform(8)}`,
  fontSize: pxTransform(12),
}))

// 上报埋点
const handleUploadLog = () => {
  const extraBody: Record<string, any> = {
    event_name: 'login',
  }
  if (query.value.invite_code) {
    extraBody.invited_status = 1
    if (query.value.invite_type === '1') {
      extraBody.extra = JSON.stringify({
        login_type: 2,
      })
    }
  }
  uploadLog(extraBody)
}

// 监听用户登录
watch(
  () => userStore.userData.token,
  async (newVal) => {
    if (newVal) {
      // 获取活动账号信息
      await activityStore.getActivityAccountInfo()
      handleUploadLog()
    }
  }
)

const handleLoginClick = async () => {
  if (userStore.isLogined) {
    if (isLimitSwitch()) {
      return
    }
    try {
      if (!adminStore.editable) {
        await userStore.logout()
        activityStore.resetActivityUserData()
        // 重置微信小程序默认分享消息
        activityPageStore.setDefaultShareContent()
      }
    } catch (error) {}
  } else {
    popupStore.setShowLoginModal(true)
  }
}

const handleClose = () => {
  popupStore.setShowLoginModal(false)
  // 用户主动关闭弹窗时，清空队列
  userStore.scheduler.clear()
}

const handleLogin = () => {
  popupStore.setShowLoginModal(false)
}
</script>

<style>
.login-root {
  display: flex;
  align-items: center;
  height: 100%;
  color: #fff;
}

.login-radio {
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-radio-icon {
  border-radius: 100%;
  border-width: 1px;
  border-style: solid;
}

.login-radio-icon-inner {
  width: 60%;
  height: 60%;
  border-radius: 100%;
}
</style>