/**
 * 根据传入的颜色和透明度返回加了透明度后的颜色
 * @param color 十六进制、RGB、RGBA 颜色
 * @param opacity 透明度，取值 0-1
 * @returns
 */
export function applyAlphaToColor(color: string, alpha: number) {
  // 将颜色值转换成小写字母，并去除空格
  color = color.toLowerCase().replace(/\s/g, '');

  // 匹配十六进制颜色值
  let match = color.match(/^#?(?:[0-9a-f]{3}){1,2}$/i);
  if (match !== null) {
    // 将颜色值转换成六位十六进制格式
    let hexColor = match[0].replace(/^#?/, '');
    if (hexColor.length === 3) {
      hexColor = hexColor.replace(/(.)/g, '$1$1');
    }
    // 将透明度转换成十六进制格式
    let alphaHex = Math.round(alpha * 255).toString(16);
    if (alphaHex.length === 1) {
      alphaHex = '0' + alphaHex;
    }
    // 合并颜色值和透明度，得到加了透明度后的颜色值
    return '#' + hexColor + alphaHex;
  }

  // 匹配 rgba 颜色值
  match = color.match(/^rgba?\((\d{1,3}),(\d{1,3}),(\d{1,3}),?([^,\)]+)?\)$/i);
  if (match !== null) {
    // 按照给定的透明度计算新的 alpha 值
    const newAlpha = alpha;
    // 将新的 alpha 值转换成浮点数，范围在 0 到 1 之间
    const alphaFloat = isNaN(newAlpha) ? 1 : Math.min(Math.max(parseFloat(`${newAlpha}`), 0), 1);
    // 合并颜色值和透明度，得到加了透明度后的颜色值
    return `rgba(${match[1]}, ${match[2]}, ${match[3]}, ${alphaFloat})`;
  }

  // 匹配 rgb 颜色值
  match = color.match(/^rgb\((\d{1,3}),(\d{1,3}),(\d{1,3})\)$/i);
  if (match !== null) {
    // 合并颜色值和透明度，得到加了透明度后的颜色值
    return applyAlphaToColor(`rgba(${match[1]}, ${match[2]}, ${match[3]})`, alpha);
  }

  // 如果传入的颜色值不是合法的颜色格式，则直接返回原始值
  return color;
}