// import { http, Result } from '@/utils/request'
// import { useEnv } from '@/hooks/useEnv'
// import axios from 'axios'

// const { VITE_AD_POOL_URL } = useEnv()

// export type PoolLandingParams = {
//   /**
//    * 固定值
//    */
//   sy_ad_id: number
//   /**
//    * wid,用插件生成的标识,之前群上给过的js
//    */
//   wid?: string
// }

// /**
//  * @description 包体归因
//  * @param {*} params.sy_ad_id 活动ID
//  * @param {*} params.wid wid,用插件生成的标识,之前群上给过的js
//  * @returns
//  */
// export function postPoolLanding(params: PoolLandingParams) {
//   return axios.get(`${VITE_AD_POOL_URL}/pool/landing`, { params })
// }