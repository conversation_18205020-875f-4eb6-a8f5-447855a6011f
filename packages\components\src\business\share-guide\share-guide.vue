<!--
* @Description: 分享引导
-->
<template>
  <Overlay
    :show="show"
    z-index="99"
    @click="handleOverlayClick"
  >
    <div class="share-guide-content">
      <ResizableProvider>
        <!-- 提示标题 -->
        <UiImage
          v-model:setting="setting.title"
          :confined="false"
        />
        <!-- 我知道了 -->
        <UiImage
          v-if="setting.gotBtn.enabled"
          v-model:setting="setting.gotBtn"
          :confined="false"
          @click.stop="handleGotIt"
        />
        <!-- 复制链接按钮 -->
        <UiImage
          v-if="setting.copyLinkBtn.enabled"
          v-model:setting="setting.copyLinkBtn"
          :confined="false"
          @click.stop="handleCopy"
        />
        <!-- 关闭按钮 -->
        <UiImage
          v-if="setting.closeBtn.enabled"
          v-model:setting="setting.closeBtn"
          :confined="false"
          @click.stop="handleClose"
        />
      </ResizableProvider>
    </div>
  </Overlay>
</template>

<script lang="ts">
export default {
  name: 'share-guide',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults } from 'vue';
import { useVModel } from '@vueuse/core';
import { whiteUrl } from '@bish/utils/src/urlSearch';
import Overlay from '@bish/ui/src/overlay/index.vue';
import type { ShareGuideSetting } from './share-guide';
import { copyText } from '../../__utils/text';
import ResizableProvider from '../../ui/resizable/resizable-provider.vue';
import UiImage from '../../ui/image/index.vue';

export interface ShareGuideProps {
  /**
   * 配置
   */
  setting: ShareGuideSetting
  /**
   * 是否显示
   */
  show: boolean
}

const props = withDefaults(defineProps<ShareGuideProps>(), {
  show: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: ShareGuideSetting): void;
  (event: 'close'): void;
}>();

const setting = useVModel(props, 'setting', emits);

const handleClose = () => {
  emits('close');
};

const handleOverlayClick = () => {
  handleClose();
};

const handleGotIt = () => {
  handleClose();
};

const handleCopy = () => {
  copyText(whiteUrl(window?.location.href));
};
</script>

<style>
.share-guide-content {
  position: relative;
  max-width: 375PX;
  margin: 0 auto;
}
</style>