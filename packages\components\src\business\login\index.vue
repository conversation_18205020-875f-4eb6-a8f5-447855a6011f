<!--
 * @description: 登录组件
 * @author: linziyang
-->
<template>
  <Resizable
    v-if="!userStore.limitLogin"
    class="login"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    @click="handleLoginClick"
  >
    <div :style="loginStyle" class="login-root">
      {{ userName }}
    </div>

    <!-- web普通登录 -->
    <login-modal
      v-model:setting="setting.modalSetting"
      :theme="setting.theme"
      :show="openLoginModal"
      @close="handleCloseLoginModal"
      @after-login="handleUploadLog"
    />
    
    <!-- 微信小程序webview内嵌登录-展示用 -->
    <login-weapp-embed
      v-if="setting.weappEmbedSetting"
      v-model:setting="setting.weappEmbedSetting"
      :show="openWeappEmbed"
      :theme="setting.theme"
    />

    <!-- 登录弹窗 -->
    <LoginWeappModal
      v-if="setting.weappSetting"
      :show="openWeapp"
      v-model:setting="setting.weappSetting.modalSetting"
      @close="() => toggleWeapp(false)"
    />
  </Resizable>

</template>

<script lang="ts">
import type { LoginModalSetting } from './login-modal.vue'
import type { LoginWeappEmbedSetting } from './login-weapp-embed/index'
import type { LoginWeappSetting } from './login-weapp'
import type { CommonSetting } from '@bish/types/src'
import { defaultConfig as modalDefaultConfig } from './login-modal.vue'
import useAdminStore from '@bish/store/src/modules/admin'
import type { StatefulComponent } from '@bish/types/src/admin';

export interface LoginSetting extends CommonSetting {
  modalSetting: LoginModalSetting
  weappEmbedSetting?: LoginWeappEmbedSetting
  weappSetting?: LoginWeappSetting
  theme?: string
  align?: 'left' | 'right'
  /**
   * 是否内嵌微信小程序，1是 0否
   */
  weappWebview: number
  /**
   * 是否微信小程序，1是 0否
   */
  weapp: number
  /**
   * 是否限制退出登录，1是 0否，默认 0
   */
  limitSwitch: number
}

export interface LoginProps {
  setting: LoginSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): LoginSetting => ({
  theme: 'rgb(36,43,69)',
  align: 'right',
  modalSetting: modalDefaultConfig(),
  weappEmbedSetting: undefined,
  weappSetting: undefined,
  x: 148,
  y: 65,
  width: 227,
  height: 23,
  weappWebview: 0,
  weapp: 0,
  limitSwitch: 0,
})

export default {
  name: 'login'
}
</script>
<script setup lang="ts">
import { computed, watch } from 'vue'
import useUserStore from '@bish/store/src/modules/user'
import useActivityStore from '@bish/store/src/modules/activity'
import usePopupStore from '@bish/store/src/modules/popup'
import { additionalLinkParameters, maskPhoneNumber, parseQueryString } from '@bish/utils/src/utils'
import { pxTransform } from '@bish/utils/src/viewport'
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus'
import { useLog } from '@bish/hooks/src/useLog'
import localStorage from '@bish/utils/src/storage/localStorage'
import { applyAlphaToColor } from '../../__utils/color'
import Resizable from '../../ui/resizable/index.vue'
import LoginModal from './login-modal.vue'
import LoginWeappEmbed from './login-weapp-embed/login-weapp-embed.vue'
import LoginWeappModal from './login-weapp/login-weapp-modal.vue';

const props = withDefaults(defineProps<LoginProps>(), {
  setting: defaultConfig
})

const emits = defineEmits<{
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const userStore = useUserStore()
const activityStore = useActivityStore()
const popupStore = usePopupStore()
const adminStore = useAdminStore()

const [openLoginModal, toggleOpenLoginModal] = useControllableStatus(props, emits, { fieldName: 'showLoginModal' });
const [openWeappEmbed] = useControllableStatus(props, emits, { fieldName: 'showWeappEmbed' });
const [openWeapp, toggleWeapp] = useControllableStatus(props, emits, { fieldName: 'showWeapp' });

const { uploadLog } = useLog()

const isLimitSwitch = () => {
  return props.setting.limitSwitch === 1
};

const userName = computed(() => {
  const name = userStore.userData.phone_number
    ? maskPhoneNumber(userStore.userData.phone_number)
    : userStore.userData.name
  return name ? `${name}${!isLimitSwitch() ? ' [退出登录]' : ''}` : '您好，请 [登录] '
})

const linearColor = computed(() => {
  return props.setting.theme ? applyAlphaToColor(props.setting.theme, 0.01) : props.setting.theme
})

const loginStyle = computed(() => ({
  'background-image': `linear-gradient(to ${props.setting.align === 'left' ? 'right' : 'left'}, ${props.setting.theme}, ${linearColor.value})`,
  'justify-content': props.setting.align === 'left' ? 'flex-start' : 'flex-end',
  padding: `0 ${pxTransform(8)}`,
  fontSize: pxTransform(12),
}))


// 通过 token 登录后，上报登录事件
watch(
  () => activityStore.activityAccountInfo,
  (newVal) => {
    if (localStorage.getLocalStorage('__tokenLogin__') && newVal?.account_id) {
      localStorage.removeLocalStorage('__tokenLogin__')
      handleUploadLog()
    }
  },
  { immediate: true },
)

watch(
  () => popupStore.showLoginModal,
  (newVal) => {
    toggleOpenLoginModal(newVal);
  },
);

const handleLoginClick = async () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return
  }
  if (userStore.isLogined) {
    if (isLimitSwitch()) {
      return
    }
    try {
      if (!adminStore.editable) {
        await userStore.logout()
        const queryParams = parseQueryString(window.location.href)
        const newUrl = additionalLinkParameters(
          {
            event_page: queryParams.event_page,
          },
          window.location.href.split('?')[0]
        )
        // 重定向到新的URL
        window.location.replace(newUrl)
        window.location.reload()
      }
    } catch (error) {}
  } else {
    popupStore.setShowLoginModal(true)
  }
}

const handleCloseLoginModal = () => {
  // 同步数据
  if (openLoginModal && !popupStore.showLoginModal) {
    toggleOpenLoginModal(false);
  }
  popupStore.setShowLoginModal(false);
}

// 上报埋点
const handleUploadLog = () => {
  const query = parseQueryString(window.location.href)
  const extraBody: Record<string, any> = {
    event_name: 'login'
  }
  if (query.invite_code || sessionStorage.getItem('inviteCode')) {
    extraBody.invited_status = 1
    if (query.invite_type === '1' || sessionStorage.getItem('inviteType') === '1') {
      extraBody.extra = JSON.stringify({
        login_type: 2,
      })
    }
  }
  uploadLog(extraBody)
}
</script>

<style lang="less" scoped>
.login-root {
  display: flex;
  align-items: center;
  height: 100%;
  color: #fff;
}
</style>