<!--
* @Description: 红包任务
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="task-item"
    :style="{
      backgroundImage: `url(${bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 标题 -->
      <UiText v-if="setting.title?.content" v-model:setting="setting.title" />

      <!-- 描述 -->
      <UiText v-if="setting.description?.content" v-model:setting="setting.description" />
      
      <!-- 完成进度 -->
      <UiText
        v-if="setting.process"
        v-model:setting="setting.process"
        :interpolation="processInterpolation"
        :confined="false"
      />

      <!-- 领取按钮 -->
      <template v-if="showClaimBtn">
        <UiImage
          v-if="setting.claimBtn.imgLink"
          :setting="setting.claimBtn"
          @click="handleClaim"
          :confined="false"
        />
      </template>
      <!-- 进行中按钮 -->
      <template v-if="showGoBtn">
        <UiImage
          :setting="setting.goBtn"
          @click="handleClaim"
          :confined="false"
        />
      </template>
      <!-- 已领取 -->
      <template v-if="userTask?.status === 3">
        <UiImage
          v-if="setting.achievedBtn.imgLink"
          :setting="setting.achievedBtn"
          @click="handleClaim"
          :confined="false"
        />
      </template>
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'red-packet',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import type { StatefulComponent } from '@bish/types/src/admin';
import { useLog } from '@bish/hooks/src/useLog';
import { postActivityTaskGetAwardTask } from '@bish/api/src/activity';
import { defaultConfig } from './index';
import type { TaskRedPacketSetting } from './red-packet';
import Resizable from '../../../../ui/resizable/index.vue';
import ResizableProvider from '../../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../../ui/image/index.vue';
import UiText from '../../../../ui/text/index.vue';

export interface TaskCollectionProps {
  /**
   * 配置
   */
  setting: TaskRedPacketSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TaskCollectionProps>(), {
  setting: () => defaultConfig(),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskRedPacketSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityStore = useActivityStore();

const setting = useVModel(props, 'setting', emits);

const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

const userTask = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
});

// const completed = computed(() => {
//   return userTask.value ? userTask.value?.progress_num === userTask.value?.target_num : false;
// });

const process = computed(() => {
  return `${ userTask.value?.progress_num || 0}/${taskConfig.value?.target_num || 0}`;
});

const processInterpolation = computed(() => {
  return {
    process: process.value,
  };
});

const bgImage = computed(() => {
  if (userTask?.value?.status === 2 && setting.value.activeBgImage) {
    return setting.value.activeBgImage;
  }
  // 没有配置完成背景图，取默认背景图
  return userTask?.value?.status !== 3 ? setting.value.bgImage : setting.value.activeBgImage || setting.value.bgImage;
});

const showClaimBtn = computed(() => {
  // 用户未登录，展示领取按钮
  if (!userTask.value) {
    return true;
  }
  // 任务已完成，但是未领取，展示领取按钮
  if (userTask.value?.status === 2) {
    return true;
  }
  // 未配置进行按钮，并且状态为进行中，展示领取按钮
  if (!setting.value.goBtn?.imgLink && userTask.value?.status === 1) {
    return true;
  }
  return false;
});

const showGoBtn = computed(() => {
  // 配置了进行按钮，并且状态进行中，展示进行按钮
  if (setting.value.goBtn?.imgLink && userTask.value?.status === 1) {
    return true;
  }
  return false;
});

const handleClaim = async () => {
  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfig.value) {
    showToast('活动已结束~');
    return;
  }
  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (userTask?.value?.status === 3) {
    if (setting.value.receivedRepeatTip) {
      showToast(setting.value.receivedRepeatTip);
    }
    return;
  }
  if (userTask?.value?.status === 1) {
    if (setting.value.notAccomplishTip) {
      showToast(setting.value.notAccomplishTip);
    }
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  if (!userTask.value) {
    return;
  }
  try {
    const res = await postActivityTaskGetAwardTask(
      {
        act_id: activityStore.activityInfo.init?.id,
        act_acc_id: activityStore.activityAccountInfo.act_acc_id,
        // 这里处理一下月度礼包，本月如果有领取的话，那么到下个月 taskLogId 是不会进行重置成 0 的
        // 只有状态会重置成 1 进行中（未完成）
        // 所以当状态是 1 的时候，taskLogId 传 0
        task_log_id: userTask.value.status === 1 ? 0 : userTask.value.task_log_id, // 这里跟上边的 未完成任务冲突 了，对于月度活动建议使用 customReceive 复写
        select_type: 2, // 红包任务
      },
    );
    if (res.code === 0) {
      setTimeout(() => {
        if (setting.value.receivedSuccessTip) {
          showToast(setting.value.receivedSuccessTip);
        }
        // 重新获取用户与组件的数据
        activityStore.getComponentWithUserInfo();
      }, 500);
    }
  } catch (error) {
    console.log('领取红包奖励失败', error);
  }
};
</script>

<style>
.task-item {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.task-item-btn__disabled {
  filter: grayscale(100%);
}
</style>
