import { readonly, ref, watchEffect } from 'vue'
import type { ModuleStatus, StatefulComponent } from '@bish/types/src/admin';

/**
 * @description admin 状态切换 hook，目前用户弹窗显示状态的切换
 * @params {*} params.props 组件的 props
 * @params {*} params.options 配置项
 * @params {*} params.options.fieldName 字段名称
 * @returns
 */
export default function useControllableStatus<P extends StatefulComponent, Name extends string>(
  props: P,
  emits: (name: Name, ...args: any[]) => void,
  options: {
    /**
     * 字段名称
     */
    fieldName: string,
    /**
     * 默认值
     */
    default?: boolean
  }
) {
  const show = ref(options?.default || false);
  const { fieldName } = options;

  watchEffect(() => {
    if (props.status && props.status.length) {
      const current = (props.status as ModuleStatus).find(item => item.fieldName === fieldName);
      if (current) {
        show.value = current.value;
      }
    }
  });

  const setShow = (newVal: boolean) => {
    if (props.status !== undefined) {
      const index = (props.status as ModuleStatus).findIndex(item => item.fieldName === fieldName);
      if (index !== -1) {
        const newItem = {
          ...props.status[index],
          value: newVal,
        };
        const newStatus = [...props.status];
        newStatus[index] = newItem;
        emits('update:status' as Name, newStatus);
      }
      return;
    }
    show.value = newVal;
  };

  return [readonly(show), setShow] as const;
}
