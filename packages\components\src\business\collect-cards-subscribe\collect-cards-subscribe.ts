import type { CommonSetting } from '@bish/types/src';
import type { PopupSetting } from '../../common/popup.vue';
import type { TextSetting } from '../../ui/text/index.vue';
import type { ImageSetting } from '../../ui/image/index.vue';
import type { ResizableProps } from '../../ui/resizable/index.vue';
import type { CommonBackgroundSetting } from '@bish/types/src';
import type { WeSubscribeModalSetting } from '../we-subscribe-modal';
import type { WxSubscribeGuideSetting } from '../wx-subscribe-guide';
 

export interface CollectCardsSubscribeSetting extends CommonSetting {
  bgImage: string
  /**
   * 按钮配置
   */
  optionBtn: {
    subscribe: ImageSetting
    unsubscribe: ImageSetting
    award: ImageSetting
  }
  /**
   * 奖品配置
   */
  award?: CommonBackgroundSetting,
  /**
   * 内嵌订阅消息中间页配置
   */
  weappEmbedSubscribeSetting?: WeSubscribeModalSetting
  /**
   * 微信小程序订阅提示弹窗
   */
  wxSubscribeModal?: WxSubscribeGuideSetting
  /**
   * 集卡完成获奖弹窗
   */
  modalSetting?: PopupSetting & {
    content?: TextSetting
    table?: TextSetting
  }
} 
