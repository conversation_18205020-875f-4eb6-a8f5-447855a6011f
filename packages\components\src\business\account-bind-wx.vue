<!--
* @Description: 账号绑定微信-弹窗
-->
<template>
  <Popup
    z-index="99"
    :show="show"
    :lock-scroll="false"
    v-model:setting="setting.modalSetting"
    @close="handleClose"
  >
    <!-- 内容文案 -->
    <UiText v-if="setting.modalSetting.content?.content" v-model:setting="setting.modalSetting.content" class="account-bind-wx-content" />

    <!-- 切换账号按钮 -->
    <UiImage
      v-if="setting.modalSetting.switchBtn?.imgLink"
      v-model:setting="setting.modalSetting.switchBtn"
      :confined="false"
      @click="handleLoginOut"
    />

    <!-- 确定绑定按钮 -->
    <UiImage
      v-if="setting.modalSetting.confirmBtn?.imgLink"
      v-model:setting="setting.modalSetting.confirmBtn"
      :confined="false"
      @click="handleBindWx"
    />
  </Popup>
</template>

<script lang="ts">
import type { StatefulComponent } from '@bish/types/src/admin';
import type { TextSetting } from '../ui/text/index.vue';
import type { ImageSetting } from '../ui/image/index.vue';
import type { PopupSetting } from '../common/popup.vue';

export interface AccountBindWxModalSetting extends PopupSetting {
  /**
   * 内容
   */
  content: TextSetting
  /**
   * 切换账号按钮
   */
  switchBtn: ImageSetting
  /**
   * 确定绑定按钮
   */
  confirmBtn: ImageSetting
}

export interface AccountBindWxSetting {
  /**
   * 弹窗
   */
  modalSetting: AccountBindWxModalSetting
}

export interface AccountBindWxProps {
  /**
   * 配置
   */
  setting: AccountBindWxSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): AccountBindWxSetting => {
  return {
    modalSetting: {
      x: 0,
      y: 0,
      width: 375,
      height: 343,
      bgImage: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724925052111_.png',
      content: {
        x: 74,
        y: 122,
        width: 229,
        height: 100,
        fontSize: 12,
        color: '#732508',
        align: 'center',
        content: '单微信/单账号仅能参与活动一次，微信号与游戏账号一旦绑定，即无法解除,活动红包奖励将发放到绑定微信号，请确认是否绑定当前账号、',
      },
      closeBtn: {
        x: 315,
        y: 31,
        width: 19,
        height: 19,
        imgLink: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724917975880_.png',
      },
      switchBtn: {
        x: 43,
        y: 248,
        width: 144,
        height: 43,
        imgLink: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724925058512_.png',
      },
      confirmBtn: {
        x: 187,
        y: 248,
        width: 144,
        height: 43,
        imgLink: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-8-29/1724925061246_.png',
      },
    },
  };
};

export default {
  name: 'account-bind-wx',
}
</script>

<script lang="ts" setup>
import { withDefaults, watch, onMounted } from 'vue';
import { useVModel } from '@vueuse/core';
import { showLoadingToast, showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import useUserStore from '@bish/store/src/modules/user';
import usePopupStore from '@bish/store/src/modules/popup';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { additionalLinkParameters, parseQueryString } from '@bish/utils/src/utils';
import localStorage from '@bish/utils/src/storage/localStorage';
import { bindWxIdentity } from '@bish/api/src/user';
import UiText from '../ui/text/index.vue';
import UiImage from '../ui/image/index.vue';
import Popup from '../common/popup.vue';

const props = withDefaults(defineProps<AccountBindWxProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: AccountBindWxSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityStore = useActivityStore();
const userStore = useUserStore();
const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);

const [show, toggleShow] = useControllableStatus(props, emits, { fieldName: 'showAccountBindWx' });

watch(
  () => popupStore.showAccountBindWx,
  (newVal) => {
    toggleShow(newVal);
  },
);

const queryParams = parseQueryString(window.location.href)

onMounted(() => {
  // 缓存微信参数和邀请码
  if (queryParams.unionid && queryParams.openid) {
    localStorage.setLocalStorage('wx_params', {
      unionid: queryParams.unionid,
      openid: queryParams.openid,
    });
    // 更新当前活动URL
    const redirectUrl = additionalLinkParameters(
      {
        event_page: queryParams.event_page,
      },
      window.location.href.split('?')[0],
    )
    window.location.href = redirectUrl;
  }
});

const handleLoginOut = () => {
  handleClose();
  userStore.resetUserData();
  activityStore.setComponentWithUserInfo({});
  activityStore.setActivityAccountInfo({});
  activityStore._checkIn();
};

const handleBindWx = async () => {
  const wxParams = localStorage.getLocalStorage('wx_params');
  if (wxParams && wxParams.unionid && wxParams.openid) {
    const toast = showLoadingToast({
      duration: 0,
      forbidClick: true,
      message: '绑定中...',
    });
    const params = {
      act_id: activityStore.activityInfo?.init?.id,
      act_acc_id: activityStore.activityAccountInfo?.act_acc_id,
      unionid: wxParams.unionid,
      openid: wxParams.openid,
    };
    try {
      const res = await bindWxIdentity(params);
      toast.close();
      if (res.code === 0) {
        showToast('绑定成功~')
        // 重新获取相关数据
        activityStore.getActivityAccountInfo();
        activityStore.setComponentWithUserInfo({});
      } else {
        showToast(res.message);
      }
      handleClose();
    } catch (error) {
      toast.close();;
      console.warn('sdk账号与微信绑定失败', error);
    }
  } else {
    showToast('缺少微信参数！');
    handleClose();
  }
};

const handleClose = () => {
  if (!popupStore.showAccountBindWx) {
    toggleShow(false);
  }
  popupStore.setShowAccountBindWx(false);
};
</script>

<style lang="less">
.account-bind-wx-content {
  font-weight: 600;
  line-height: 1.6;
}
</style>