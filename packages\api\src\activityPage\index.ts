import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'

const { VITE_ACTIVITY_URL } = useEnv()

export type ActivityPageConfigParams = {
  /**
   * 活动装修页ID
   */
  id: number
}

export type ActivityPageConfigData = {
  /**
   * 关联的活动id
   */
  activity_id: number
  /**
   * 活动装修页id
   */
  id: number
  /**
   * 活动页名称
   */
  name: string
  /**
   * 活动页配置信息，json字符串
   */
  page_config: string
  /**
   * 活动页入口链接
   */
  path: string
  /**
   * 关联的项目id
   */
  project_id: number
  /**
   * 活动页状态
   */
  status: string
  /**
   * 地区类型 1国内(默认) 2海外
   */
  area_type: 1 | 2
}

/**
 * @description 获取活动页配置详情
 * @param {number} params.id 活动装修页id
 * @returns
 */
export function postActivityPageConfig(params: ActivityPageConfigParams) {
  return http.post<ActivityPageConfigData>(`${VITE_ACTIVITY_URL}/frontEndTemplate/activityPageConfig/detail`, params)
}
