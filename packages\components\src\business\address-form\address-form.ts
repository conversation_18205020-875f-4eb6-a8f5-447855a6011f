import type { CommonSetting } from '@bish/types/src';
import type { PopupSetting } from '../../common/popup.vue';

export interface AddressFormSetting {
  /**
   * 弹窗基础配置
   */
  modal: PopupSetting & {
    /**
     * 表单配置
     */
    form: {
      /**
       * 定位大小
       */
      position: CommonSetting
      /**
       * label
       */
      label: {
        /**
         * 颜色
         */
        color: string
      }
    }
  }
}