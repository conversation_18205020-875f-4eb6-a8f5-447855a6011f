import { EffectScope, effectScope, onScopeDispose, ref } from "vue"

function useMouse() {
  const x = ref(0)
  const y = ref(0)

  function handler(e: MouseEvent) {
    x.value = e.pageX
    y.value = e.pageY
  }

  // #ifdef H5
  window.addEventListener('mousemove', handler)

  onScopeDispose(() => {
    window.removeEventListener('mousemove', handler)
  })
  // #endif

  return { x, y }
}

const createSharedMouse = () => {
  let subscribers = 0
  let state: ReturnType<typeof useMouse> | null | undefined
  let scope: EffectScope | null = null
  const dispose = () => {
    if (scope && --subscribers <= 0) {
      scope.stop()
      state = scope = null
    }
  }
  return () => {
    subscribers++
    if (!state) {
      scope = effectScope(true)
      state = scope.run(() => useMouse())
    }
    onScopeDispose(dispose)
    return state!
  }
}
export { useMouse }

const useSharedMouse = createSharedMouse()
export default useSharedMouse
