<template>
  <div
    ref="containerRef"
    :id="cid"
    :style="{
      left: pxTransform(x),
      top: pxTransform(y),
      width: pxTransform(width),
      height: pxTransform(height),
      position,
    }"
    :class="[
      'ui-resizable',
      {
        dragging: moving && !startState.direction,
        'ui-resizable-movable': movable,
        'ui-resizable-immovable': !movable,
        'ui-resizable-resizable': mergeResizable,
        'ui-resizable-active': internalActive,
      }
    ]"
    @mousedown="startMove"
    @click="handleClick"
  >
    <!-- 手柄 -->
    <div class="ui-resizable-handles">
      <div
        v-for="(handle, index) in visibleHandles"
        :key="handle"
        :class="`ui-resizable-handle ui-resizable-handle-${handle}`"
        :style="getHandleStyle(handle, index)"
        @mousedown.stop="startMove($event, handle)"
      />
    </div>

    <!-- 自定义内容 -->
    <slot />

    <!-- 宽高提示 -->
    <!-- #ifdef WEB -->
    <Teleport to="body">
      <div v-if="moving && startState.direction" class="ui-resizable-tooltip" :style="tooltipStyle">
        <div>宽度：{{ width }}</div>
        <div>高度：{{ height }}</div>
      </div>
    </Teleport>
    <!-- #endif -->
  </div>
</template>

<script lang="ts" setup>
import {
  defineProps,
  defineEmits,
  onMounted,
  onUnmounted,
  onBeforeUnmount,
  ref,
  computed,
  watch,
  watchEffect,
  getCurrentInstance,
  inject,
} from 'vue';
import { onClickOutside } from '@vueuse/core';
import { pxTransform } from '@bish/utils/src/viewport';
import useAdminStore from '@bish/store/src/modules/admin';
import useSharedMouse from '@bish/hooks/src/useSharedMouse';
import { getLimitDelta, contextKey, type PositionLimitType } from './config';
import type { NearPositionType, ResizableContainerInjectType } from './resizable-provider.vue'

export type PositionType = 'static' | 'absolute'

export interface ResizableProps {
  /**
   * 水平位置
   */
  x?: number
  /**
   * 垂直位置
   */
  y?: number
  /**
   * 宽度
   */
  width: number
  /**
   * 高度
   */
  height: number
  /**
   * 组件id，唯一标识
   */
  cid?: string
  /**
   * 是否启用的，可用于判断组件是否显示、或者代码逻辑的判断条件
   */
  enabled?: boolean
  /**
   * 是否可改变大小的
   */
  resizable?: ResizeHandles | boolean
  /**
   * 最小宽度
   */
  minWidth?: number
  /**
   * 最小高度
   */
  minHeight?: number
  /**
   * 最大宽度
   */
  maxWidth?: number
  /**
   * 最大高度
   */
  maxHeight?: number
  /**
   * 是否可移动的，默认 true
   */
  movable?: boolean
  /**
   * 是否激活的
   */
  active?: boolean
  /**
   * 是否限制活动的，设置会限制拖动范围只能在父级容器内，默认 true
   */
  confined?: boolean
  /**
   * 是否参与辅助线的计算，如弹窗等是不需要的
   */
  baseline?: boolean
  /**
   * 是否保持长宽比，默认 false
   */
  aspectRatioEnabled?: boolean
  /**
   * 布局方式，同 css position 属性，支持 'static' | 'absolute'
   */
  position?: PositionType
}

export type ResizeHandles = {
  n?: boolean
  e?: boolean
  s?: boolean
  w?: boolean
  nw?: boolean
  ne?: boolean
  sw?: boolean
  se?: boolean
}

export interface ResizeEvent {
  x: number
  y: number
  width: number
  height: number
}

const isWeb = typeof window !== 'undefined';

const props = withDefaults(defineProps<ResizableProps>(), {
  x: 0,
  y: 0,
  width: 0,
  height: 0,
  resizable: () => ({
    n: true,
    e: true,
    s: true,
    w: true,
    nw: true,
    ne: true,
    sw: true,
    se: true,
  }),
  minWidth: 0,
  minHeight: 0,
  maxWidth: 375,
  maxHeight: Infinity,
  movable: true,
  active: undefined,
  confined: true,
  baseline: true,
  aspectRatioEnabled: false,
  position: 'absolute',
});

const emits = defineEmits<{
  (event: 'resize', value: ResizeEvent): void;
  (event: 'update:x', value: number): void;
  (event: 'update:y', value: number): void;
  (event: 'update:width', value: number): void;
  (event: 'update:height', value: number): void;
  (event: 'update:active', value: boolean): void;
  (event: 'click', value: MouseEvent): void;
}>();
const instance = getCurrentInstance() // 拿来当计算辅助线时的key

const resizeContext = inject<ResizableContainerInjectType>(contextKey)

const { x: mouseX, y: mouseY } = useSharedMouse() // 
const moving = ref(false) // 是否正在移动
let startState = {
  x: 0, // 起始x坐标
  y: 0, // 起始y坐标
  mouseX: 0, // 起始鼠标x坐标
  mouseY: 0, // 起始鼠标y坐标
  width: 0, // 起始宽度
  height: 0, // 起始高度
  direction: '', // 操作方向，仅移动时为空字符串
}
let positionLimit: PositionLimitType | undefined; // 移动边界限制，由容器resizeContext提供
let nearPositionConfig: Record<keyof NearPositionType, number[]> | undefined; // 计算辅助线&吸附的点集合，由同一层的自身坐标注册到context后统一下发

const containerRef = ref<HTMLDivElement | undefined>(undefined);

const internalActive = ref(false);

const adminStore = useAdminStore();

const tooltipStyle = computed(() => {
  return {
    top: mouseY.value + 10 + 'px',
    left: mouseX.value + 10 + 'px',
  }
});

const mergeMovable = computed(() => {
  return props.movable && adminStore.editable
})

const mergeResizable = computed(() => {
  return adminStore.editable ? props.resizable : false
})

// 注册到context的坐标点，用于供同层级其他组件计算辅助线
const registerValue = computed(() => ({
  top: props.y,
  middle: props.y + props.height / 2,
  bottom: props.y + props.height,
  left: props.x,
  center: props.x + props.width / 2,
  right: props.x + props.width,
}))
onMounted(() => {
  if (resizeContext && instance && props.baseline) {
    resizeContext.registerValue(instance, registerValue);
  }
})
onBeforeUnmount(() => {
  if (resizeContext && instance) {
    resizeContext.cancelValue(instance);
  }
})
onClickOutside(
  containerRef,
  () => {
    // 外部没有传入 active，或则 active 为 false
    if (!props.active) {
      internalActive.value = false;
    };
  },
);
watch(
  () => props.active,
  (newVal) => {
    if (newVal !== internalActive.value) {
      internalActive.value = !!newVal;
    }
  },
  {
    immediate: true,
  }
)

// 根据响应式的分支切换特性，减少非移动时的副作用执行
watchEffect(() => {
  if (moving.value) {
    const sizeLimit = { maxHeight: props.maxHeight, minHeight: props.minHeight, maxWidth: props.maxWidth, minWidth: props.minWidth };
    // 根据边界，尺寸限制/同级组件靠近吸附等，对移动的距离做偏移计算
    let { delta: deltaX, adsorptionPoint: adsorptionPointX } = getLimitDelta(
      {
        startPoint: startState.x,
        mouseDelta: mouseX.value - startState.mouseX,
        width: startState.width,
        direction: startState.direction.includes('w') ? 'start' : startState.direction.includes('e') ? 'end' : void 0,
      },
      { min: props.minWidth, max: props.maxWidth },
      props.confined ? { min: positionLimit?.left!, max: positionLimit?.right! } : void 0,
      nearPositionConfig ? [...nearPositionConfig.left, ...nearPositionConfig.center, ...nearPositionConfig.right] : []
    );
    let { delta: deltaY, adsorptionPoint: adsorptionPointY } = getLimitDelta(
      {
        startPoint: startState.y,
        mouseDelta: mouseY.value - startState.mouseY,
        width: startState.height,
        direction: startState.direction.includes('n') ? 'start' : startState.direction.includes('s') ? 'end' : void 0,
      },
      { min: props.minHeight, max: props.maxHeight },
      props.confined ? { min: positionLimit?.top!, max: positionLimit?.bottom! } : void 0,
      nearPositionConfig ? [...nearPositionConfig.top, ...nearPositionConfig.middle, ...nearPositionConfig.bottom] : []
    );
    
    // resize和move一体化，区别只在于resize需要同时操作width/height
    let deltaWidth = 0;
    let deltaHeight = 0;

    const { direction } = startState
    // 根据操作的方向计算宽/高偏移
    if (direction.includes('n')) {
      deltaHeight = -deltaY;
    }
    if (direction.includes('s')) {
      deltaHeight = deltaY;
    }
    if (direction.includes('w')) {
      deltaWidth = -deltaX;
    }
    if (direction.includes('e')) {
      deltaWidth = deltaX;
    }
    // resize时，仅左侧的操作需要移动x坐标，其余的体现在宽度变化上
    if (['n', 's'].includes(direction) || direction.includes('e')) {
      deltaX = 0
    }
    // 同理
    if (['w', 'e'].includes(direction) || direction.includes('s')) {
      deltaY = 0
    }

    // 保持长宽比逻辑
    if (props.aspectRatioEnabled && ['nw', 'ne', 'sw', 'se'].includes(direction)) {
      const aspectRatio = Math.round((startState.width / startState.height) * 10000) / 10000;
      
      // 使用原始的移动距离来计算
      const rawDeltaX = mouseX.value - startState.mouseX;
      const rawDeltaY = mouseY.value - startState.mouseY;
      
      if (Math.abs(rawDeltaX) > Math.abs(rawDeltaY)) {
        // 以水平移动为准
        deltaWidth = direction.includes('w') ? -rawDeltaX : rawDeltaX;
        // 计算新的宽度，并应用最小/最大限制
        let newWidth = Math.round(startState.width + deltaWidth);
        if (newWidth < props.minWidth) {
          newWidth = props.minWidth;
          deltaWidth = newWidth - startState.width;
        }
        if (newWidth > props.maxWidth) {
          newWidth = props.maxWidth;
          deltaWidth = newWidth - startState.width;
        }
        
        // 根据限制后的宽度计算高度
        deltaHeight = deltaWidth / aspectRatio;
        if (direction.includes('n')) {
          deltaY = -deltaHeight;
        }
      } else {
        // 以垂直移动为准
        deltaHeight = direction.includes('n') ? -rawDeltaY : rawDeltaY;
        // 计算新的高度，并应用最小/最大限制
        let newHeight = Math.round(startState.height + deltaHeight);
        if (newHeight < props.minHeight) {
          newHeight = props.minHeight;
          deltaHeight = newHeight - startState.height;
        }
        if (newHeight > props.maxHeight) {
          newHeight = props.maxHeight;
          deltaHeight = newHeight - startState.height;
        }
        
        // 根据限制后的高度计算宽度
        deltaWidth = deltaHeight * aspectRatio;
        if (direction.includes('w')) {
          deltaX = -deltaWidth;
        }
      }
    }

    // 移除这里的最小/最大尺寸限制，因为我们已经在上面处理过了
    const newWidth = Math.round(startState.width + deltaWidth);
    const newHeight = Math.round(startState.height + deltaHeight);

    // 计算最终位置
    const newX = startState.x + deltaX;
    const newY = startState.y + deltaY;

    // 上报计算的辅助线
    if (typeof adsorptionPointX === 'number' && !['n', 's'].includes(direction)) {
      resizeContext?.registerBaseline('x', adsorptionPointX);
    } else {
      resizeContext?.cancelBaseline('x');
    }
    if (typeof adsorptionPointY === 'number' && !['e', 'w'].includes(direction)) {
      resizeContext?.registerBaseline('y', adsorptionPointY);
    } else {
      resizeContext?.cancelBaseline('y');
    }

    const resizeEvent: ResizeEvent = {
      x: newX,
      y: newY,
      width: newWidth,
      height: newHeight,
    }

    emits('resize', resizeEvent);
    emits('update:x', newX);
    emits('update:y', newY);
    emits('update:width', newWidth);
    emits('update:height', newHeight);

    if (deltaX !== 0) {
      // 拖拽时禁止点击
      setPointerEvents('none');
    }
  }
})

// #ifdef H5
onMounted(() => {
  document.addEventListener('mouseup', endMove)
})
onUnmounted(() => {
  document.removeEventListener('mouseup', endMove)
})
// #endif
function endMove() {
  startState = {
    x: 0,
    y: 0,
    mouseX: 0,
    mouseY: 0,
    width: props.width,
    height: props.height,
    direction: '',
  }
  positionLimit = void 0;
  nearPositionConfig = void 0;
  moving.value = false;

  resizeContext?.cancelBaseline('y');
  resizeContext?.cancelBaseline('x');

  // 恢复点击
  setPointerEvents('auto')
}
function startMove(event: MouseEvent, dir = '') {
  if (
    props.position === 'static' && !dir ||
    !mergeMovable.value && !dir
  ) {
    return
  }
  // 嵌套场景，阻止父级容器的拖拽事件
  event.stopPropagation();

  if (dir) {
    event.preventDefault();
  }
  
  // 这些属性在移动时都是一样的，仅在开始时计算一次
  startState = {
    mouseX: event.pageX,
    mouseY: event.pageY,
    x: props.x,
    y: props.y,
    width: props.width,
    height: props.height,
    direction: dir,
  }
  positionLimit = resizeContext?.getContainerPositionLimit();
  nearPositionConfig = getNearConfigList(positionLimit);
  moving.value = true;
}

function getNearConfigList(containerConfig?: PositionLimitType) {
  const res: Record<keyof NearPositionType, number[]> = {
    top: [],
    middle: [],
    bottom: [],
    left: [],
    center: [],
    right: [],
  };
  if (containerConfig) {
    res.top.push(containerConfig.top);
    res.middle.push(containerConfig.top + (containerConfig.bottom - containerConfig.top) / 2);
    res.bottom.push(containerConfig.bottom);
    res.left.push(containerConfig.left);
    res.center.push(containerConfig.left + (containerConfig.right - containerConfig.left) / 2);
    res.right.push(containerConfig.right);
  }

  resizeContext?.childMap.forEach((item) => {
    if (item !== registerValue) {
      Object.keys(item.value).forEach((key) => {
        res[key as keyof NearPositionType].push(item.value[key as keyof NearPositionType]);
      });
    }
  });
  return res;
}
function setPointerEvents(value: 'auto' | 'none') {
  if (containerRef.value) {
    containerRef.value.style.pointerEvents = value;
  }
}

function handleClick(e: MouseEvent) {
  if (mergeResizable.value) {
    internalActive.value = true;
  }
  emits('update:active', true);
  emits('click', e)
}

// 计算可见的手柄
const visibleHandles = computed(() => {
  if (!mergeResizable.value) return [];
  
  const handles = ['n', 'e', 's', 'w', 'nw', 'ne', 'sw', 'se'];
  return handles.filter(handle => {
    // 添加类型断言，告诉 TypeScript handle 是 ResizeHandles 的有效键
    return (props.resizable as ResizeHandles)[handle as keyof ResizeHandles];
  });
});

// 获取手柄样式
const getHandleStyle = (handle: string, index: number) => {
  // 基础偏移量，每个手柄偏移6px
  const baseOffset = 3;
  
  let top = 0;
  let left = 0;
  
  // 根据手柄类型计算位置
  switch (handle) {
    case 'n':
      left = props.width / 2 - baseOffset;
      top = -baseOffset;
      break;
    case 'e':
      left = props.width - baseOffset;
      top = props.height / 2 - baseOffset;
      break;
    case 's':
      left = props.width / 2 - baseOffset;
      top = props.height - baseOffset;
      break;
    case 'w':
      left = -baseOffset;
      top = props.height / 2 - baseOffset;
      break;
    case 'nw':
      left = -baseOffset;
      top = -baseOffset;
      break;
    case 'ne':
      left = props.width - baseOffset;
      top = -baseOffset;
      break;
    case 'sw':
      left = -baseOffset;
      top = props.height - baseOffset;
      break;
    case 'se':
      left = props.width - baseOffset;
      top = props.height - baseOffset;
      break;
  }
  
  return {
    top: `${top}px`,
    left: `${left}px`,
  };
};
</script>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ui-resizable',
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  }
});
</script>

<style>
.ui-resizable {
  position: relative;
  user-select: none;
}

.ui-resizable-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.ui-resizable-resizable:hover > .ui-resizable-handles,
.ui-resizable-active > .ui-resizable-handles {
  display: block;
}

.ui-resizable-resizable:hover,
.ui-resizable-active {
  outline: 1px solid #ccc;
  outline-offset: 1px;
}

.ui-resizable img {
  user-drag: none;
  user-select: none;
}

.ui-resizable-handles {
  position: relative;
  display: none;
}

.ui-resizable-handle {
  background-color: #ffffff;
  border: 1px solid #ccc;
  width: 6px;
  height: 6px;
  position: absolute;
}

.ui-resizable-handle-n {
  cursor: ns-resize;
}

.ui-resizable-handle-s {
  cursor: ns-resize;
}

.ui-resizable-handle-w {
  cursor: ew-resize;
}

.ui-resizable-handle-e {
  cursor: ew-resize;
}

.ui-resizable-handle-nw {
  cursor: nwse-resize;
}

.ui-resizable-handle-ne {
  cursor: nesw-resize;
}

.ui-resizable-handle-sw {
  cursor: nesw-resize;
}

.ui-resizable-handle-se {
  cursor: nwse-resize;
}

.ui-resizable.dragging {
  cursor: move;
}

.ui-resizable-movable {
  position: absolute;
}

.ui-resizable-immovable {
  position: relative !important;
}

.ui-resizable-tooltip {
  position: fixed;
  z-index: 99999;
  padding: 3px 5px;
  border: 1px solid #F5E4B9;
  font-size: 12px;
  background-color: #FFFAEE;
}
</style>
