import type { CommonSetting, CommonImageSetting } from '@bish/types/src';
import type { MilestoneSetting } from '../../../common/milestone/index.vue';
import type { TextSetting } from '../../../ui/text/index.vue'

export interface TaskProgressSetting extends MilestoneSetting {
  /**
   * 标记配置
   */
  mark: {
    /**
     * 道具配置
     */
    item: CommonSetting
    /**
     * 文本容器配置
     */
    content: CommonSetting
    /**
     * 节点名称配置
     */
    name: TextSetting
  }
  /**
   * 列表项
   */
  marks: {
    /**
     * 节点名称
     */
    name: string
    /**
     * 节点任务ID配置
     */
    taskId: number
    /**
     * 节点道具
     */
    item: string
  }[]
}