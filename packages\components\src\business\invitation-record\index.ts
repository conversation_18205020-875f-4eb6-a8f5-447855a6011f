import { defaultConfig as recordsDefaultConfig } from '../records/records.vue';
import type { InvitationRecordSetting } from './invitation-record';

export * from './invitation-record';

export const defaultConfig = (): InvitationRecordSetting => {
  const recordsDefault = recordsDefaultConfig();
  return {
    ...recordsDefault,
    x: 0,
    y: 0,
    width: 140,
    height: 60,
    bgImage: '',
    text: {
      x: 20,
      y: 20,
      width: 100,
      height: 20,
      fontSize: 14,
      color: '#1887D3',
      align: 'center',
      alignItems: 'center',
      content: '邀请记录',
      textDecoration: 'underline',
    },
    modalSetting: {
      x: 0,
      y: 0,
      width: 375,
      height: 392,
      bgImage: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/********/1729163184473-%E7%BB%84%208.png',
      okBtn: {
        width: 0,
        height: 0,
      },
      table: {
        x: 55,
        y: 85,
        width: 266,
        height: 280,
        style: 'simple',
        radius: 0,
        border: {
          width: 1,
          color: '#A27750',
        },
        head: { 
          color: '#72450D',
        },
        body: {
          color: '#72450D',
        },
      },
      closeBtn: {
        x: 326,
        y: 1,
        width: 34,
        height: 34,
        imgLink: 'https://bish-**********.cos.ap-shanghai.myqcloud.com/temp/********/*************-%E7%BB%84%203.png',
      },
    },
    columns: [
      {
        title: '角色名',
        dataIndex: 'invited_account_id',
      },
      {
        title: '邀请时间',
        dataIndex: 'created_at',
      },
      {
        title: '邀请类型',
        dataIndex: 'invited_account_type',
      },
    ],
  }
};