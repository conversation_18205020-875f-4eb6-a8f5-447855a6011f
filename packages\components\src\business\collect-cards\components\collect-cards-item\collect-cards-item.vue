<!--
* @Description: 集卡-卡组项
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :movable="false"
    :style="itemStyle"
    class="collect-cards-item"
  >
    <ResizableProvider>
      <!-- 卡片 -->
      <Resizable
        v-model:x="setting.card.x"
        v-model:y="setting.card.y"
        v-model:width="setting.card.width"
        v-model:height="setting.card.height"
        :style="{
          backgroundImage: `url(${props.data.icon})`,
        }"
        class="collect-cards-item-card"
      />
      <!-- 已刮奖 -->
      <UiImage
        v-if="setting.scratched?.enabled && scratched && !isGift"
        :setting="setting.scratched"
        :confined="false"
      />
      <!-- 好友赠送 -->
      <UiImage
        v-if="setting.gift?.enabled && isGift"
        :setting="setting.gift"
        :confined="false"
      />
      <!-- 刮奖 -->
      <UiImage
        v-if="setting.scratch?.enabled && scratchable"
        :setting="setting.scratch"
        :confined="false"
        @click="handleScratch"
      />

      <!-- 遮罩 -->
      <div
        v-if="!active"
        :style="maskStyle"
        class="collect-cards-item-mask"
      />

      <!-- 未解锁 -->
      <div
        v-if="locked"
        :style="maskStyle"
        class="collect-cards-item-mosaic"
      >
        <div class="collect-cards-item-locked">
          <img :src="lock_icon" class="collect-cards-item-locked-icon" alt="lock" />
          <div v-if="showLockedTxt" class="collect-cards-item-locked-txt">
            尚未获得
          </div>
        </div>
      </div>
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'collect-cards-item',
}
</script>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import type { CSSProperties } from 'vue';
import { useToggle, useVModel } from '@vueuse/core';
import { showToast, showLoadingToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { pxTransform } from '@bish/utils/src/viewport';
// import { useLog } from '@bish/hooks/src/useLog';
import { postActivityTaskGetAwardTask } from '@bish/api/src/activity';
import type { ActivityInfoConfigTask, ActivityInfoConfigCollectDrawDetail } from '@bish/api/src/activity';
import Resizable from '../../../../ui/resizable/index.vue';
import ResizableProvider from '../../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../../ui/image/index.vue';
import UiText from '../../../../ui/text/index.vue';
import Popup from '../../../../common/popup.vue';
import type { CollectCardsItemSetting } from './collect-cards-item';
import { defaultConfig } from './index';
import lock_icon from './images/lock.png';

export type CollectCardsItemData = ActivityInfoConfigCollectDrawDetail[number]

export interface CollectCardsItemProps {
  setting: CollectCardsItemSetting
  /**
   * 是否激活的
   */
  active?: boolean
  /**
   * 是否锁定
   */
  locked?: boolean
  /**
   * 是否可刮奖
   */
  scratchable?: boolean
  /**
   * 是否已刮奖
   */
  scratched?: boolean
  /**
   * 是否是好友赠送
   */
  isGift?: boolean
  /**
   * 是否显示锁定文本
   */
  showLockedTxt?: boolean
  /**
   * 卡片数据
   */
  data: CollectCardsItemData
}

const props = withDefaults(defineProps<CollectCardsItemProps>(), {
  setting: () => defaultConfig(),
  active: true,
  locked: false,
  scratchable: false,
  scratched: false,
  isGift: false,
  showLockedTxt: true,
  data: () => ({
    id: 0,
    name: '',
    icon: '',
    is_limit_card: 0,
  } as CollectCardsItemData),
});

const emits = defineEmits<{
  (event: 'update:setting', value: CollectCardsItemSetting): void;
  (event: 'scratch', value: any): void;
}>();

const setting = useVModel(props, 'setting', emits);

const activityStore = useActivityStore();
const adminStore = useAdminStore();

// const { uploadLog } = useLog();

const itemStyle = computed<CSSProperties>(() => {
  return {
    backgroundImage: `url(${
      props.active && !props.locked
        ? setting.value.bgImage
        : setting.value.inactiveBgImage
    })`,
  };
});

const maskStyle = computed<CSSProperties>(() => {
  return {
    top: pxTransform(setting.value.card.y!),
    left: pxTransform(setting.value.card.x!),
    width: pxTransform(setting.value.card.width),
    height: pxTransform(setting.value.card.height),
  };
});

const handleScratch = () => {
  // TODO: 派发刮奖事件，回调当前卡片数据
  emits('scratch', {});
}
</script>

<style>
.collect-cards-item,
.collect-cards-item-card {
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.collect-cards-item-mask {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.65);
}

.collect-cards-item-mosaic {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

@supports (background: -moz-element(#bg)) {
  .collect-cards-item-mosaic {
    background: -moz-element(#bg) no-repeat;
    filter: blur(4px);
  }
}

.collect-cards-item-locked {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.collect-cards-item-locked-icon {
  width: 22px;
  height: 22px;
}

.collect-cards-item-locked-txt {
  margin-top: 4px;
  color: #FFFFFF;
  font-weight: 500;
}
</style> 
