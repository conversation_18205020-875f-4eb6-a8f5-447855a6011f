import type { CommonBackgroundSetting, CommonSetting } from '@bish/types/src';
import type { ImageSetting } from '../../../ui/image/index.vue';
import type { TextSetting } from '../../../ui/text/index.vue';
import type { ItemsSetting } from '../../../business/items.vue';
import type { ServerRolePickerSetting } from '../../../business/server-role-picker.vue';
import type { DownloadGameSetting as DownloadGameBtnSetting } from '../../../business/download-game.vue';

export type PointsGiftPackStratifyItems = {
  /**
   * 礼包 id
   */
  gifPackId: number
  /**
   * 道具
   */
  items: {
    /**
     * 道具图片
     */
    imgLink: string
    /**
     * 道具名称
     */
    name: string
  }[]
}[]

export interface PointsGiftPackStratifySetting extends CommonBackgroundSetting {
  /**
   * 道具列表
   */
  list: CommonBackgroundSetting
  /**
   * 道具列表项
   */
  item: CommonSetting & {
    /**
     * 道具图片
     */
    itemImg: CommonSetting
    /**
     * 道具名称
     */
    name: TextSetting
    /**
     * 背景
     */
    bgImage: string
  }
  /**
   * 道具展示
   */
  items: PointsGiftPackStratifyItems
  /**
   * 区服-角色选择器
   */
  serverRolePicker: ServerRolePickerSetting
  /**
   * 立即领取按钮
   */
  receiveBtnImage: ImageSetting
  /**
   * 已领取按钮
   */
  receivedBtnImage: ImageSetting
  /**
   * 下载游戏按钮
   */
  downloadBtn: DownloadGameBtnSetting
  /**
   * 是否需要绑定角色，默认 0，若是则使用活动全局绑定角色组件进行绑角操作
   */
  bindRole: number
}