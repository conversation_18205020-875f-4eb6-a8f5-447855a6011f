{"name": "@bish/request", "version": "0.0.26", "description": "", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "exports": {".": {"import": "./src/index.ts", "require": "./src/index.ts"}, "./*": "./*"}, "main": "./src/index.ts", "module": "./src/index.ts", "files": ["src"], "keywords": [], "author": "", "license": "ISC", "dependencies": {"@bish/hooks": "workspace:^", "@bish/lang": "workspace:^", "@bish/store": "workspace:^", "@bish/utils": "workspace:^", "axios": "^1.5.0"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@vue/tsconfig": "^0.1.3"}, "peerDependencies": {"vant": "4.9.3", "vue-router": "4.3.3"}, "publishConfig": {"registry": "http://sy-registry.shiyue.com"}}