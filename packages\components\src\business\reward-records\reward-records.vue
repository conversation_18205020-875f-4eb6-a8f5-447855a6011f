<!--
* @Description: 我的奖励-表格样式
-->
<template>
  <Records
    v-model:setting="setting"
    :open="open"
    :columns="mergedColumns"
    :dataSource="dataSource"
    @open="() => handleShow()"
    @close="() => toggle(false)"
  >
    <template #bodyCell="{ column, record }">
      <!-- 发放状态 -->
      <template v-if="column.dataIndex === 'send_status'">
        {{ statusName(record.item_type, record.send_status) }}
      </template>
      <!-- 类型 -->
      <template v-if="column.dataIndex === 'item_type'">
        {{ typeName(record.item_type) }}
      </template>
    </template>

    <!-- 填写地址入口 -->
    <template #popupExtra>
      <template v-if="hasPhysicalItem">
        <UiImage
          v-if="hasPhysical"
          v-model:setting="setting.address"
          @click="handleShowAddressForm"
        />
      </template>
      <template v-if="hasRedPacketItem">
        <UiImage
          v-if="hasRedPacket"
          v-model:setting="setting.wallet"
          @click="handleShowWallet"
        />
      </template>
    </template>
  </Records>
</template>

<script lang="ts">
import type { RecordsSetting, Column } from '../records/records.vue';
import { defaultConfig as recordsDefaultConfig } from '../records/records.vue';
import type { ImageSetting } from '../../ui/image/index.vue';

export interface RewardRecordsSetting extends RecordsSetting {
  /**
   * 表格列
   */
  columns: Column[]
  /**
   * 填写地址
   */
  address: ImageSetting
  /**
   * 我的钱包
   */
  wallet: ImageSetting
}

export interface RewardRecordsProps {
  /**
   * 配置
   */
  setting: RewardRecordsSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): RewardRecordsSetting => {
  const recordsDefault = recordsDefaultConfig();
  return {
    ...recordsDefault,
    x: 0,
    y: 0,
    width: 33,
    height: 105,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713156187911_.png',
    modalSetting: {
      x: 0,
      y: 0,
      width: 348,
      height: 354,
      bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713156213604_.png',
      okBtn: {
        width: 0,
        height: 0,
      },
      table: {
        x: 60,
        y: 77,
        width: 229,
        height: 219,
        style: 'simple',
        radius: 0,
        border: {
          width: 1,
          color: '#5E7E94',
        },
        head: {
          color: '#5E7E94',
        },
        body: {
          color: '#5E7E94',
        },
      },
      closeBtn: {
        x: 302,
        y: 26,
        width: 21,
        height: 20,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713156222085_.png',
      },
    },
    columns: [
      {
        title: '奖励内容',
        dataIndex: 'item_name',
      },
      {
        title: '获取时间',
        dataIndex: 'created_at',
      },
      // {
      //   title: '获奖角色',
      //   dataIndex: 'role_meta.role_name',
      //   width: '27%',
      // },
      // {
      //   title: '发放状态',
      //   dataIndex: 'send_status',
      //   width: '27%',
      // },
      // {
      //   title: '类型',
      //   dataIndex: 'item_type',
      //   width: '27%',
      // },
    ],
    address: {
      x: 147,
      y: 305,
      width: 56,
      height: 12,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241203/1733206502213-address.png',
    },
    wallet: {
      x: 47,
      y: 305,
      width: 56,
      height: 12,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250224/1740400275167-withdraw-btn.png',
    },
  }
};

export default {
  name: 'reward-records',
}
</script>

<script lang="ts" setup>
import { computed } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import useUserStore from '@bish/store/src/modules/user';
import useAdminStore from '@bish/store/src/modules/admin';
import usePopupStore from '@bish/store/src/modules/popup';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import type { StatefulComponent } from '@bish/types/src/admin';
import { PRIZE_TYPE_PHYSICAL, PRIZE_TYPE_RED_PACKET } from '../../__constants/prize';
import Records from '../records/records.vue';
import UiImage from '../../ui/image/index.vue';

const props = withDefaults(defineProps<RewardRecordsProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: RecordsSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const userStore = useUserStore();
const activityStore = useActivityStore();
const adminStore = useAdminStore();
const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);
const [open, toggle] = useControllableStatus(props, emits, { fieldName: 'showRewardRecords' });

const dataSource = computed(() => {
  if (adminStore.editable) {
    return [
      {
        item_name: '道具1',
        created_at: '2024.09.09 16:01:33',
        role_meta: {
          role_name: '角色1',
        },
        send_status: 1,
        item_type: 2,
      },
      {
        item_name: '道具2',
        created_at: '2024.09.09 16:01:33',
        role_meta: {
          role_name: '角色1',
        },
        send_status: 2,
        item_type: 3,
      },
      {
        item_name: '道具3',
        created_at: '2024.09.09 16:01:33',
        role_meta: {
          role_name: '角色1',
        },
        send_status: 3,
        item_type: 1,
      },
      {
        item_name: '红包',
        created_at: '2024.09.09 16:01:33',
        role_meta: {
          role_name: '角色1',
        },
        send_status: 3,
        item_type: 5,
      },
    ];
  }
  return activityStore.componentWithUserInfo?.award_list || [];
});

const mergedColumns = computed(() => {
  return props.setting.columns.map(column => {
    return {
      ...column,
      // ellipsis: column.dataIndex === 'created_at',
      // width: column.dataIndex === 'created_at' ? undefined : '27%',
    };
  })
});

const userAddress = computed(() => {
  return activityStore.activityAccountInfo?.extra?.address_info || null;
});

const hasPhysical = computed(() => {
  return setting.value.address?.enabled;
});

const hasPhysicalItem = computed(() => {
  return dataSource.value.find(item => item.item_type === PRIZE_TYPE_PHYSICAL);
});

const hasRedPacket = computed(() => {
  return setting.value.wallet?.enabled;
});

const hasRedPacketItem = computed(() => {
  return dataSource.value.find(item => item.item_type === PRIZE_TYPE_RED_PACKET);
});

const typeName = (type: number) => {
  let text = '';
  switch (type) {
    case 1:
      text = '实物';
      break;
    case 2:;
      text = '游戏道具';
      break;
    case 3:
      text = '序列号';
      break;
    case 4:
      text = '积分';
      break;
    case 5:
      text = '现金';
      break;
  }
  return text;
};

const statusName = (type: number, status: number) => {
  let text = '';
  switch (status) {
    case 1:
      text = type === 1 && !userAddress.value ? '填写地址' : '发放中';
      break;
    case 2:
      text = '已发放';
      break;
    default:
      text = '发放失败';
      break;
  }
  if (type === 5) {
    text = '前往钱包查看';
  }
  return text;
};

const handleShow = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return
  }

  if (!userStore.isLogined) {
    popupStore.setShowLoginModal(true)
    userStore.scheduler.add(() => {
      toggle(true)
      return Promise.resolve()
    })
  } else {
    toggle(true)
  }
}

const handleShowAddressForm = () => {
  popupStore.setShowAddressForm(true);
};

const handleShowWallet = () => {
  toggle(false);
  popupStore.setShowWallet(true);
};

</script>

<style lang="less">
.reward-records {}
</style>
