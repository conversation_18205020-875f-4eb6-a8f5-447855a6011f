<!--
* @Description: 邀请流失好友
-->
<template>
  <Popup z-index="10" :show="show" :lock-scroll="false" v-model:setting="setting" @close="handleClose">
    <!-- 表格 -->
    <Resizable class="friends-table-container" v-model:x="setting.table.x" v-model:y="setting.table.y"
      v-model:width="setting.table.width" v-model:height="setting.table.height">
      <div class="friends-container">
        <div class="friends-item" v-for="item in friendsList" :key="item.id" :style="friendsItemStyle">
          <div class="friends-item-name">
            {{ item.friend_info?.role_name }}
          </div>
          <img :src="setting.copyLinkBtn.imgLink" :style="copyLinkBtnStyle" @click="handleInvite(item)" />
        </div>
        <div class="friends-item-empty" v-if="friendsList.length === 0">
          <div class="friends-item-empty-text" :style="friendsItemEmptyTextStyle">
            暂无流失好友~
          </div>
        </div>
      </div>
    </Resizable>
  </Popup>
</template>

<script lang="ts">
import type { CSSProperties } from 'vue';
import type { TextSetting } from '../../../../../ui/text/index.vue';
import type { ImageSetting } from '../../../../../ui/image/index.vue';
import type { PopupSetting } from '../../../../../common/popup.vue';
import type { CommonSetting } from '@bish/types/src/index';
import type { lossFriendsModalSetting } from '../invite-loss-friends';
import { lossFriendsModalDefaultConfig } from '../index';
import UiImage from '../../../../../ui/image/index.vue';

export interface LossFriendsModalProps {
  /**
   * 配置
   */
  setting: lossFriendsModalSetting
  /**
   * 是否显示
   */
  show: boolean
  /**
   * 好友列表
   */
  friendsList: any[]
}

export const defaultConfig = lossFriendsModalDefaultConfig()

export default {
  name: 'loss-friends-modal',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useAdminStore from '@bish/store/src/modules/admin';
import { pxTransform } from '@bish/utils/src/viewport';
import { get } from '@bish/utils/src/utils';
import Resizable from '../../../../../ui/resizable/index.vue';
import Popup from '../../../../../common/popup.vue';

const props = withDefaults(defineProps<LossFriendsModalProps>(), {
  setting: defaultConfig,
  show: false,
  friendsList: [],
});

const emits = defineEmits<{
  (event: 'update:setting', value: lossFriendsModalSetting): void;
  (event: 'close', e: Event): void;
  (event: 'handleInvite', item: any): void;
}>();

const activityPageStore = useActivityPageStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const borderStyle = computed<CSSProperties>(() => {
  return {
    borderWidth: pxTransform(setting.value.table.border.width),
    borderColor: setting.value.table.border.color,
  };
});

const copyLinkBtnStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(setting.value.copyLinkBtn.width),
    height: pxTransform(setting.value.copyLinkBtn.height),
  };
});

const friendsItemStyle = computed<CSSProperties>(() => {
  return {
    color: setting.value.table.body.color,
    fontSize: pxTransform(setting.value.table.body.fontSize),
    fontWeight: setting.value.table.body.fontWeight ? 'bold' : 'normal',
    marginRight: pxTransform(setting.value.table.body.marginRight),
    borderBottom: `${setting.value.table.border.width}px solid ${setting.value.table.border.color}`,
    padding: `${pxTransform(7)}`,
  };
});

const friendsItemEmptyTextStyle = computed<CSSProperties>(() => {
  return {
    position: 'absolute',
    color: setting.value.table.body.color,
    fontSize: pxTransform(16),
    top: pxTransform(100),
  };
});

const handleInvite = (item: any) => {
  emits('handleInvite', item);
};

const handleClose = (e: Event) => {
  emits('close', e);
};
</script>

<style lang="less">
@friends-prefix: friends;

.@{friends-prefix} {
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-table-container {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    .friends-container {
      overflow-y: auto;
      .friends-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .friends-item-empty {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>