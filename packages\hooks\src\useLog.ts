/*
 * @Description: 事件上报hooks
 */
import crypto from 'crypto-js'
import useActivityStore from '@bish/store/src/modules/activity'
import useAdminStore from '@bish/store/src/modules/admin'
import loadScript from '@bish/utils/src/loadScript'
import { useEnv } from '@bish/hooks/src/useEnv'
import localStorage from '@bish/utils/src/storage/localStorage'
import { bishInst } from '@bish/core/src/models/Bish'

export type LogConfig = {
  key: string
  secret: string
  apiUrl: string
}

export type UpLoadData = {
  /**
   * 事件名称
   */
  event_name?: string
  /**
   * 事件发生时间
   */
  event_time?: string
  /**
   * 浏览器指纹ID
   */
  distinct_id?: string
  /**
   * 用户账号ID
   */
  account_id?: string
  /**
   * 上报所属项目ID，和后端商定
   */
  project_id?: number
  /**
   * 所属游戏ID
   */
  game_id?: number
  /**
   * 系统来源 1：浏览器、2：公众号:、3：游戏客户端 4:服务端 5：小程序 6：微社区 7：平台社区小程序
   */
  system_source?: number
  /**
   * 客户端版本号:1.0.0 共创1.0
   */
  app_version?: string
  /**
   * 当前场景ID
   */
  scene_id?: string
  /**
   * 进入页面前场景ID
   */
  referrer_scene_id?: string
  /**
   * 进入页面前的场景标识
   */
  referrer_scene_url?: string
  /**
   * 用户当前IP地址
   */
  ip?: string
  /**
   * 设备标示
   */
  browser?: string
  /**
   * 浏览器版本,例如 Chrome 45
   */
  browser_version?: string
  /**
   * 最近一次打开产品的来源如推送，浏览器
   */
  latest_active_source?: string
  /**
   * 代表用户是否为新用户	后端	1：新用户，2：老用户，0：为游客
   */
  is_new_user?: number
  /**
   * 操作系统	os_type	否	字符串	操作系统类型：（现在属于M端是123和默认值）1:安卓 2:ios  3:SDkwin系统; 4:pc；5:小程序 6：微社区 7：平台社区小程序
   */
  os_type?: string
  /**
   * 点击类型 1:图片;2:轮播图;3:按钮;4:链接;5: 社区公告 6: banner 7:金刚区 8: 社区弹窗 9:合集模块 10:合集内容
   */
  click_type?: number
  /**
   * 页面路径
   */
  current_page_url?: string
  /**
   * 页面名称
   */
  current_page_title?: string
  /**
   * 点击事件唯一ID，和后端商定
   */
  click_id?: number 
  /**
   * 帖子id
   */
  post_id?: string
  /**
   * 帖子作者
   */
  post_owner?: string
  /**
   * 通用额外字段，json保存数剧
   */
  extra?: any
  /**
   * 页面停留时间
   */
  duration?: number
  /**
   * 页面深度
   */
  depth?: number
  /**
   * 订阅消息上报id
   */
  task_id?: number
  /**
   * 唯一id
   */
  unique_id?: number
  /**
   * 活动id
   */
  activity_id: number
  /**
   * 活动账号id
   */
  activity_account_id: string
  /**
   * 被曝光的资源类型
   */
  exposure_type?: number
  /**
   * 渠道id，对应链接上的 c 参数
   */
  channel_id?: number
  /**
   * 用户所在人群id
   */
  extra_app_id?: number
  /**
   * 曝光资源id，1：积分礼包
   */
  exposure_id?: number
  /**
   * 活动类型
   */
  type?: number
  /**
   * 活动创建人
   */
  partner?: string
}

/**
 * 生成52位随机字符串
 */
function randomString(e: number) {
  e = e || 52
  const t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
    a = t.length

  let n = ''
  for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
  return n
}

export const initWeappDistinctId = (): string =>  {
  const distinctPrefix = bishInst.weapp.distinctPrefix
  let distinctId = localStorage.getLocalStorage(`__weappDistinctId__`)

  if (distinctId && new RegExp(`${distinctPrefix}`).test(distinctId)) {
    // 已存在指纹id
    return distinctId
  }
  distinctId = `${bishInst.weapp.distinctPrefix}${randomString(52)}`
  uni.setStorageSync('__weappDistinctId__', distinctId)
  return distinctId
};

function SY_WEAPP_log({
  config,
  body,
}: {
  config: LogConfig
  body: Record<string, any>
}) {
  const key = config.key
  const secret = config.secret
  const ts = (Math.round(+new Date() / 1000) - 2).toString() // -2: 时间跟服务端相差几毫秒，接口报错ts不在有效期
  const nonce = randomString(8)
  const data = {
    body,
    sign: crypto.MD5(`${secret}${key}${ts}${nonce}${secret}`).toString(),
    ts: (Math.round(+new Date() / 1000) - 2).toString(),
    key,
    nonce,
  }

  return new Promise((resolve, reject) => {
    uni.request({
      url: config.apiUrl,
      method: 'POST',
      data,
      header: {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/json; charset=UTF-8',
      },
      success: (res => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(res.data)
        }
      }),
      fail: (err => {
        console.error('log fail:', err)
        reject(err);
      }),
    });
  }).catch((error) => {
    console.log(error)
  })
}

export function useLog() {
  const activityStore = useActivityStore()
  const adminStore = useAdminStore()

  const { VITE_APP_TYPE } = useEnv()

  const isWeapp = VITE_APP_TYPE === 'wx'

  /**
   * @description: 获取浏览器指纹
   * @return {*}
   */
  const createVisitorId = async (cb: (visitorId: string) => void) => {
    if (isWeapp) {
      cb(initWeappDistinctId())
      return
    }
    // @ts-expect-error
    const fpPromise = import('@bish/utils/src/fp').then((FingerprintJS: any) => FingerprintJS.load())
    // Get the visitor identifier when you need it.
    fpPromise
      .then((fp) => fp.get())
      .then((result) => {
        // This is the visitor identifier:
        cb(result.visitorId);
      })
  }

  const uploadLog = (extraBody: Partial<UpLoadData>) => {
    // 后台端无须上报
    if (adminStore.editable) {
      return
    }

    const logOptions = (visitorId: string) => {
      // 业务参数：具体每一项上报的参数
      const body: UpLoadData = {
        project_id: 5, // 代表数据项目ID，系统唯一，如1:社区:、2:积分商城、3:游戏客户端，4:用户体系5:活动通用，6:web支付
        game_id: activityStore.activityInfo?.init?.project_id, // 游戏ID 11闪烁 107长安
        activity_id: activityStore.activityInfo?.init?.id, // 活动ID(act_id)
        referrer_scene_id: '1', // 第几个tab页
        distinct_id: localStorage.getLocalStorage('SY_LOG_DATA_distinct_id') || visitorId || '',
        activity_account_id: `${activityStore.activityAccountInfo?.account_id || ''}`,
        account_id: `${activityStore.activityAccountInfo?.account_id ? +activityStore.activityAccountInfo?.account_id : 0}`,
        extra_app_id: activityStore.activityAccountInfo?.user_group_id || 0,
        event_time: Math.round(Number(new Date()) / 1000).toString(),
        system_source: bishInst.weapp.systemSource,
        os_type: bishInst.weapp.systemType,
        channel_id: localStorage.getLocalStorage('SY_LOG_DATA_c') || '',
        scene_id: localStorage.getLocalStorage('SY_LOG_DATA_scene_id') || '',
        type: activityStore.activityInfo?.init?.activity_type,
        partner: activityStore.activityInfo?.init?.create_user,
        ...extraBody,
      }
      if (isWeapp) {
        const pages = getCurrentPages();
        const curPages = pages[pages.length - 1];
        const pageRoute = curPages?.route || '';

        body.browser = 'miniprogram'
        body.current_page_url = pageRoute
      }

      return {
        // 配置参数：可自行写成业务层面的共用参数, 必传
        config: {
          key: import.meta.env.VITE_LOG_KEY,
          secret: import.meta.env.VITE_LOG_SECRET,
          apiUrl: import.meta.env.VITE_LOG_URL,
        },
        body,
      }
    }

    if (isWeapp) {
      createVisitorId((visitorId) => {
        SY_WEAPP_log(logOptions(visitorId))
      })
      return
    }

    loadScript('https://tools.shiyue.com/SY_LOG.min.js').then(() => {
      createVisitorId((visitorId) => {
        const SY_LOG = (window as any).SY_LOG
        SY_LOG.log(logOptions(visitorId))
      })
    })
  }

  return {
    uploadLog,
  }
}
