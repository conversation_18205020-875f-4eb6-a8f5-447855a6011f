import { defaultConfig as milestoneDefaultConfig } from '../../../../common/milestone/index.vue';
import type { InviteFriendsProgressSetting } from './invite-friends-progress';

export const defaultConfig = (): InviteFriendsProgressSetting => {
  return {
    ...milestoneDefaultConfig(),
    taskId: '0',
    height: 170,
    mark: {
      item: {
        x: 7,
        y: 20,
        width: 103,
        height: 240,
      },
      content: {
        x: 4,
        y: -56, 
        width: 103,
        height: 60,
      },
      point: {
        x: 0,
        y: 10,
        width: 104,
        height: 20,
        fontSize: 16,
        color: '#CDF26C',
        align: 'center',
        fontWeight: true,
      },
      name: {
        x: 10,
        y: 30,
        width: 84,
        height: 20,
        fontSize: 14,
        color: '#CDF26C',
        align: 'center',
        fontWeight: true,
        background: '',
      },
      achieved: {
        x: -3,
        y: 20,
        width: 56,
        height: 40,
      },
    },
    avatar: {
      x: -3,
      y: 20,
      width: 90,
      height: 101,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250207/1738897549472-2%20%E6%8B%B7%E8%B4%9D%402x%20%281%29.png',
      defaultAvatar: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250122/1737540261412-defaule.png'
    },
    marks: [
      {
        name: '邀请好友',
        point: 2,
        completed: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250206/1738832164468-%E7%BB%84%206%20%E6%8B%B7%E8%B4%9D%402x%20%281%29.png',
        incomplete: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250206/1738832185346-%E7%BB%84%206%402x%20%281%29.png',
      },
      {
        name: '邀请好友',
        point: 3,
        completed: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250206/1738832164468-%E7%BB%84%206%20%E6%8B%B7%E8%B4%9D%402x%20%281%29.png',
        incomplete: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250206/1738832185346-%E7%BB%84%206%402x%20%281%29.png',
      },
      {
        name: '邀请好友',
        point: 5,
        completed: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250206/1738832164468-%E7%BB%84%206%20%E6%8B%B7%E8%B4%9D%402x%20%281%29.png',
        incomplete: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250206/1738832185346-%E7%BB%84%206%402x%20%281%29.png',
      },
      {
        name: '邀请好友',
        point: 10,
        completed: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250206/1738832164468-%E7%BB%84%206%20%E6%8B%B7%E8%B4%9D%402x%20%281%29.png',
        incomplete: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250206/1738832185346-%E7%BB%84%206%402x%20%281%29.png',
      },
    ],
    multiple: 1,
    showMarkPoint: true,
    showMarkName: true,
    showMarkItem: true,
  };
};