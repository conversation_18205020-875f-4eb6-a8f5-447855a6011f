import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';

/**
 * 元素可见性 Hook 选项
 */
export interface ElementVisibilityOptions {
  /**
   * 根元素，默认为 null（视口）
   */
  root?: Element | null;
  /**
   * 根元素的边距，用于扩展或缩小根元素的可见区域
   */
  rootMargin?: string;
  /**
   * 目标元素可见比例的阈值，范围为 0 到 1
   */
  threshold?: number | number[];
  /**
   * 是否只触发一次回调
   */
  once?: boolean;
  /**
   * 延迟设置观察器的时间（毫秒）
   */
  delay?: number;
}

/**
 * 检测元素何时出现在视口中的 Hook
 * @param callback 元素可见时的回调函数
 * @param options 配置选项
 * @returns 包含目标元素引用的对象
 */
export function useElementVisibility(
  callback: () => void,
  options: ElementVisibilityOptions = {}
) {
  const {
    root = null,
    rootMargin = '0px',
    threshold = 0,
    once = true,
    delay = 1000, // 默认延迟 200ms
  } = options;

  // 目标元素引用
  const targetRef = ref<HTMLElement | null>(null);
  // 是否已触发回调
  const hasTriggered = ref(false);
  // IntersectionObserver 实例
  let observer: IntersectionObserver | null = null;

  // 处理元素可见性变化
  const handleIntersection = (entries: IntersectionObserverEntry[]) => {
    const [entry] = entries;
    
    // 如果元素可见且尚未触发回调（或不是只触发一次）
    if (entry.isIntersecting && (!hasTriggered.value || !once)) {
      callback();
      hasTriggered.value = true;
      
      // 如果只需触发一次，则断开观察
      if (once && observer) {
        observer.disconnect();
      }
    }
  };

  // 设置观察器的函数
  const setupObserver = () => {
    // 如果已经有观察器，先断开连接
    if (observer) {
      observer.disconnect();
      observer = null;
    }

    // 创建 IntersectionObserver 实例
    observer = new IntersectionObserver(handleIntersection, {
      root,
      rootMargin,
      threshold,
    });
    
    // 如果目标元素存在，开始观察
    if (targetRef.value) {
      observer.observe(targetRef.value);
    }
  };

  // 组件挂载时设置观察器，但添加延迟
  onMounted(() => {
    // 使用 nextTick 确保 DOM 已更新
    nextTick(() => {
      // 添加延迟，确保元素完全渲染
      setTimeout(() => {
        setupObserver();
      }, delay);
    });
  });

  // 监听 targetRef 的变化，当元素引用变化时重新设置观察器
  watch(targetRef, (newValue) => {
    if (newValue && !hasTriggered.value) {
      // 添加延迟，确保元素完全渲染
      setTimeout(() => {
        setupObserver();
      }, delay);
    }
  });

  // 组件卸载时清理观察器
  onUnmounted(() => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
  });

  return {
    targetRef,
    hasTriggered,
  };
} 