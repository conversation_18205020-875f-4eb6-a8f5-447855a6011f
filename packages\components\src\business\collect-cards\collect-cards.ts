import type { CommonSetting } from '@bish/types/src';
import type { PopupSetting } from '../../common/popup.vue';
import type { TextSetting } from '../../ui/text/index.vue';
import type { ImageSetting } from '../../ui/image/index.vue';
import type { ShareGuideSetting } from '../share-guide';
import type { CollectCardsGroupSetting } from './components/collect-cards-group';
import type { CollectCardsListSetting } from './components/collect-cards-list';
import type { CollectCardsScratchSetting } from './components/collect-cards-scratch';
import type { CollectCardsGiveSetting } from './components/collect-cards-give';
import type { CollectCardsAskForSetting } from './components/collect-cards-ask-for';

export interface CollectCardsSetting extends CommonSetting {
  /**
   * 抽奖 id
   */
  lotteryId: number
  /**
   * 是否需要绑定角色
   */
  needBindRole: number
  /**
   * 卡组列表
   */
  group: CollectCardsGroupSetting
  /**
   * 卡片列表
   */
  list: CollectCardsListSetting
  /**
   * 刮卡
   */
  scratch: CollectCardsScratchSetting
  /**
   * 赠送
   */
  give: CollectCardsGiveSetting
  /**
   * 索要
   */
  askFor: CollectCardsAskForSetting
  /**
   * 集卡完成提示
   */
  completeTip: TextSetting
  /**
   * 分享引导，WEB端使用，微信小程序直接进行分享
   */
  shareGuide: ShareGuideSetting
} 
