import type { CommonSetting } from '@bish/types/src';
import type { PopupSetting } from '../../../../common/popup.vue';
import type { TextSetting } from '../../../../ui/text/index.vue';
import type { ImageSetting } from '../../../../ui/image/index.vue';
import type { CollectCardsItemSetting } from '../collect-cards-item';

export interface CollectCardsGroupSetting extends CommonSetting {
  /**
   * 卡组项配置
   */
  item: CollectCardsItemSetting
  /**
   * 指示器
   */
  indicator: TextSetting
  /**
   * 上一页
   */
  prev: ImageSetting
  /**
   * 下一页
   */
  next: ImageSetting
} 
