import showCaptcha, { type CallbackData } from '@bish/utils/src/captcha'
import { postCaptchaAppid } from '@bish/api/src/user'

export * from '@bish/utils/src/captcha'

export default async function showTencentCaptcha(
  type: 'normal' | 'strongest' | 'redPacket',
  onSuccess: (value: CallbackData) => void,
) {
  try {
    if (type === 'redPacket') {
      showCaptcha(
        type,
        onSuccess,
      )
      return
    }
    const res = await postCaptchaAppid({
      captcha_type: type,
    })
    if (res.code === 0) {
      showCaptcha(
        type,
        onSuccess,
        {
          aidEncrypted: res.data.encrypt_captcha_app_id,
        },
      )
    }
  } catch (error) {
    console.warn('获取加密串失败', error)
  }
}