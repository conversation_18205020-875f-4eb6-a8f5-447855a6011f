<!--
* @Description: 恭喜获得-弹窗
-->
<template>
  <Popup
    z-index="99"
    :show="show"
    :lock-scroll="false"
    v-model:setting="setting"
    @close="handleClose"
    @ok="handleClose"
  >
    <!-- 自定义弹窗标题 -->
    <template #title>
      <div>{{ title }}</div>
    </template>

    <CongratulationsContent
      v-model:setting="setting"
      :prize="prize"
      @close="handleClose"
    />

    <!-- 操作按钮 -->
    <template #ok />
  </Popup>
</template>

<script lang="ts">
import type { LotteryPrizeDrawData } from '@bish/api/src/lottery';
import type { CongratulationsModalSetting } from './congratulations-modal';

export interface CongratulationsModalProps {
  /**
   * 配置
   */
  setting: CongratulationsModalSetting
  /**
   * 是否显示
   */
  show: boolean
  /**
   * 奖品
   */
  prize: LotteryPrizeDrawData
}

export default {
  name: 'congratulations-modal',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { PRIZE_TYPE_EMPTY } from '../../__constants/prize';
import Popup from '../popup.vue';
import { defaultConfig } from './index';
import CongratulationsContent from './congratulations-content/congratulations-content.vue';

const props = withDefaults(defineProps<CongratulationsModalProps>(), {
  setting: defaultConfig,
  show: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: CongratulationsModalSetting): void;
  (event: 'ok'): void;
  (event: 'close'): void;
}>();

const setting = useVModel(props, 'setting', emits);

const isEmpty = computed(() => {
  return props.prize?.type === PRIZE_TYPE_EMPTY;
});

const title = computed(() => {
  return isEmpty.value ? '谢谢参与' : '恭喜获得';
});

const handleClose = () => {
  emits('close');
};
</script>

<style>
.congratulations {}
</style>