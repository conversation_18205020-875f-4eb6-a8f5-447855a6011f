import type { TeamCreateSetting } from './team-create';

export * from './team-create';

export const defaultConfig = (): TeamCreateSetting => {
  return {
    x: 0,
    y: 0,
    width: 65,
    height: 267,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736922229510-create-btn.png',
    teamId: 0,
    affix: 1,
    modal: {
      x: 0,
      y: 0,
      width: 360,
      height: 260,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736922150614-bg.png',
      closeBtn: {
        x: 310,
        y: 8,
        width: 32,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250114/1736863567849-close.png',
      },
      okBtn: {
        x: 190,
        y: 173,
        width: 97,
        height: 32,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736922160241-accept.png',
      },
      cancelBtn: {
        x: 73,
        y: 173,
        width: 97,
        height: 32,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736922165415-reject.png',
        enabled: true,
      },
      content: {
        x: 70,
        y: 91,
        width: 220,
        height: 64,
        fontSize: 12,
        color: '#363636',
        align: 'center',
        alignItems: 'center',
        content: '<div style="line-height: 1.8;">是否要创建队伍成为队长？</br><span style="color: #EC5B23;">{{teamName}}</span> 的队伍</div>',
        enabled: true,
      },
    },
  };
};