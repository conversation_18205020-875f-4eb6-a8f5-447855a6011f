import { watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import useUserStore from '@bish/store/src/modules/user'
import useActivityStore from '@bish/store/src/modules/activity'
import { deleteLinkParameters } from '@bish/utils/src/utils'
import { setUserInfo } from '@bish/utils/src/storage/modules/login'
import { useActivityPageConfig } from './useActivityPageConfig'
import { useLog } from '../useLog'

export function useMinProgram() {
  const wx = (window as any).wx

  const userStore = useUserStore()
  const activityStore = useActivityStore()

  const route = useRoute()
  const { hasComponent } = useActivityPageConfig()
  const { uploadLog } = useLog()

  let isLoginFlag = false

  watch(
    () => activityStore.activityInfo,
    (newVal) => {
      if (newVal.init?.id && isLoginFlag) {
        isLoginFlag = false
        nextTick(() => {
          // 登陆次数/人数 数据上报
          if (!route.query.invite_code) {
            uploadLog({
              event_name: 'login',
              account_id: Number(route.query.account_id),
              activity_account_id: route.query.account_id as string,
            })
          }

          // 被邀请登录次数/人数 数据上报
          if (route.query.invite_code) {
            uploadLog({
              event_name: 'login',
              activity_account_id: route.query.account_id as string,
              invited_status: 1,
            })
          }
        })
      }
    }
  )

  const loginByUrlQuery = () => {
    if (!route.query.token) {
      return
    }
    const userInfo = {
      account_id: route.query.account_id as string,
      name: route.query.name as string,
      phone_number: route.query.phone_number as string,
      token: route.query.token as string,
      user_type: route.query.user_type as string,
      is_real: route.query?.is_real as any,
    }
    userStore.setUserData(userInfo)
    setUserInfo(userInfo)
    isLoginFlag = true
  }

  // 跳转到小程序的登录页面
  const handleJumpMiniProgramLogin = () => {
    // 删除原先登录成功附加到 url 的相关参数
    const pureUrl = deleteLinkParameters(['account_id', 'name', 'phone_number', 'token', 'user_type'], window.location.href)
    wx?.miniProgram.redirectTo({
      url: `/subpkg/login/login?redirect=` + encodeURIComponent(`/subpkg/webView/webView?url=${encodeURIComponent(pureUrl)}`),
    })
  }

  /**
   * 是否符合参与条件
   * @param needBindRole 是否要求要绑定角色
   */
  const _checkIn = (needBindRole = true) => {
    if (!userStore.userData.token) {
      handleJumpMiniProgramLogin()
      return false
    }
    if (needBindRole && !activityStore.activityAccountInfo.role_info) {
      // 绑定旧角色
      if (hasComponent('BindOldRole')) {
        activityStore.visibleBindOldRole = true
      }
      return false
    }
    return true
  }

  return {
    loginByUrlQuery,
    _checkIn,
    handleJumpMiniProgramLogin,
  }
}
