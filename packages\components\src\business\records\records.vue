<!--
* @Description: 记录组件
-->
<template>
  <Resizable
    class="records"
    :class="{ 'records-affix': setting.affix && !adminStore.editable }"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
    @click="handleOpen"
  >
    <!-- 自定义展示内容 -->
    <slot />

    <!-- 弹窗 -->
    <Popup
      z-index="100"
      :show="open"
      :lock-scroll="false"
      :resizable="false"
      v-model:setting="setting.modalSetting"
      @close="handleClose"
    >
      <!-- 表格 -->
      <Resizable
        class="records-table-container"
        v-model:x="setting.modalSetting.table.x"
        v-model:y="setting.modalSetting.table.y"
        v-model:width="setting.modalSetting.table.width"
        v-model:height="setting.modalSetting.table.height"
      >
        <div class="records-table-scroll" :style="tableStyle">
          <template v-if="dataSource.length > 0">
            <div
              v-if="setting.modalSetting.table.head.show ?? 1"
              class="records-table"
              :class="{ [`records-table__${setting.modalSetting.table.style}`]: true }"
            >
              <!-- 表头 -->
              <div class="records-table-head" :style="tableHeadStyle">
                <div
                  v-for="column in columns"
                  :key="column.dataIndex"
                  class="records-table-head-th"
                  :style="tableThStyle(column)"
                >
                  {{ column.title }}
                </div>
              </div>
            </div>
            <div class="records-table" :class="{ [`records-table__${setting.modalSetting.table.style}`]: true }">
              <div
                v-for="item in dataSource"
                :key="tableRowKey(item)"
                class="records-table-body-tr"
                :style="tableRowStyle"
              >
                <div
                  v-for="(column, index) in columns"
                  :key="`${column.dataIndex}_${index}`"
                  class="records-table-body-td"
                  :class="{ 'records-table-body-td-ellipsis': column.ellipsis }"
                  :style="tableTdStyle(column)"
                >
                  <slot name="bodyCell" :column="column" :record="item">
                    {{ get(item, column.dataIndex) }}
                  </slot>
                </div>
              </div>
            </div>
          </template>
          <!-- 没有数据 -->
          <div v-else class="records-table-noData" :style="emptyStyle">
            <div class="records-table-noData-txt">{{ emptyDesc }}</div>
          </div>
        </div>
      </Resizable>

      <!-- 额外的弹窗内容 -->
      <slot name="popupExtra" />
    </Popup>
  </Resizable>
</template>

<script lang="ts">
import type { CommonBackgroundSetting, CommonSetting } from '@bish/types/src';
import type { PopupSetting } from '../../common/popup.vue';

export type TableStyle = 'intricate' | 'simple'

export type Column = {
  /**
   * 设置列的对齐方式，默认 'center'
   */
  align?: 'left' | 'right' | 'center'
  /**
   * 列数据在数据项中对应的路径，支持通过数组查询嵌套路径
   */
  dataIndex: string
  /**
   * 列宽度
   */
  width?: string | number
  /**
   * 列头显示文字
   */
  title: string
  /**
   * 超过宽度将自动省略，默认 false
   */
  ellipsis?: boolean
  /**
   * 最小宽度，默认 10%
   */
  minWidth?: string | number
}

export interface RecordsSetting extends CommonBackgroundSetting {
  /**
   * 弹窗配置
   */
  modalSetting: PopupSetting & {
    /**
     * 表格
     */
    table: CommonSetting & {
      /**
       * 样式
       */
      style: TableStyle
      /**
       * 圆角，默认 0
       */
      radius: number
      /**
       * 边框配置
       */
      border: {
        /**
         * 宽度
         */
        width: number
        /**
         * 颜色
         */
        color: string
      }
      /**
       * 表头
       */
      head: {
        /**
         * 字号
         */
        fontSize?: number
        /**
         * 是否加粗
         */
        fontWeight?: boolean
        /**
         * 颜色
         */
        color: string
        /**
         * 背景
         */
        background?: string
        /**
         * 是否显示头部，可选 0否 1是，默认 1
         */
        show?: number
      }
      /**
       * 表格主体
       */
      body: {
        /**
         * 字号
         */
        fontSize?: number
        /**
         * 是否加粗
         */
        fontWeight?: boolean
        /**
         * 颜色
         */
        color: string
        /**
         * 背景
         */
        background?: string
      }
    }
  }
  /**
   * 是否固定在页面，1是 0否，默认 1，设置后将使用 fixed 布局
   */
  affix: number
}

export interface RecordsProps {
  /**
   * 配置
   */
  setting: RecordsSetting
  /**
   * 表格列的配置描述
   */
  columns: Column[]
  /**
   * 数据数组
   */
  dataSource: any[]
  /**
   * 表格行 key 的取值，可以是字符串或一个函数，默认 'key'
   */
  rowKey?: string | ((record: any) => string)
  /**
   * 空状态描述，默认 暂无数据
   */
  emptyDesc?: string
  /**
   * 明细弹窗 是否可见
   */
  open: boolean
}

export const defaultConfig = (): RecordsSetting => {
  return {
    x: 0,
    y: 0,
    width: 24,
    height: 91,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20240909/1725869833996-%E6%88%91%E7%9A%84%E5%A5%96%E5%8A%B1%20%E6%8B%B7%E8%B4%9D%202.png',
    modalSetting: {
      x: 0,
      y: 0,
      width: 375,
      height: 300,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20240909/1725869831568-%E5%9B%BE%E5%B1%82%204.png',
      okBtn: {
        width: 0,
        height: 0,
      },
      table: {
        x: 52,
        y: 93,
        width: 272,
        height: 155,
        style: 'intricate',
        radius: 0,
        border: {
          width: 1,
          color: '#6C1D08',
        },
        head: {
          fontSize: 14,
          fontWeight: false,
          color: '#732508',
          background: '#E1B98B',
        },
        body: {
          fontSize: 12,
          fontWeight: false,
          color: '#732508',
          background: '#E1B98B',
        },
      },
      closeBtn: {
        x: 315,
        y: 31,
        width: 19,
        height: 19,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20240909/1725869860188-%E6%9B%B2%E7%BA%BF%20606%20%E6%8B%B7%E8%B4%9D%202.png',
      },
    },
    affix: 0,
  };
};

export default {
  name: 'records',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useAdminStore from '@bish/store/src/modules/admin';
import { pxTransform } from '@bish/utils/src/viewport';
import { get } from '@bish/utils/src/utils';
import Resizable from '../../ui/resizable/index.vue';
import Popup from '../../common/popup.vue';

const props = withDefaults(defineProps<RecordsProps>(), {
  setting: defaultConfig,
  columns: () => ([]),
  dataSource: () => ([]),
  rowKey: 'key',
  emptyDesc: '暂无数据',
  open: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: RecordsSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
  (event: 'open'): void;
}>();

const activityPageStore = useActivityPageStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const themeColor = computed(() => {
  return activityPageStore.mergedPageConfig.themeColor;
});

const borderStyle = computed<CSSProperties>(() => {
  const { setting } = props;
  return {
    borderWidth: pxTransform(setting.modalSetting.table.border.width),
    borderColor: setting.modalSetting.table.border.color,
  };
});

const tableStyle = computed<CSSProperties>(() => {
  const { setting } = props;
  const { setting: { modalSetting } } = props;
  return {
    borderRadius: pxTransform(setting.modalSetting.table.radius),
    background: modalSetting.table.style === 'intricate' ? modalSetting.table.body.background : undefined,
    ...borderStyle.value,
  };
});


const tableHeadStyle = computed<CSSProperties>(() => {
  const { setting: { modalSetting } } = props;
  return {
    backgroundColor: themeColor.value,
    fontSize: pxTransform(modalSetting.table.head.fontSize || 14),
    fontWeight: modalSetting.table.head.fontWeight ? 'bold' : void 0,
    color: modalSetting.table.head.color,
    background: modalSetting.table.head.background,
  };
});

const tableRowStyle = computed<CSSProperties>(() => {
  const { setting: { modalSetting } } = props;
  return {
    fontSize: pxTransform(modalSetting.table.body.fontSize || 12),
    fontWeight: modalSetting.table.body.fontWeight ? 'bold' : void 0,
    color: modalSetting.table.body.color,
    ...borderStyle.value,
  };
});

const tableThStyle = (column: Column): CSSProperties => {
  const flexBasis = typeof column.width === 'number' ? pxTransform(column.width) : column.width || 1;
  const flexGrow = column.width ? 0 : 1;
  return {
    textAlign: column.align || 'center',
    flex: `${flexGrow} 0 ${flexBasis}`,
    padding: `${pxTransform(10)} 0`,
    minWidth: typeof column.minWidth === 'number' ? pxTransform(column.minWidth) : column.minWidth || '10%',
    ...borderStyle.value,
  };
};

const tableTdStyle = (column: Column): CSSProperties => {
  const flexBasis = typeof column.width === 'number' ? pxTransform(column.width) : column.width || 1;
  const flexGrow = column.width ? 0 : 1;
  return {
    textAlign: column.align || 'center',
    flex: `${flexGrow} 0 ${flexBasis}`,
    padding: `${pxTransform(10)} 0`,
    minWidth: typeof column.minWidth === 'number' ? pxTransform(column.minWidth) : column.minWidth || '10%',
    ...borderStyle.value,
  };
};

const emptyStyle = computed<CSSProperties>(() => {
  const { setting: { modalSetting } } = props;
  return {
    background: modalSetting.table.body.background,
    ...tableRowStyle.value,
  };
});

const tableRowKey = (item: any) => {
  if (typeof props.rowKey === 'string') {
    return item[props.rowKey];
  }
  return props.rowKey(item);
}

const handleOpen = () => {
  emits('open');
};

const handleClose = (e: Event) => {
  emits('close', e);
};
</script>

<style lang="less">
@records-prefix: records;

.@{records-prefix} {
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-table-container {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }

  &-table {
    &__simple {
      .@{records-prefix}-table {
        &-head {
          background: none !important;

          &-th {
            border-right: none !important;
          }
        }

        &-body {
          background: none !important;

          &-td {
            border-right: none !important;
          }
        }
      }
    }

    &-head {
      display: flex;

      &-th {
        flex: 1 0 0%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-style: solid;
        border-top: none;
        border-left: none;

        &:last-child {
          border-right: none;
        }
      }
    }

    &-body {
      &-tr {
        display: flex;
      }

      &-td {
        flex: 1 0 0%;
        display: flex;
        align-items: center;
        justify-content: center;
        border-style: solid;
        border-top: none;
        border-left: none;
        border-right: none;

        &:last-child {
          border-right: none;
        }

        &-ellipsis {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: break-all;
          display: block;
        }
      }
    }

    &-noData {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    &-scroll {
      flex: 1;
      overflow: auto;
    }
  }

  &-affix {
    position: fixed !important;
    z-index: 98;
  }
}
</style>