import { ref, watch } from 'vue'
import useActivityStore from '@bish/store/src/modules/activity'
import { getChargeSum } from '@bish/api/src/percharge'
export function useCharge(activityAccountInfo: any, activityInfo: any) {
  const activityStore = useActivityStore()
  const count = ref(0)
  /**
   * @description: 获取活动账号累计充值金额
   * @param {ComponentWithUserInfoParams} extraParams
   * @return {*}
   */
  const fetchChargeSum = async (newActivityAccountInfo: any) => {
    const params = {
      act_id: newActivityAccountInfo.activity_id,
      act_acc_id: newActivityAccountInfo.act_acc_id,
    }
    const { code, data } = await getChargeSum(params)
    if (!code) {
      activityStore.setSum(data)
    }
  }
  // 监听多个来源，如果是 ref 定义的数据，不需要 .value
  watch(
    [activityAccountInfo, activityInfo],
    ([newActivityAccountInfo, newActivityInfo]) => {
      if (newActivityAccountInfo.act_acc_id && newActivityInfo.config?.preCharge) {
        if (count.value < 1) {
          fetchChargeSum(newActivityAccountInfo)
        }

        count.value++
      }
    },
    { immediate: true, deep: true }
  )
  return {}
}
