<template>
  <div @click="handleClick">
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import useAdminStore from '@bish/store/src/modules/admin';
import { navigateScroll } from '../../__utils/location';

export interface LinkProps {
  /**
   * 点击跳转的地址
   */
  href?: string;
  /**
   * 相当于 a 链接的 target 属性，href 存在时生效，默认
   */
  target?: string;
  /**
   * 是否限制跳转
   */
  isLimit?: boolean
}

const props = withDefaults(defineProps<LinkProps>(), {
  href: '',
  isLimit: false,
});

const adminStore = useAdminStore();

const getOrigin = (url: string): string => {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.origin;
  } catch (error) {
    return '';
  }
};

const providedTarget = computed(() => {
  if (props.target) {
    return props.target;
  }
  const origin = getOrigin(props.href || '');
  if (origin === window?.location.origin) {
    return '_self';
  }
  return '_blank';
});

const handleClick = (e: MouseEvent) => {
  if (!props.href) {
    return;
  }
  if (props.isLimit) {
    return;
  }
  // 后台端编辑模式下，点击跳转链接时，弹出确认框，避免误操作
  if (adminStore.editable) {
    if (window.confirm('是否确认进行跳转？')) {
      window.open(props.href, providedTarget.value);
    }
    return;
  }
  navigateScroll(props.href);
}
</script>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ui-link',
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  }
});
</script>
