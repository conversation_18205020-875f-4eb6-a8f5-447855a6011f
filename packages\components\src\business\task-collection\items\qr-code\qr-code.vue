<!--
* @Description: 二维码任务
-->
<template>
  <TaskItem v-model:setting="setting" v-model:status="status" @do="handleClaim">
    <Popup
      z-index="99"
      :show="open"
      :lock-scroll="false"
      v-model:setting="setting.modalSetting"
      @close="handleClose"
    >
      <!-- 二维码 -->
      <div class="task-item-qrCode-content">
        <UiImage :setting="setting.modalSetting.qrCode" show-menu-by-longpress />
        <div v-if="showMask" :style="maskStyle" class="task-item-qrCode-mask">
          登录解锁二维码
        </div>
      </div>

      <!-- 登录按钮 -->
      <UiImage
        v-if="showLoginBtn"
        :setting="setting.modalSetting.loginBtn"
        @click="handleClaim"
      />
    </Popup>
  </TaskItem>
</template>

<script lang="ts">
export default {
  name: 'task-qr-code',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, computed, watch, onMounted } from 'vue';
import type { CSSProperties } from 'vue'; 
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import useRouteQuery from '@bish/hooks/src/useRouteQuery';
import { pxTransform } from '@bish/utils/src/viewport';
import { defaultConfig } from './index';
import type { TaskQrCodeSetting } from './qr-code';
import TaskItem from '../item.vue';
import UiImage from '../../../../ui/image/index.vue';
import Popup from '../../../../common/popup.vue';
import { parseQueryString } from '@bish/utils/src/utils';

export interface TaskCollectionProps {
  /**
   * 配置
   */
  setting: TaskQrCodeSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TaskCollectionProps>(), {
  setting: () => defaultConfig(),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskQrCodeSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityStore = useActivityStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);
const status = useVModel(props, 'status', emits);

const [open, toggle] = useControllableStatus(props, emits, { fieldName: 'showModal' });

const { query } = useRouteQuery();

const preOperationQrCode = query.value.pre_op === 'qr-code';

const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

const userTask = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
});

const completed = computed(() => {
  return userTask.value ? userTask.value?.progress_num === userTask.value?.target_num : false;
});

const showMask = computed(() => {
  if (adminStore.editable) {
    return false;
  }
  return !activityStore.activityAccountInfo?.account_id;
});

const showLoginBtn = computed(() => {
  if (adminStore.editable) {
    return true;
  }
  return !activityStore.activityAccountInfo?.account_id;
});

const maskStyle = computed<CSSProperties>(() => {
  const { qrCode } = setting.value.modalSetting || {};
  return {
    left: pxTransform(qrCode.x || 0),
    top: pxTransform(qrCode.y || 0),
    width: pxTransform(qrCode.width || 0),
    height: pxTransform(qrCode.height || 0),
    fontSize: pxTransform(12),
  };
});

// 登录成功后，任务已完成则隐藏弹窗
watch(
  () => completed.value,
  (newVal) => {
    const queryParams = parseQueryString(window.location.href);
    if (newVal && open.value && queryParams?.pre_op === 'qr-code') {
      // 这里加个延迟，缓解一下在小程序环境下 hideLoading 会打 toast 也一起隐藏的问题
      setTimeout(() => {
        showToast(setting.value.completeTip);
        toggle(false);
      }, 1000);
    }
  },
);

/**
 * 初始展示弹窗
 */
const initOpenModal = () => {
  if (preOperationQrCode && !completed.value) {
    toggle(true);
  }
  if (preOperationQrCode && completed.value) {
    // 这里加个延迟，缓解一下在小程序环境下 hideLoading 会打 toast 也一起隐藏的问题
    setTimeout(() => {
      showToast(setting.value.completeTip);
    }, 1000);
  }
};

onMounted(() => {
  initOpenModal();
});

const handleClaim = async () => {
  console.log('handleClaim');
  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfig.value) {
    showToast('活动已结束~');
    return;
  }
  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  toggle(true);
};

const handleClose = () => {
  toggle(false);
  activityStore.getComponentWithUserInfo();
};
</script>

<style>
.task-item {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.task-item-qrCode-content {
  position: relative;
}

.task-item-qrCode-mask {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.8);
}
</style>
