import useUserStore from '@bish/store/src/modules/user';
// @ts-expect-error
import sign from '@bish/utils/src/sign'
import { useEnv } from '@bish/hooks/src/useEnv'
import { getRealName } from '@bish/utils/src/storage/modules/real-name';
import { getSimilar } from '@bish/utils/src/storage/modules/similar';
// 国际化
import i18n from '@bish/lang/src'
import { filterObject } from './__utils';

const {
  VITE_BASE_URL,
  VITE_BASE_SIGN_KEY,
  VITE_ACTIVITY_URL,
  VITE_ACTIVITY_SIGN_KEY,
  VITE_SDK_URL,
  VITE_SDK_SIGN_KEY,
  VITE_COMMON_URL,
  VITE_COMMON_SIGN_KEY,
  VITE_PAY_URL,
  VITE_PAY_SIGN_KEY,
  VITE_MALL_URL,
  VITE_MALL_SECRET,
  VITE_WECHAT_URL,
  VITE_WECHAT_SECRET,
} = useEnv()

export interface Result<T> {
  code: number
  message: string
  data: T
}

export type CustomAxiosConfig = Omit<Parameters<typeof uni.request>[0], 'success' | 'fail' | 'data'> & {
  /**
   * 是否显示错误信息
   */
  errToast?: boolean
  /**
   * 是否需要 token，默认 true
   */
  needToken?: boolean
}
const signKeyMap: Record<string, string> = {
  [VITE_BASE_URL]: VITE_BASE_SIGN_KEY,
  [VITE_ACTIVITY_URL]: VITE_ACTIVITY_SIGN_KEY,
  [VITE_SDK_URL]: VITE_SDK_SIGN_KEY,
  [VITE_COMMON_URL]: VITE_COMMON_SIGN_KEY,
  [VITE_PAY_URL]: VITE_PAY_SIGN_KEY,
  [VITE_MALL_URL]: VITE_MALL_SECRET,
  [VITE_WECHAT_URL]: VITE_WECHAT_SECRET,
}
export function getSignKey(url: string) {
  const regex = /(https?:\/\/[^/]+)/
  const match = url.match(regex)
  if (match) {
    const domain = match[1] // => https://test.shiyue.com
    return signKeyMap[domain]
  }
  return ''
}

const request = <T>(url: string, data?: any, options?: CustomAxiosConfig) => {
  const { needToken = true, errToast, ...otherOptions } = options || {}
  // 获取用户实名状态
  const realName = getRealName() ?? 1
  // 获取是否参与过相似活动
  const similar = getSimilar() ?? 0

  // 检查 act_acc_id 参数，如果值为空，中断请求并抛出相似活动错误
  if (similar && data && 'act_acc_id' in data && !data.act_acc_id) {
    return Promise.reject(new Error('SIMILAR_ACTIVITY'))
  }

  // 检查 act_acc_id 参数，如果值为空，中断请求并抛出实名认证错误
  if (!realName && data && 'act_acc_id' in data && !data.act_acc_id) {
    return Promise.reject(new Error('NEED_REAL_NAME'))
  }

  const signKey = getSignKey(url)
  const ts = Math.round(+new Date() / 1000).toString()
  const userStore = useUserStore()

  const mergedData: Record<string, any> = {
    ...filterObject(data),
    ts,
  };
  
  if (needToken && userStore.userData.token) {
    mergedData.token = userStore.userData.token
  }
  mergedData.sign = sign(mergedData, signKey)
  
  return new Promise<Result<T>>((resolve, reject) => {
    uni.request({
      ...otherOptions,
      url,
      data: mergedData,
      header: {
        Accept: 'application/json, text/javascript, */*; q=0.01',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Accept-Language': i18n.global.locale,
        // @ts-expect-error
        ...(otherOptions.header || {}),
      },
      // @ts-expect-error
      success: ((res: {
        statusCode: number;
        cookies: string[];
        header: any;
        data: Result<T>;
      }) => {
        if (res.statusCode === 200) {
          if (res.data.code === 0) {
            resolve(res.data);
          } else if ([1003, 1006, 1008, 2002].includes(res.data.code)) {
            userStore.resetUserData()
            uni.showToast({
              title: i18n.global.t('login-expired', '登录过期~'),
              icon: 'none',
              duration: 3000,
            });
            resolve(res.data);
          } else {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
              duration: 3000,
            });
            reject(res.data);
          }
        } else if (res.statusCode === 403) {
          uni.showModal({
            title: '提示',
            content: '403：请拨vpn连着内网访问',
          });
        } else {
          reject(res.data);
          uni.hideLoading();
        }
      }),
      fail: (res => {
        // 处理 needRealName 错误
        if (res.errMsg && (res.errMsg.includes('NEED_REAL_NAME') || res.errMsg.includes('SIMILAR_ACTIVITY'))) {
          reject(res.errMsg);
          return;
        }
        
        uni.showToast({
          title: res.errMsg,
          icon: 'none',
          duration: 3000,
        });
        reject(res.errMsg);
      }),
    });
  });
};

export const http = {
  get<T = any>(url: string, config?: CustomAxiosConfig): Promise<Result<T>> {
    return request<T>(url, {}, config)
  },

  post<T = any>(url: string, data?: object, config?: CustomAxiosConfig): Promise<Result<T>> {
    // @ts-expect-error
    return request<T>(url, data, { ...(config || {}), method: 'POST' })
  },

  // put<T = any>(url: string, data?: object, config?: CustomAxiosConfig): Promise<T> {
  //   return request<T>(url, data, config)
  // },

  // delete<T = any>(url: string, config?: CustomAxiosConfig): Promise<T> {
  //   return request<T>(url, {}, config)
  // },
}

export default request;
