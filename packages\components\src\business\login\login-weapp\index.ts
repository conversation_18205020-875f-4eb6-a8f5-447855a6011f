import type { LoginWeappModalSetting, LoginWeappSetting } from './login-weapp';

export * from './login-weapp';

export const defaultModalConfig = (): LoginWeappModalSetting => ({
  modalSetting: {
    x: 0,
    y: 0,
    width: 303,
    height: 420,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250113/1736749062598-m-login-bg.png',
    okBtn: {
      x: 27,
      y: 324,
      width: 250,
      height: 40,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250113/1736749067471-m-login-btn.png'
    },
    closeBtn: {
      x: 136,
      y: 443,
      width: 32,
      height: 32,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250113/1736749116290-close.png'
    },
  },
  agreement: {
    position: {
      x: 32,
      y: 291,
      width: 250,
      height: 32,
    },
    show: true,
    textColor: '#745137',
    linkColor: '#745137',
  },
});

export const defaultConfig = (): LoginWeappSetting => ({
  modalSetting: defaultModalConfig(),
})