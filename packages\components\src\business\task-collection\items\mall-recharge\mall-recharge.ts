import type { TaskCollectionCommon } from '../index'
import type { PopupSetting } from '../../../../common/popup.vue';

/**
 * 任务-商城充值配置
 */
export type TaskMallRechargeSetting = TaskCollectionCommon & {
  /**
   * 是否内嵌微信小程序，1是 0否
   */
  weappWebview: number
  /**
   * 内嵌中间页配置
   */
  weappEmbedSetting?: MallRechargeWeappEmbedSetting
}

export interface MallRechargeWeappEmbedSetting {
  /**
   * 弹窗基础配置
   */
  modalSetting: PopupSetting
}
