<!--
* @Description: 集卡-好友赠送
-->
<template>
  <Resizable
    v-model:x="setting.actionBtn.x"
    v-model:y="setting.actionBtn.y"
    v-model:width="setting.actionBtn.width"
    v-model:height="setting.actionBtn.height"
    class="collect-cards-ask-for"
  >
    <UiImage
      :setting="actionBtnSetting"
      :movable="false"
      :resizable="false"
      class="collect-cards-ask-for-btn"
      :class="{ 'collect-cards-ask-for-btn__disabled': disabled }"
      @click="handleAskFor"
    />

    <!-- 赠送弹窗-->
    <Popup
      z-index="99"
      :show="openAskForModal"
      :lock-scroll="false"
      v-model:setting="setting.modal"
      @close="handleCloseAskForModal"
      @ok="handleGive"
    >
      <ResizableProvider>
        <!-- 标题 -->
        <UiImage
          v-if="setting.modal.title.enabled"
          v-model:setting="setting.modal.title"
          :confined="false"
        />
        <!-- 当前拥有 -->
        <UiText
          v-if="setting.modal.current.enabled && showCurrent"
          v-model:setting="setting.modal.current"
          :confined="false"
          :interpolation="interpolation"
        />
        <!-- 卡片盒子 -->
        <UiImage
          v-if="setting.modal.box.enabled"
          v-model:setting="setting.modal.box"
          :confined="false"
        />
        <!-- 卡片 -->
        <Resizable
          v-model:x="setting.modal.card.x"
          v-model:y="setting.modal.card.y"
          v-model:width="setting.modal.card.width"
          v-model:height="setting.modal.card.height"
          :confined="false"
          :style="{
            backgroundImage: `url(${turnDetail?.item_icon})`,
          }"
          class="collect-cards-ask-for-card"
        />
        <!-- 卡片名称 -->
        <UiText
          v-if="setting.modal.name.enabled"
          v-model:setting="setting.modal.name"
          :confined="false"
        >
          {{ turnDetail?.item_name }}
        </UiText>
      </ResizableProvider>
    </Popup>

    <!-- 已拥有弹窗 -->
    <Popup
      z-index="99"
      :show="openNoCardModal"
      :lock-scroll="false"
      v-model:setting="setting.noCardModal"
      @close="handleCloseNoCardModal"
    >
      <ResizableProvider>
        <!-- 标题 -->
        <UiImage
          v-if="setting.noCardModal.title.enabled"
          v-model:setting="setting.noCardModal.title"
          :confined="false"
        />
        <!-- 卡片盒子 -->
        <UiImage
          v-if="setting.noCardModal.box.enabled"
          v-model:setting="setting.noCardModal.box"
          :confined="false"
        />
        <!-- 卡片 -->
        <Resizable
          v-model:x="setting.noCardModal.card.x"
          v-model:y="setting.noCardModal.card.y"
          v-model:width="setting.noCardModal.card.width"
          v-model:height="setting.noCardModal.card.height"
          :confined="false"
          :style="{
            backgroundImage: `url(${turnDetail?.item_icon})`,
          }"
          class="collect-cards-ask-for-card"
        />
        <!-- 卡片名称 -->
        <UiText
          v-if="setting.noCardModal.name.enabled"
          v-model:setting="setting.noCardModal.name"
          :confined="false"
        >
          {{ turnDetail?.item_name }}
        </UiText>
        <!-- 遮罩 -->
        <div
          :style="maskStyle"
          class="collect-cards-ask-for-mask"
        >
          <!-- 提示 -->
          <UiText
            v-if="setting.noCardModal.tip.enabled"
            v-model:setting="setting.noCardModal.tip"
            :confined="false"
          />
        </div>
      </ResizableProvider>
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'collect-cards-ask-for',
}
</script>

<script lang="ts" setup>
import { computed, ref, onMounted, watch } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useAdminStore from '@bish/store/src/modules/admin';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import type { ShareMessageConfigType } from '@bish/hooks/src/business/useActivityPageConfig';
import { pxTransform } from '@bish/utils/src/viewport';
import { postCollectDrawShare, postCollectDrawTurnDetail, postCollectDrawOperate } from '@bish/api/src/collect-cards';
import type { CollectDrawTurnDetailData } from '@bish/api/src/collect-cards';
import type { ComponentWithUserInfoCollectDrawCardList, ComponentWithUserInfoCollectDrawCardListReceiveLog } from '@bish/api/src/activity';
import { useLog } from '@bish/hooks/src/useLog';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { additionalLinkParameters, sleep, userAgent } from '@bish/utils/src/utils';
import { whiteUrl } from '@bish/utils/src/urlSearch';
import useShortLink from '@bish/hooks/src/business/useShortLink';
import localStorage from '@bish/utils/src/storage/localStorage';
import { useActivityPageConfig } from '@bish/hooks/src/business/useActivityPageConfig';
import useRouteQuery from '@bish/hooks/src/useRouteQuery';
import type { StatefulComponent } from '@bish/types/src/admin';
import { interpolateString, copyText } from '../../../../__utils/text';
import Resizable from '../../../../ui/resizable/index.vue';
import ResizableProvider from '../../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../../ui/image/index.vue';
import UiText from '../../../../ui/text/index.vue';
import Popup from '../../../../common/popup.vue';
import type { CollectCardsAskForSetting } from './collect-cards-ask-for';
import { defaultConfig } from './index';

export interface CollectCardsProps {
  setting: CollectCardsAskForSetting
  status?: StatefulComponent['status']
  /**
   * 当前选中用户卡片信息
   */
  userCardInfoCurrent?: ComponentWithUserInfoCollectDrawCardList[number]
  /**
   * 当前选中用户卡片获取记录
   */
  receiveCurrent?: ComponentWithUserInfoCollectDrawCardListReceiveLog[number]
}

const CC_M = 2; // 标识：集卡索要

const props = withDefaults(defineProps<CollectCardsProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: CollectCardsAskForSetting): void;
  (event: 'share-guide'): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const turnDetail = ref<CollectDrawTurnDetailData>();

const setting = useVModel(props, 'setting', emits);
const [openAskForModal, toggleAskForModal] = useControllableStatus(props, emits, { fieldName: 'showAskForModal' });
const [openNoCardModal, toggleNoCardModal] = useControllableStatus(props, emits, { fieldName: 'showNoCardModal' });

const activityPageStore = useActivityPageStore();
const userStore = useUserStore();
const activityStore = useActivityStore();
const adminStore = useAdminStore();

const { uploadLog } = useLog();
const { shortLink, getShortLink } = useShortLink();
const { setWeappShareMessage } = useActivityPageConfig();
const { query } = useRouteQuery();

const { isUserAgentType } = userAgent();

// 是否已查看，避免重复打开
let viewed = false;

const interpolation = computed(() => {
  return {
    count: turnDetail.value?.item_num ?? 0,
  };
});

const showCurrent = computed(() => {
  return adminStore.editable || (turnDetail.value?.item_num ?? 0) > 0;
});

const actionBtnSetting = computed<CollectCardsAskForSetting['actionBtn']>(() => {
  return {
    top: 0,
    left: 0,
    width: setting.value.actionBtn.width,
    height: setting.value.actionBtn.height,
    imgLink: setting.value.actionBtn.imgLink,
  };
});

const maskStyle = computed<CSSProperties>(() => {
  return {
    top: pxTransform(setting.value.noCardModal.card.y!),
    left: pxTransform(setting.value.noCardModal.card.x!),
    width: pxTransform(setting.value.noCardModal.card.width),
    height: pxTransform(setting.value.noCardModal.card.height),
  };
});

const disabled = computed(() => {
  // 有卡片
  return userStore.userData.token && props.userCardInfoCurrent?.receive_log.length;
});

const fetchGiftInfo = async () => {
  if (viewed) {
    return;
  }
  if (+query.value.cc_m !== CC_M || !query.value.item_id || !query.value.log_id) {
    return;
  }
  toggleAskForModal(false);
  toggleNoCardModal(false);
  await sleep(300);
  try {
    const res = await postCollectDrawTurnDetail({
      act_id: activityPageStore.activityPageConfig.activity_id,
      act_acc_id: activityStore.activityAccountInfo.act_acc_id,
      item_id: +query.value.item_id,
      operate_type: 4,
      share_log_id: +query.value.log_id,
    });
    if (res.code === 0) {
      turnDetail.value = res.data;

      // 已登录，并且没有对应卡片
      if (activityStore.activityAccountInfo.act_acc_id && res.data.item_num <= 0) {
        toggleNoCardModal(true);
        return;
      }
      toggleAskForModal(true);
    }
  } catch (error) {
    console.warn('获取卡片赠送信息失败', error);
  }
}

/**
 * 初始化卡片赠送信息，没有登录态时
 */
const initGiftInfo = () => {
  if (userStore.userData.token) {
    return;
  }
  fetchGiftInfo();
};

onMounted(() => {
  initGiftInfo();
});

watch(
  () => activityStore.activityAccountInfo.account_id,
  () => {
    fetchGiftInfo();
  }
);

const genShareParams = async () => {
  try {
    const res = await postCollectDrawShare({
      act_id: activityPageStore.activityPageConfig.activity_id,
      act_acc_id: activityStore.activityAccountInfo.act_acc_id,
      item_id: props.userCardInfoCurrent!.id,
      type: CC_M,
    });
    if (res.code === 0) {
      const scene_id = localStorage.getLocalStorage('SY_LOG_DATA_scene_id') || '';
      const params: Record<string, any> = {
        cc_m: CC_M, // 标识：集卡赠送
        item_id: props.userCardInfoCurrent!.id,
        log_id: res.data.id, // 分享记录id
        invite_code: activityStore.activityAccountInfo.invite_code,
      }
      if (scene_id) {
        params.c = scene_id;
      }
      return params;
    }
  } catch (error) {
    showToast('获取卡片索取参数失败，请稍后再试');
    console.warn('获取卡片索取参数失败', error);
  }
  return {};
};

/**
 * 创建短链
 */
const createShortLink = async () => {
  const url = window?.location.href;
  const pureUrl = whiteUrl(url);
  const params = await genShareParams();
  if (!params.log_id) {
    return Promise.reject();
  }
  return await getShortLink(
    additionalLinkParameters(params, pureUrl)
  );
};

/**
 * 设置分享参数
 */
const sendShareMessage = async (copyTemplate: CollectCardsAskForSetting['copyTemplates'][0]) => {
  const shareParams = await genShareParams();

  if (!shareParams.log_id) {
    return false;
  }

  // 设置微信小程序内嵌分享参数
  if (isUserAgentType === 'WX_WEBVIEW') {
    const shareContent: ShareMessageConfigType = {};
    if (copyTemplate?.text) {
      shareContent.title = copyTemplate?.text;
    }
    if (copyTemplate?.poster) {
      shareContent.imgUrl = copyTemplate?.poster;
    }
    setWeappShareMessage(shareParams, shareContent);
  }
  // if (uploadLog) {
  //   uploadLogInvite();
  // }
  return true;
}

const handleAskFor = async () => {
  // 数据上报：点击索要
  uploadLog({
    event_name: 'click',
    click_id: 145,
    click_type: 3,
  });

  if (disabled.value) {
    return;
  }

  // 登录态拦截
  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }
  // 随机选择一条邀请文案
  const list = setting.value.copyTemplates || [];
  const randomIndex = list.findIndex(item => item.cardId === props.userCardInfoCurrent?.id);
  const current = list[randomIndex];

  // 微信小程序内嵌环境
  if (isUserAgentType === 'WX_WEBVIEW') {
    const success = await sendShareMessage(current);
    if (success) {
      emits('share-guide');
    }
    return;
  }
  try {
    await createShortLink();
    const randomText = current ? `${current.text}{{link}}` : ''
    const mergeText = randomText
    const url = mergeText
      ? interpolateString(mergeText, { link: shortLink.value })
      : shortLink.value;
    copyText(url, { success: '链接已复制，快去分享给好友吧~', error: '复制失败' });
  } catch (error) {
    console.warn('创建赠送链接失败', error);
  }
}

const handleGive = async () => {
  // 登录态拦截
  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }
  if (!turnDetail.value?.item_id) {
    return;
  }
  try {
    const res = await postCollectDrawOperate({
      act_id: activityPageStore.activityPageConfig.activity_id,
      act_acc_id: activityStore.activityAccountInfo.act_acc_id,
      share_log_id: +query.value.log_id,
      item_id: turnDetail.value?.item_id,
      operate_type: 4,
    });
    if (res.code === 0) {
      showToast('赠送成功');
    }
  } catch (error) {
    console.warn('赠送卡片失败', error);
  } finally {
    // 重新获取用户与组件的数据
    activityStore.getComponentWithUserInfo();
    // 关闭赠送弹窗
    handleCloseAskForModal();
  }
};

const handleCloseAskForModal = () => {
  viewed = true;
  toggleAskForModal(false);
};

const handleCloseNoCardModal = () => {
  viewed = true;
  toggleNoCardModal(false);
};
</script>

<style>
.collect-cards-ask-for {}

.collect-cards-ask-for-btn__disabled {
  filter: grayscale(100%);
}

.collect-cards-ask-for-card {
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.collect-cards-ask-for-mask {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

@supports (background: -moz-element(#bg)) {
  .collect-cards-ask-for-mask {
    background: -moz-element(#bg) no-repeat;
    filter: blur(4px);
  }
}

.collect-cards-ask-for-tip {
  color: #FFFFFF;
  font-weight: 500;
}
</style> 
