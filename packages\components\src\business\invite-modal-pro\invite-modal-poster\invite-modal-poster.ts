import type { CommonSetting } from '@bish/types/src';
import type { ImageSetting } from '../../../ui/image/index.vue';
import type { TextSetting } from '../../../ui/text/index.vue';
import type { PopupSetting } from '../../../common/popup.vue';

export interface InviteModalPosterSetting extends PopupSetting {
  /**
   * 海报背景图
   */
  posterBg: ImageSetting
  /**
   * 二维码
   */
  qrCode: CommonSetting
  /**
   * 保存按钮
   */
  saveBtn: ImageSetting
  /**
   * 提示文案
   */
  tip: TextSetting
}