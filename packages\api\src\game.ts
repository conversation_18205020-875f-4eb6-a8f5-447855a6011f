import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'

const { VITE_ACTIVITY_URL } = useEnv()

export type GameDrawNumParams = {
  /**
   * 活动id
   */
  activity_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
}

export type GameDrawNumData = {
  /**
   * 已获得抽卡数
   */
  recruit_num: number
}

/**
 * 获取游戏预抽卡次数
 * @param params GameDrawNumParams
 * @returns 
 */
export function postGameDrawNum(params: GameDrawNumParams) {
  return http.post<GameDrawNumData>(`${VITE_ACTIVITY_URL}/game/get_game_draw_num`, params)
}
