import type { CommonSetting } from '@bish/types/src/index';
import type { TextSetting } from '../../../ui/text/index.vue';
import type { ImageSetting } from '../../../ui/image/index.vue';
import type { CollectCardsScratchSetting } from '../../collect-cards/components/collect-cards-scratch';
import { defaultConfig as collectCardsScratchDefaultConfig } from '../../collect-cards/components/collect-cards-scratch';

export type TaskCollectionCommon = CommonSetting & {
  /**
   * 任务ID
   */
  taskId: number | undefined
  /**
   * 背景图片
   */
  bgImage?: string
  /**
   * 点亮背景图片
   */
  activeBgImage?: string
  /**
   * 完成背景图片
   */
  endBgImage?: string
  /**
   * 标题
   */
  title?: TextSetting
  /**
   * 任务描述
   */
  description?: TextSetting
  /**
   * 任务进度，1/3
   */
  process?: TextSetting
  /**
   * 是否需要绑定角色，默认 1
   */
  bindRole: number
  /**
   * 去完成按钮
   */
  doBtn?: ImageSetting
  /**
   * 立即领取按钮
   */
  claimBtn?: ImageSetting
  /**
   * 已完成按钮
   */
  achievedBtn?: ImageSetting
  /**
   * 领取成功提示
   */
  receivedSuccessTip?: string
  /**
   * 重复领取提示
   */
  receivedRepeatTip?: string
  /**
   * 任务未完成提示
   */
  notAccomplishTip?: string
  /**
   * 是否限制用户切换登录，有些模块希望在用户领取过奖励之后，不想让用户切换其他账号再领取奖励
   * 0否 1是
   */
  limitLogin: number
  /**
   * 是否是伪任务，若是，则会在点击去完成按钮时，上报任务完成
   * 0否 1是
   */
  isFakeTask: number
  /**
   * 领取成功提示方式，1：toast 2：恭喜获得 3：卡片
   * TODO: 补充恭喜获得配置
   */
  congratsMode: number
  /**
   * 抽奖id，当 congratsMode 为 3 时，需要传入抽奖id
   */
  lotteryId?: number
  /**
   * 卡片配置
   * TODO: 补充恭喜获得配置
   */
  scratch?: CollectCardsScratchSetting
}

export const scratchConfig = (): CollectCardsScratchSetting => collectCardsScratchDefaultConfig();

export const commonTaskConfig = (): TaskCollectionCommon => {
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 71,
    taskId: 0,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241017/1729164038210-%E6%AF%8F%E6%97%A5%E7%99%BB%E5%BD%95%E6%B8%B8%E6%88%8F.png',
    title: {
      x: 13,
      y: 10,
      width: 200,
      height: 20,
      fontSize: 14,
      color: '#774B3E',
      content: '',
    },
    description: {
      x: 13,
      y: 30,
      width: 200,
      height: 20,
      fontSize: 12,
      color: '#774B3E',
      content: '',
    },
    process: {
      x: 106,
      y: 40,
      width: 100,
      height: 20,
      fontSize: 10,
      color: '#774B3E',
      content: '{{process}}',
    },
    bindRole: 1,
    limitLogin: 0,
    isFakeTask: 0,
    congratsMode: 1,
  }
};
