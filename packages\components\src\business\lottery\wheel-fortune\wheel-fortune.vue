<template>
  <Resizable
    class="wheel-fortune"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :style="wheelFortuneStyle"
    :id="setting.cid"
  >
    <ResizableProvider class="wheel-fortune-content">
      <!-- 完成进度 -->
      <UiText
        v-if="setting.process.enabled"
        v-model:setting="setting.process"
        :interpolation="processInterpolation"
        :confined="false"
      />

      <!-- 圆形转盘 -->
      <div class="wheel-fortune-container" :style="containerStyle">
        <div 
          v-for="(item, index) in slices" 
          :key="index" 
          class="wheel-fortune-slice"
          :style="item.style"
        >
          <!-- 转盘项 -->
          <div
            class="wheel-fortune-slice-triangle"
            :style="[
              item.triangleStyle,
              {
                borderTopColor:
                  index === activeIndex
                    ? !setting.activeBg
                      ? setting.activeColor
                      : 'transparent'
                    : 'transparent',
                backgroundImage: index === activeIndex && setting.activeBg ? `url(${setting.activeBg})` : 'none',
              },
            ]"
          />

          <Resizable
            v-model:x="setting.box.x"
            v-model:y="setting.box.y"
            v-model:width="setting.box.width"
            v-model:height="setting.box.height"
            :style="{
              backgroundImage: `url(${setting.box.bgImage})`,
              ...item.boxStyle,
              marginLeft: pxTransform(position(index).x),
              marginTop: pxTransform(position(index).y),
            }"
            class="wheel-fortune-box"
          >
            <!-- 奖品图片 -->
            <Resizable
              v-model:x="setting.box.prize.x"
              v-model:y="setting.box.prize.y"
              v-model:width="setting.box.prize.width"
              v-model:height="setting.box.prize.height"
              :class="{ 'wheel-fortune-prize-placeholder': adminStore.editable }"
            >
              <img
                v-if="providedPrizes[index + 0]?.icon"
                :src="providedPrizes[index + 0]?.icon"
                class="wheel-fortune-prize-img"
              />
            </Resizable>
            <!-- 奖品名称 -->
            <UiText
              v-model:setting="setting.box.prizeName"
              :confined="false"
              :scrollspy="false"
              :editableContent="false"
              class="wheel-fortune-prize-name"
            >
              {{ providedPrizes[index + 0]?.name || '--' }}
            </UiText>
          </Resizable>
        </div>
      </div>

      <!-- 抽奖按钮 -->
      <Resizable
        v-if="setting.drawBtn"
        v-model:x="setting.drawBtn.x"
        v-model:y="setting.drawBtn.y"
        v-model:width="setting.drawBtn.width"
        v-model:height="setting.drawBtn.height"
        :confined="false"
        class="wheel-fortune-draw"
        :style="drawBtnWrapStyle"
      >
        <!-- 按钮 -->
        <div
          class="wheel-fortune-draw-btn"
          :class="{ 'wheel-fortune-draw-btn__disabled': !remaining && !setting.drawBtn.noDrawNumBtnImg}"
          :style="drawBtnStyle"
          @click="handleDraw"
        />
        <!-- 剩余抽奖次数 -->
        <UiText
          v-if="setting.drawBtn?.remaining.enabled"
          v-model:setting="setting.drawBtn.remaining"
          :interpolation="remainingInterpolation"
          :confined="false"
        />
      </Resizable>

      <!-- 指针 -->
      <UiImage
        v-if="setting.pointer?.enabled"
        v-model:setting="setting.pointer"
        :confined="false"
        :style="pointerStyle"
      />
    </ResizableProvider>

    <!-- 恭喜获得 -->
    <CongratulationsModal
      v-if="congratulationsRequired && setting.congratulations"
      v-model:setting="setting.congratulations"
      :show="showCongratulations"
      :prize="drawData!"
      @close="handleClose"
    />
  </Resizable>
</template>

<script lang="ts">
import type { CSSProperties } from 'vue';
import type { StatefulComponent } from '@bish/types/src/admin';

export interface WheelFortuneProps {
  /**
   * 配置
   */
  setting: WheelFortuneSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export default {
  name: 'wheel-fortune',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, onUnmounted } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import { useBoxLottery } from '@bish/hooks/src/business/useBoxLottery';
import useLotteryAnimation from '@bish/hooks/src/useLotteryAnimation';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { pxTransform } from '@bish/utils/src/viewport';
import useAdminStore from '@bish/store/src/modules/admin';
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import UiText from '../../../ui/text/index.vue';
import UiImage from '../../../ui/image/index.vue';
import CongratulationsModal from '../../congratulations-modal.vue';
import { navigateScroll } from '../../../__utils/location';
import type { WheelFortuneSetting } from './wheel-fortune';
import useUserStore from '@bish/store/src/modules/user';

const props = withDefaults(defineProps<WheelFortuneProps>(), {
  setting: () => ({} as WheelFortuneSetting),
});

const emits = defineEmits<{
  (event: 'update:setting', value: WheelFortuneSetting): void;
}>();

const setting = useVModel(props, 'setting', emits);

const adminStore = useAdminStore();
const userStore = useUserStore()
const {
  fetchDraw,
  fetchDrawEnd,
  drawData,
  lotteryConfig,
  lotterySchedule,
  process,
} = useBoxLottery(setting.value.lotteryId, setting.value.needBindRole);
const [showCongratulations, toggleShowCongratulations] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showCongratulations' });

const boxGrid = computed(() => {
  return setting.value.boxGrid || 10;
});

const box = computed(() => {
  return Array.from({ length: boxGrid.value }, (_, i) => i);
});

// 这里记录顺时针运动轨迹
const track = computed(() => {
  return box.value;
});

const wheelFortuneStyle = computed<CSSProperties>(() => {
  return {
    backgroundImage: `url(${setting.value.bgImage})`,
    // transform: `rotate(${setting.value.angle}deg)`,
  };
});

const containerStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(setting.value.size),
    height: pxTransform(setting.value.size),
  };
});

/**
 * 计算周长
 * @param radius 圆的半径
 */
const calculateCircumference = (radius: number) => {
  return 2 * Math.PI * radius;
};

// 计算每个扇形的样式
const slices = computed(() => {
  const count = boxGrid.value;
  const radius = Math.ceil(setting.value.size / 2);
  const width = Math.ceil(calculateCircumference(radius) / count); // 近似值

  return box.value.map((_, index) => {
    const rotateAngle = (360 / count) * index + (360 / count / 2) + (setting.value.angle || 0);

    const style: CSSProperties = {
      transform: `translateX(-50%) rotate(${rotateAngle}deg)`,
      transformOrigin: '50% 100% ', // 确保以圆心为旋转基准
    };
    
    const triangleStyle = {
      width: pxTransform(width),
      height: pxTransform(radius),
      borderTop: `${pxTransform(radius)} solid transparent`,
      borderRight: `${pxTransform(width / 2)} solid transparent`,
      borderLeft: `${pxTransform(width / 2)} solid transparent`,
    };
    
    // 为盒子添加反向旋转样式，使其保持正向显示
    const boxStyle: CSSProperties = {
      transform: `rotate(-${rotateAngle}deg)`, // 反向旋转，抵消扇形的旋转
      transformOrigin: 'center center',
    };

    return {
      style,
      triangleStyle,
      boxStyle,
    };
  });
});

const drawBtnStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(setting.value.drawBtn?.width!),
    height: pxTransform(setting.value.drawBtn?.height!),
    backgroundImage: `url(${
      setting.value.drawBtn?.noDrawNumBtnImg && !remaining.value && userStore.userData.token
        ? setting.value.drawBtn.noDrawNumBtnImg
        : setting.value.drawBtn?.bgImage
    })`,
  };
});

const drawBtnWrapStyle = computed<CSSProperties>(() => {
  return {
    zIndex: 3,
  };
});

const pointerStyle = computed<CSSProperties>(() => {
  return {
    zIndex: 2,
    transform: `rotate(${buttonRotation.value}deg)`,
  };
});

const providedPrizes = computed(() => {
  return lotteryConfig.value?.lottery_prize || [];
});

const congratulationsRequired = computed(() => {
  // 兼容旧数据
  return props.setting.congratulations?.required ?? true;
});

const remaining = computed(() => {
  return lotterySchedule.value?.lottery_remain_num || 0;
});

const processInterpolation = computed(() => {
  return {
    process: process.value,
  };
});

const remainingInterpolation = computed(() => {
  return {
    num: remaining.value || '0',
  };
});

const handleAnimationEnd = () => {
  if (!drawData.value) {
    return;
  }
  if (congratulationsRequired.value) {
    toggleShowCongratulations(true);
  } else {
    showToast(`恭喜获得 ${drawData.value?.name}`);
  }
  fetchDrawEnd();
};

const { activeIndex, buttonRotation, start, cleanupTimers } = useLotteryAnimation(
  box.value,
  track.value,
  providedPrizes,
  drawData,
  handleAnimationEnd,
);

const run = async () => {
  start();
};

const handleDraw = async () => {
  // 先执行接口获取奖品id，之后再跑动画
  // 没有抽奖次数也可以在 beforeStart 中自行处理
  const res = await fetchDraw();
  // 没有抽奖次数
  if (!res || !remaining.value) {
    if (setting.value.drawBtn?.notDrawNumTip) {
      showToast(setting.value.drawBtn?.notDrawNumTip)
    }
    // 没有抽奖次数，滚动到指定位置
    const cid = setting.value.drawBtn?.notDrawNumUrl
    if (cid) {
      navigateScroll(cid)
    }
    return;
  }
  // 格子数为1，直接结束，这里类似领取任务奖励
  if (boxGrid.value === 1) {
    handleAnimationEnd();
    return;
  }
  run();
};

const handleClose = () => {
  toggleShowCongratulations(false);
};

// 在组件卸载时清理定时器
onUnmounted(() => {
  cleanupTimers();
});

const position = (index: number) => {
  return props.setting.boxPosition?.[index] || { x: 0, y: 0 };
};
</script>

<style lang="less">
.wheel-fortune {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  
  &-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-container {
    border-radius: 50%;
    position: relative;
    overflow: hidden;
  }

  &-slice {
    position: absolute;
    top: 0;
    left: 50%;

    &-triangle {
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-origin: border-box; /* 设置背景图从边框开始绘制 */
    }
  }

  &-prize {
    &-img {
      width: 100%;
      height: 100%;

    }
    
    &-name {}
    
    &-placeholder {
      background-color: #f4f4f4;
    }
  }

  &-box {
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  &-draw {
    &-btn {
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform-origin: center center; // 确保按钮围绕中心旋转

      &__disabled {
        filter: grayscale(100%);
      }
    }
  }
}
</style>