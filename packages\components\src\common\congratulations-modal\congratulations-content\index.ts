import type { CongratulationsContentSetting } from './congratulations-content';

export * from './congratulations-content';

export const defaultConfig = (): CongratulationsContentSetting => {
  return {
    prizeImg: {
      x: 126,
      y: 90,
      width: 120,
      height: 120,
      show: true,
    },
    prizeName: {
      x: 38,
      y: 229,
      width: 300,
      height: 35,
      fontSize: 20,
      color: '#FFE08D',
      align: 'center',
      content: '{{prizeName}}',
    },
    tip: {
      x: 131,
      y: 260,
      width: 113,
      height: 35,
      fontSize: 12,
      color: '#FFE08D',
      align: 'center',
    },
    gameItemTip: '请到游戏内查收奖品',
    redPacketTip: '请到我的奖励内查看',
    otherTip: '请添加企微领取奖品',
    okBtn: {
      x: 119,
      y: 288,
      width: 143,
      height: 49,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-7/1722967840752_.png',
    },
    addressBtn: {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      imgLink: '',
      enabled: false,
    },
    withdrawBtn: {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      imgLink: '',
      enabled: false,
    },
  };
}; 
