import type { TaskCollectionCommon } from '../index'
import type { ImageSetting } from '../../../../ui/image/index.vue'
import type { PopupSetting } from '../../../../common/popup.vue';

export type TaskQrCodeModal = PopupSetting & {
  /**
   * 二维码
   */
  qrCode: ImageSetting
  /**
   * 登录按钮
   */
  loginBtn: ImageSetting
}

/**
 * 任务-二维码（企微好友、公众号...）
 */
export type TaskQrCodeSetting = TaskCollectionCommon & {
  /**
   * 弹窗配置
   */
  modalSetting: TaskQrCodeModal
  /**
   * 任务完成提示
   */
  completeTip: string
}