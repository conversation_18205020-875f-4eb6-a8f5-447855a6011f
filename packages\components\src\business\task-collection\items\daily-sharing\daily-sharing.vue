<!--
* @Description: 每日分享任务
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="task-item"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 标题 -->
      <UiText v-if="setting.title?.content" v-model:setting="setting.title" />

      <!-- 描述 -->
      <UiText v-if="setting.description?.content" v-model:setting="setting.description" />
      
      <!-- 完成进度 -->
      <UiText
        v-if="setting.process"
        v-model:setting="setting.process"
        :interpolation="processInterpolation"
        :confined="false"
      />

      <!-- 去分享 -->
      <UiImage
        :setting="setting.claimBtn"
        @click="handleClaim"
        :confined="false"
      />
      <!-- 去分享：微信小程序环境 -->
      <button
        v-if="showShareBtn"
        :style="shareBtnStyle"
        class="task-item-wx-btn"
        open-type="share"
        @click="uploadTask"
      />
    </ResizableProvider>

    <!-- 分享引导 -->
    <ShareGuide
      v-if="setting.shareGuide"
      :show="openShareGuide"
      v-model:setting="setting.shareGuide"
      @close="() => toggleShareGuide(false)"
    />
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'task-daily-sharing',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { postActivityTaskUploadTask } from '@bish/api/src/activity';
import { sleep, userAgent } from '@bish/utils/src/utils';
import { pxTransform } from '@bish/utils/src/viewport';
import { useLog } from '@bish/hooks/src/useLog';
import { defaultConfig } from './index';
import type { TaskDailySharingSetting } from './daily-sharing';
import Resizable from '../../../../ui/resizable/index.vue';
import ResizableProvider from '../../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../../ui/image/index.vue';
import UiText from '../../../../ui/text/index.vue';
import ShareGuide from '../../../../business/share-guide/share-guide.vue';

export interface TaskCollectionProps {
  /**
   * 配置
   */
  setting: TaskDailySharingSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TaskCollectionProps>(), {
  setting: () => defaultConfig(),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskDailySharingSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityStore = useActivityStore();

const setting = useVModel(props, 'setting', emits);
const { uploadLog } = useLog();

const [openShareGuide, toggleShareGuide] = useControllableStatus(props, emits, { fieldName: 'showShareGuide' });

const { isUserAgentType } = userAgent();

// 微信小程序环境
const isWeapp = computed(() => {
  return isUserAgentType === 'WX_MINI';
});

const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

const userTask = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
});

const process = computed(() => {
  return `${userTask.value?.progress_num || 0}/${taskConfig.value?.target_num || 0}`;
});

const processInterpolation = computed(() => {
  return {
    process: process.value,
  };
});

const showShareBtn = computed(() => {
  return isWeapp.value && (!setting.value.needLogin || activityStore.activityAccountInfo?.account_id) && !activityStore.isSummit(false);
});

const shareBtnStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.claimBtn.x || 0),
    top: pxTransform(setting.value.claimBtn.y || 0),
    width: pxTransform(setting.value.claimBtn.width || 0),
    height: pxTransform(setting.value.claimBtn.height || 0),
  };
});

const uploadSharingLog = () => {
  // 事件上报：点击前往分享
  uploadLog({
    event_name: 'click',
    click_id: 3,
    click_type: 3,
    // 被曝光的资源类型，1每日分享 2分享任务
    // 每日分享不需要登录就可以
    exposure_type: setting.value.needLogin ? 2 : 1,
  });
};

const uploadTask = async () => {
  if (!taskConfig.value) {
    return;
  }
  uploadSharingLog();
  if (!activityStore.activityAccountInfo?.account_id) {
    return;
  }
  try {
    await postActivityTaskUploadTask({
      act_id: activityStore.activityInfo?.init?.id,
      act_acc_id: activityStore.activityAccountInfo?.act_acc_id,
      task_code: taskConfig.value?.task_code,
      task_id: taskConfig.value?.id,
    });
    // 重新获取用户与组件的数据
    await sleep(2000);
    activityStore.getComponentWithUserInfo();
  } catch (error) {
    console.warn('上报任务完成失败', error);
  }
};

const handleClaim = () => {
  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfig.value) {
    showToast('活动已结束~');
    return;
  }
  uploadSharingLog();
  if (setting.value.needLogin) {
    const pass = activityStore._checkIn(!!setting.value.bindRole);
    if (!pass) {
      return;
    }
  }
  if (activityStore.isSummit()) {
    return;
  }
  if (isUserAgentType === 'WX_MINI') {
    // TODO: 拉起分享列表
  } else {
    toggleShareGuide(true);
  }
  
  uploadTask();
};

const handleClose = () => {
  // toggle(false);
};
</script>

<style>
.task-item {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.task-item-wx-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: transparent;
}

.task-item-wx-btn::after {
  border: none;
}
</style>
