import type { CollectCardsAskForSetting } from './collect-cards-ask-for';

export * from './collect-cards-ask-for';

export const defaultConfig = (): CollectCardsAskForSetting => {
  return {
    actionBtn: {
      x: 18,
      y: 327,
      width: 161,
      height: 39,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744367729654-ask-for-btn.png',
      enabled: true,
    },
    modal: {
      x: 0,
      y: 0,
      width: 375,
      height: 382,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744289519671-scratch-bg.png',
      closeBtn: {
        x: 59,
        y: 349,
        width: 119,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744367815067-close-1.png',
      },
      okBtn: {
        x: 199,
        y: 349,
        width: 119,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744367818256-give.png',
      },
      title: {
        x: 56,
        y: -46,
        width: 264,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744367733776-give-title.png',
        enabled: true,
      }, 
      box: {
        x: 78,
        y: 5,
        width: 217,
        height: 303,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744363647725-card-box.png',
        enabled: true,
      },
      card: {
        x: 94,
        y: 14,
        width: 186,
        height: 287,
      },
      name: {
        x: 0,
        y: 316,
        width: 375,
        height: 20,
        fontSize: 13,
        color: '#FFF8DC',
        align: 'center',
        fontWeight: true,
        enabled: true,
      },
      current: {
        x: 0,
        y: -18,
        width: 375,
        height: 20,
        fontSize: 12,
        color: '#FFF8DC',
        align: 'center',
        fontWeight: true,
        enabled: true,
        content: '当前可赠送卡片{{count}}张',
      },
    },
    noCardModal: {
      x: 0,
      y: 0,
      width: 375,
      height: 382,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250410/1744289519671-scratch-bg.png',
      closeBtn: {
        x: 119,
        y: 349,
        width: 139,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744364083452-close-2.png',
      },
      title: {
        x: 56,
        y: -29,
        width: 264,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744367733776-give-title.png',
        enabled: true,
      }, 
      box: {
        x: 78,
        y: 5,
        width: 217,
        height: 303,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250411/1744363647725-card-box.png',
        enabled: true,
      },
      card: {
        x: 94,
        y: 14,
        width: 186,
        height: 287,
      },
      name: {
        x: 0,
        y: 316,
        width: 375,
        height: 20,
        fontSize: 13,
        color: '#FFF8DC',
        align: 'center',
        fontWeight: true,
        enabled: true,
      },
      tip: {
        x: 0,
        y: 88,
        width: 186,
        height: 90,
        fontSize: 11,
        color: '#FFFFFF',
        align: 'center',
        alignItems: 'center',
        content: '<div style="line-height: 1.5;">您当前未拥有该卡</br>或未有满足赠送条件的卡片，</br>无法赠送。</br>收集更多卡片或卡片刮奖后再赠送吧~</div>',
        enabled: true,
      },
    },
    copyTemplates: [
      {
        cardId: 0,
        text: '求卡~还差这张“灵宠貔貅卡”',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770683370-ask-for-poster-1.jpg',
      },
      {
        cardId: 0,
        text: '求卡~还差这张“招财猫卡”',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770685749-ask-for-poster-2.jpg',
      },
      {
        cardId: 0,
        text: '求卡~还差这张“西域商人卡”',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770688382-ask-for-poster-3.jpg',
      },
      {
        cardId: 0,
        text: '求卡~还差这张“易宝生财卡”',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770723761-ask-for-poster-4.jpg',
      },
      {
        cardId: 0,
        text: '求卡~还差这张“黄金时装卡”',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770726161-ask-for-poster-5.jpg',
      },
      {
        cardId: 0,
        text: '求卡~还差这张“财宝瓜分卡”',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770728506-ask-for-poster-6.jpg',
      },
      {
        cardId: 0,
        text: '求卡~还差这张“一鉴暴富卡”',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770758793-ask-for-poster-7.jpg',
      },
      {
        cardId: 0,
        text: '求卡~还差这张“寻金西市卡”',
        poster: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250416/1744770761134-ask-for-poster-8.jpg',
      },
    ],
  };
}; 
