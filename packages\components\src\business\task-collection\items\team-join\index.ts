import { commonTaskConfig } from '../index';
import type { TaskTeamJoinSetting } from './team-join';

export * from './team-join';

export const defaultConfig = (): TaskTeamJoinSetting => {
  return {
    ...commonTaskConfig(),
    process: {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      fontSize: 9,
      color: '#A1564C',
      content: '{{process}}',
      align: 'center',
    },
    width: 287,
    height: 63,
    taskId: 0,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250116/1736996842968-task-team.png',
    activeBgImage: '',
    doBtn: {
      x: 206,
      y: 20,
      width: 61,
      height: 23,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250116/1736996852884-tasl-team-join.png',
      enabled: true,
    },
    claimBtn: {
      x: 206,
      y: 20,
      width: 61,
      height: 23,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250120/1737375094345-join-claim.png',
      enabled: false,
    },
    achievedBtn: {
      x: 206,
      y: 20,
      width: 61,
      height: 23,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250123/1737612696102-achieved-btn.png',
      enabled: false,
    },
    formedBtn: {
      x: 206,
      y: 20,
      width: 61,
      height: 23,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250116/1736996862310-task-team-ed.png',
      enabled: true,
    },
    num: {
      x: 200,
      y: 41,
      width: 80,
      height: 20,
      fontSize: 9,
      color: '#A1564C',
      align: 'center',
      alignItems: 'center',
      content: '队伍人数：{{num}}',
      enabled: false,
    },
    teamId: 0,
  }
};