<!--
* @Description: 分享模式：海报
-->
<template>
  <div class="invite-modal-poster">
    <!-- 分享模式：海报 -->
    <Popup
      z-index="99"
      :show="show"
      :lock-scroll="false"
      :contentStyle="popupContentStyle"
      v-model:setting="setting"
      @close="handleClose"
      @click-overlay="handleClose"
    >
      <!-- 海报底图 -->
      <Resizable
        v-model:x="setting.posterBg.x"
        v-model:y="setting.posterBg.y"
        v-model:width="setting.posterBg.width"
        v-model:height="setting.posterBg.height"
      >
        <div
          ref="mainRef"
          class="invite-modal-poster-content"
          :style="posterContentStyle"
        >
          <!-- 二维码 -->
          <Resizable
            v-model:x="setting.qrCode.x"
            v-model:y="setting.qrCode.y"
            v-model:width="setting.qrCode.width"
            v-model:height="setting.qrCode.height"
          >
            <img v-if="qrCode || adminStore.editable" class="invite-modal-poster-qrCode" :src="qrCode" />
          </Resizable>
        </div>

        <!-- 生成的海报 -->
        <img
          v-if="imgUrl"
          :src="imgUrl"
          :style="posterImgStyle"
          class="invite-modal-poster-poster-img"
        />
      </Resizable>

      <!-- 提示文案 -->
      <UiText
        v-if="setting.tip?.enabled"
        v-model:setting="setting.tip"
        :confined="false"
      />

      <!-- 保存按钮 -->
      <UiImage
        v-if="showSaveBtn"
        v-model:setting="setting.saveBtn"
        :confined="false"
        @click="handleSava"
      />
    </Popup>
  </div>
</template>

<script lang="ts">
export default {
  name: 'invite-modal-poster',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, computed, watch, nextTick } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import QRCode from 'qrcode';
import { domToPng } from 'modern-screenshot';
import useAdminStore from '@bish/store/src/modules/admin';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import { useLog } from '@bish/hooks/src/useLog';
import { pxTransform } from '@bish/utils/src/viewport';
import { downloadImage } from '@bish/utils/src/downloadUtils';
import UiImage from '../../../ui/image/index.vue';
import UiText from '../../../ui/text/index.vue';
import Resizable from '../../../ui/resizable/index.vue';
import Popup from '../../../common/popup.vue';
import type { InviteModalPosterSetting } from './invite-modal-poster';
import { defaultConfig } from './index';

export interface InviteModalPosterProps {
  /**
   * 配置
   */
  setting: InviteModalPosterSetting
  /**
   * 是否显示
   */
  show: boolean
  /**
   * 短链
   */
  shortLink: string
}

const props = withDefaults(defineProps<InviteModalPosterProps>(), {
  setting: defaultConfig,
  show: false,
  bindRole: true,
});

const emits = defineEmits<{
  (event: 'update:setting', value: InviteModalPosterSetting): void;
  (event: 'close'): void;
}>();

const qrCode = ref('');
const imgUrl = ref('');
const mainRef = ref<HTMLDivElement | null>(null);
const generatingShareImg = ref(false);

const setting = useVModel(props, 'setting', emits);

const adminStore = useAdminStore();
const userStore = useUserStore();
const activityStore = useActivityStore();

const { uploadLog } = useLog();

const showSaveBtn = computed(() => {
  return imgUrl.value || adminStore.editable;
});

const posterContentStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(setting.value.posterBg.width || 0),
    height: pxTransform(setting.value.posterBg.height || 0),
    backgroundImage: `url(${setting.value.posterBg.imgLink})`,
  };
});

const posterImgStyle = computed<CSSProperties>(() => {
  return {
    left: 0,
    top: 0,
    width: pxTransform(setting.value.posterBg.width || 0),
    height: pxTransform(setting.value.posterBg.height || 0),
  };
});

const popupContentStyle = computed<CSSProperties>(() => {
  return {
    marginTop: pxTransform(20),
    marginBottom: pxTransform(90),
  }
});

const generateQR = async (url: string) => {
  try {
    const res = await QRCode.toDataURL(url);
    if (res) {
      qrCode.value = res;
    }
  } catch (err) {
    console.warn('创建二维码失败', err);
  }
};

const generateShareImg = async () => {
  try {
    if (!mainRef.value) {
      return;
    }
    const start = +new Date();
    generatingShareImg.value = true;

    const dataUrl = await domToPng(mainRef.value, {
      // pixelRatio: 1,
      quality: 1,
      scale: 3,
      fetch: {
        // requestInit: {
        //   mode: 'no-cors',
        // },
      },
    });
    const end = +new Date();
    console.log('渲染耗时海报：', end - start);
    return dataUrl;
  } catch (err) {
    console.warn('html2canvas失败', err);
  } finally {
    generatingShareImg.value = false;
  }
};

const resetShare = () => {
  imgUrl.value = '';
};

const generateShare = async () => {
  // fixbug: 这里在 ios 有时候生成图片不会把背景带进去
  await nextTick();
  const res = await generateShareImg();
  if (res) {
    imgUrl.value = res;
  }
};

watch(
  [() => props.shortLink, () => props.show],
  async ([newShortLink, newShow]) => {
    if (newShortLink && newShow) {
      await generateQR(newShortLink);
      resetShare();
      generateShare();
    }
  }
);

const handleClose = () => {
  emits('close');
};

const handleSava = () => {
  if (!imgUrl.value) {
    return;
  }
  // 事件上报：点击保存海报
  uploadLog({
    event_name: 'click',
    click_id: 43,
    click_type: 3,
  });
  downloadImage(imgUrl.value, '分享海报');
};
</script>

<style>
.invite-modal-poster-content {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.invite-modal-poster-qrCode {
  width: 100%;
  height: 100%;
}

.invite-modal-poster-poster-img {
  position: absolute;
  opacity: 0;
}
</style>