<template>
  <ServerRolePicker
    v-if="show"
    v-model="serverRole"
    :open="openBindRoleModal"
    :role="activityStore.bindRoleInfo"
    :server-options="serverOptions"
    :role-options="roleOptions"
    @confirm="handleConfirm"
    @update:open="handleOpenChange"
    v-bind="attrs"
  />
</template>

<script lang="ts">
import type { AllRoleInfo, AllRoleInfoRoleList } from '@bish/api/src/user';
import type { ServerRoleSetting, ServerRoleValue, ServerRoleOptions } from './server-role-picker';
import type { ServerRoleProps } from './server-role-picker.vue';
import { defaultConfig as ServerRolePickerDefaultConfig } from './server-role-picker.vue';

// fix: 直接导出在 uni 编译错误，无法创建组件对应的 js 文件
// export { defaultConfig } from './server-role-picker.vue';

export interface BindRoleSetting extends ServerRoleSetting {}

type BindServerInfo = {
  server_id: string,
  server_name: string,
}

type BindRoleInfo = AllRoleInfoRoleList[0]

export const defaultConfig = (): BindRoleSetting => {
  return ServerRolePickerDefaultConfig();
};

export default {
  name: 'bind-role',
}
</script>

<script lang="ts" setup>
import { ref, computed, watch, useAttrs } from 'vue';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useActivityStore from '@bish/store/src/modules/activity';
import useUserStore from '@bish/store/src/modules/user';
import usePopupStore from '@bish/store/src/modules/popup';
import useAdminStore from '@bish/store/src/modules/admin';
import { postActivityBindAccRole } from '@bish/api/src/activity';
import { sleep } from '@bish/utils/src/utils';
import { showLoadingToast } from '@bish/ui/src/toast';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import type { StatefulComponent } from '@bish/types/src/admin';
import ServerRolePicker from './server-role-picker.vue';

export interface BindRoleProps {
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<BindRoleProps>(), {});

const emits = defineEmits<{
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

// 这里在 uni 小程序，手动透传一下
const attrs = useAttrs();

const serverRole = ref<ServerRoleValue>({
  server: [],
  role: [],
});
// 角色选项
const roleOptions = ref<ServerRoleOptions>([]);

const activityPageStore = useActivityPageStore();
const activityStore = useActivityStore();
const userStore = useUserStore();
const popupStore = usePopupStore();
const adminStore = useAdminStore();

const [openBindRoleModal, toggleOpenBindRoleModal] = useControllableStatus(props, emits, { fieldName: 'showBindRoleModal' });

let binding = false;

// 区服选项
const serverOptions = computed(() => {
  const { setting } = attrs as unknown as ServerRoleProps;
  const { limitServer, invitee } = setting;
  let roleInfo: AllRoleInfo = activityStore.allRoleInfo;

  if (limitServer && !invitee) {
    roleInfo = activityStore.limitServerRole;
  }
  if (limitServer && invitee) {
    if (activityStore.activityAccountInfo.invited_status === 1) {
      roleInfo = activityStore.limitServerRole;
    }
  }
  const serverList = roleInfo.map((item) => ({
    text: item.server_name,
    value: item.server_id,
  }));
  return serverList;
})

// 是否显示绑定角色
const show = computed(() => {
  // 这里后台组件配置特殊逻辑
  if (adminStore.editable) {
    return true;
  }
  // 未登录
  if (!userStore.isLogined) {
    return false;
  }
  // 开服时间的判断基于已获取到活动初始化数据
  if (!activityStore.activityInfo.init) {
    return false;
  }
  // 配置了开服时间、未开服、未绑角
  if (!activityStore.getServerOpenState() && !activityStore.activityAccountInfo?.role_info) {
    return false;
  }
  return true;
})

watch(
  () => popupStore.showServerRole,
  (newVal) => {
    toggleOpenBindRoleModal(newVal);
  },
);

watch(
  () => serverRole.value.server,
  (newVal, oldVal?) => {
    if (newVal?.[0] !== oldVal?.[0]) {
      handleServerChange(newVal);
    }
  },
);

watch(
  () => activityStore.bindRoleInfo,
  (newVal) => {
    if (newVal) {
      serverRole.value.server = newVal.server_name ? [`${newVal.platform}_${newVal.zone_id}`] : []
      // 这里玩家角色列表返回的 role_id 是 number 类型，获取活动账号信息返回的 role_info 中的 role_id 又是 string 类型
      // 所以在这统一一下类型
      serverRole.value.role = newVal.role_id ? [+newVal.role_id] : []
    }
  },
  { immediate: true },
);

// 区服选项改变时候，重新获取角色列表
const handleServerChange = (value: ServerRoleValue['server']) => {
  if (value.length) {
    const current = activityStore.allRoleInfo?.find((item) => item.server_id === value[0]);
    if (current) {
      roleOptions.value = current.role_list.map((item) => ({
        text: `${item.role_name} Lv${item.role_level}`,
        value: item.role_id,
      }));
    }
  } else {
    roleOptions.value = [];
  }
};

// 获取玩家所有角色信息 与 获取活动账号信息 接口不是同一个，所以存在一个时机问题
// 这里获取到玩家所有角色信息之后，触发一次区服选项改变回调
watch(
  () => activityStore.allRoleInfo,
  () => {
    if (serverRole.value.server.length) {
      handleServerChange(serverRole.value.server);
    }
  },
  { immediate: true },
);

const handleOpenChange = (value: boolean) => {
  if (value) {
    if (activityStore.checkCharacterBinding()) {
      popupStore.setShowServerRole(true);
    }
  } else {
    // 同步数据
    if (openBindRoleModal && !popupStore.showServerRole) {
      toggleOpenBindRoleModal(false);
    }
    popupStore.setShowServerRole(false);
  }
};

// 绑定游戏角色
const bindActivityAccRole = async (server: BindServerInfo, role: BindRoleInfo) => {
  if (!server || !role) {
    return;
  }
  if (binding) {
    return;
  }
  const toast = showLoadingToast({
    duration: 0,
    forbidClick: true,
  })
  binding = true;
  try {
    const platform = server.server_id.split('_')[0];
    const zoneId = server.server_id.split('_')[1];
    const res = await postActivityBindAccRole({
      act_id: activityPageStore.activityPageConfig.activity_id,
      act_acc_id: activityStore.activityAccountInfo.act_acc_id ? `${activityStore.activityAccountInfo.act_acc_id}` : undefined!,
      platform,
      zone_id: +zoneId,
      role_id: role.role_id,
      role_name: role.role_name,
      server_name: server.server_name,
      role_score: role.role_score,
      vip: role.role_vip_lev,
      role_level: role.role_level,
      reg_time: `${role.reg_time}`,
      career: role.career,
      sex: role.sex,
      last_login_time: role.login_time,
    });
    if (res?.code === 0) {
      // showToast('绑定成功~')
      // 执行绑定角色回调
      userStore.workBindRoleTask();
      // 延迟1秒，确保绑定任务完成后，刷新到
      await sleep(1000);
    }
    // 重新获取相关数据
    await activityStore.getActivityAccountInfo();
    // 待活动数据获取后，在隐藏，否则用户可能在期间触发操作，再次唤起绑定角色弹窗
    popupStore.setShowServerRole(false);
  } catch (error) {
    console.warn('绑定角色失败', error);
  } finally {
    toast?.close();
    // 延迟到弹窗关闭后
    setTimeout(() => {
      binding = false;
    }, 400);
  }
}

const handleConfirm = (value: ServerRoleValue) => {
  const { server, role } = value;
  let curServer = null;
  let curRole = null;
  if (activityStore.allRoleInfo) {
    const current = activityStore.allRoleInfo.find(item => item.server_id === server[0]);
    if (current) {
      curServer = {
        server_id: current.server_id,
        server_name: current.server_name,
      };
    }
    if (current) {
      curRole = current.role_list.find(item => item.role_id === role[0]);
    }
  }
  if (curServer && curRole) {
    bindActivityAccRole(curServer, curRole);
  }
};

</script>

<style lang="less">
.bind-role {}
</style>