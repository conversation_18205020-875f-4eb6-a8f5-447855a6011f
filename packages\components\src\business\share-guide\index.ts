import type { ShareGuideSetting } from './share-guide';

export * from './share-guide';

export const defaultConfig = (): ShareGuideSetting => {
  return {
    title: {
      x: 47,
      y: 70,
      width: 275,
      height: 97,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250121/1737444670208-daily-sharing-tip.png',
    },
    gotBtn: {
      x: 185,
      y: 306,
      width: 120,
      height: 31,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250121/1737440980472-got-btn.png',
      enabled: false,
    },
    copyLinkBtn: {
      x: 127,
      y: 183,
      width: 112,
      height: 37,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250121/1737440581244-daily-sharing-copy.png',
      enabled: true,
    },
    closeBtn: {
      x: 166,
      y: 236,
      width: 35,
      height: 33,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250114/1736863567849-close.png',
      enabled: true,
    },
  };
};