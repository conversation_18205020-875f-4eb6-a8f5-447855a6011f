<!--
* @Description: 基础组队-队伍成员
-->
<template>
  <Teammate
    v-model:setting="setting"
    :members="members"
    @add="handleAdd"
  />
</template>

<script lang="ts">
export default {
  name: 'team-members',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import usePopupStore from '@bish/store/src/modules/popup';
import { useLog } from '@bish/hooks/src/useLog';
import type { ComponentWithUserInfoMembers } from '@bish/api/src/activity';
import { showToast } from '@bish/ui/src/toast';
import type { TeamMembersSetting } from './team-members';
import { defaultConfig } from './index';
import Teammate from '../../teammate/index.vue';

export interface TeamMembersProps {
  /**
   * 配置
   */
  setting: TeamMembersSetting
}

const props = withDefaults(defineProps<TeamMembersProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TeamMembersSetting): void;
}>();

const setting = useVModel(props, 'setting', emits);

const activityStore = useActivityStore();
const popupStore = usePopupStore();

const { uploadLog } = useLog();

// 队伍信息配置信息
const teamConfig = computed(() => {
  const { team } = activityStore.activityInfo.config || {};
  return team?.find(item => item.id === setting.value.teamId);
});

// 与配置对应的队伍信息
const currenTeam = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.team?.find(item => item.id === setting.value.teamId);
});

const members = computed(() => {
  let teamMembers: ComponentWithUserInfoMembers | undefined = currenTeam.value?.members || [];
  const slotItem = {
    role_name: '邀请好友',
  } as unknown as ComponentWithUserInfoMembers;
  
  // 补位
  teamMembers = [
    ...teamMembers,
    slotItem,
    slotItem,
    slotItem,
    slotItem,
    slotItem,
  ].slice(0, teamConfig.value?.number_limit || setting.value.teamNum) as ComponentWithUserInfoMembers;
  return teamMembers;
});

const handleAdd = () => {
  // 找不到队伍配置（队伍下架了）
  if (!teamConfig.value) {
    showToast('活动已结束~');
    return;
  }

  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }
  if (!currenTeam.value?.team_id) {
    popupStore.setShowTeamCreate(true);
  } else {
    popupStore.setShowTeamInvite(true);
  }
};
</script>

<style>
.team-members {}
</style>