import { defaultConfig as serverRolePickerDefaultConfig } from '../../../business/server-role-picker.vue';
import { defaultConfig as downloadGameDefaultConfig } from '../../../business/download-game.vue';
import { defaultConfig as itemsDefaultConfig } from '../../../business/items.vue';
import type { PointsGiftPackSetting } from './points-gift-pack';

export * from './points-gift-pack';

export const defaultConfig = (): PointsGiftPackSetting => {
  const serverRolePickerConfig = serverRolePickerDefaultConfig();
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 217,
    bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-9-3/1725345874340_.png',
    gifPackId: 0,
    items: itemsDefaultConfig(),
    serverRolePicker: {
      ...serverRolePickerConfig,
      selector: {
        ...serverRolePickerConfig.selector,
        x: 37,
        y: 119,
        color: '#505924',
        fontSize: 10,
        switchColor: '#1C9BD0',
      },
    },
    receiveBtnImage: {
      x: 51,
      y: 150,
      width: 127,
      height: 38,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-9-3/1725345437950_.png',
    },
    receivedBtnImage: {
      x: 51,
      y: 150,
      width: 127,
      height: 38,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-9-3/1725345440919_.png',
    },
    downloadBtn: {
      ...downloadGameDefaultConfig(),
      x: 198,
      y: 150,
      width: 127,
      height: 38,
      imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-9-3/1725345444453_.png',
      enabled: true,
    },
    bindRole: 0,
  }
}
