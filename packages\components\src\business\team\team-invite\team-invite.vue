<!--
* @Description: 基础组队-邀请组队
-->
<template>
  <Resizable
    class="team-invite"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
  >
    <ResizableProvider>
      <!-- 立即邀请 -->
      <UiImage
        v-if="!showFullBtn"
        v-model:setting="setting.inviteBtn"
        :class="{ 'team-invite-btn__disabled': !teamConfig }"
        @click="handleInvite"
      />
      <!-- 已满员 -->
      <UiImage
        v-if="showFullBtn"
        v-model:setting="setting.fullBtn"
      />
    </ResizableProvider>

    <!-- 邀请组队弹窗 -->
    <InviteModalPro
      :show="openInviteTeammates"
      v-model:setting="setting.inviteCard"
      :teamId="currenTeam?.team_id || 0"
      @close="handleCloseInvite"
    />

    <!-- 接受队伍弹窗 -->
    <InvitationModal
      :show="showInvitation"
      v-model:setting="setting.invitationCard"
      :inviter="inviter"
      @close="() => toggleInvitation(false)"
      @ok="handleJoin"
    />

    <!-- 微信小程序内嵌-引导右上角分享 -->
    <ShareGuide
      v-if="setting.shareGuide"
      :show="openWeappShareGuide"
      v-model:setting="setting.shareGuide"
      @close="handleCloseShareGuide"
    />

    <!-- 加入队伍失败弹窗 -->
    <Popup
      v-if="setting.joinFailedModal?.enabled"
      z-index="99"
      :show="openJoinFailedModal"
      v-model:setting="setting.joinFailedModal"
      @close="() => toggleJoinFailedModal(false)"
      @ok="handleCreateTeam"
    >
      <!-- 文本内容 -->
      <UiText
        v-if="setting.joinFailedModal.content.enabled"
        v-model:setting="setting.joinFailedModal.content"
      />
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'team-invite',
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, computed, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import usePopupStore from '@bish/store/src/modules/popup';
import useAdminStore from '@bish/store/src/modules/admin';
import { useLog } from '@bish/hooks/src/useLog';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { useTeamInvitation } from '@bish/hooks/src/business/useTeamInvitation';
import useRouteQuery from '@bish/hooks/src/useRouteQuery';
import { joinTeam } from '@bish/api/src/team-gift';
import { userAgent } from '@bish/utils/src/utils';
import localStorage from '@bish/utils/src/storage/localStorage';
import { useActivityPageConfig } from '@bish/hooks/src/business/useActivityPageConfig';
import type { ShareMessageConfigType } from '@bish/hooks/src/business/useActivityPageConfig';
import ShareGuide from '../../share-guide/share-guide.vue';
import Resizable from '../../../ui/resizable/index.vue';
import UiImage from '../../../ui/image/index.vue';
import UiText from '../../../ui/text/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import InviteModalPro from '../../invite-modal-pro/invite-modal-pro.vue';
import InvitationModal from '../../invitation-modal.vue';
import Popup from '../../../common/popup.vue';
import type { TeamInviteSetting } from './team-invite';
import { defaultConfig } from './index';

export interface TeamInviteProps {
  /**
   * 配置
   */
  setting: TeamInviteSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TeamInviteProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TeamInviteSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const joinFlag = ref(false);
const loginFlag = ref(false);

const setting = useVModel(props, 'setting', emits);

const [openInviteTeammates, toggleInviteTeammates] = useControllableStatus(props, emits, { fieldName: 'showInviteTeammates' });
const [openWeappShareGuide, toggleWeappShareGuide] = useControllableStatus(props, emits, { fieldName: 'showWeappShareGuide' });
const [openJoinFailedModal, toggleJoinFailedModal] = useControllableStatus(props, emits, { fieldName: 'showJoinFailedModal' });

const { showInvitation, toggleInvitation } = useTeamInvitation(props.setting.teamId, props, emits, { fieldName: 'showJoinTeam' });

const userStore = useUserStore();
const activityStore = useActivityStore();
const popupStore = usePopupStore();
const adminStore = useAdminStore();

const { uploadLog } = useLog();

const { query } = useRouteQuery();

const { isUserAgentType } = userAgent();

const { setWeappShareMessage } = useActivityPageConfig();

// 队伍信息配置信息
const teamConfig = computed(() => {
  const { team } = activityStore.activityInfo.config || {};
  return team?.find(item => item.id === setting.value.teamId);
});

// 与配置对应的队伍信息
const currenTeam = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.team?.find(item => item.id === setting.value.teamId);
});

const members = computed(() => {
  return currenTeam.value?.members || [];
});

/**
 * 是否是队长
 */
const isCaptain = computed(() => {
  const current = members.value.find(item => +item.account_id === +userStore.userData.account_id);
  return current?.change_type === 1;
});

const inviter = computed(() => {
  return query.value.invite_user ? decodeURIComponent(query.value.invite_user as string) : '';
});

const showFullBtn = computed(() => {
  if (!teamConfig.value || !currenTeam.value?.members) {
    return false;
  }
  const max = currenTeam.value?.members.length >= teamConfig.value?.number_limit;
  return setting.value.fullBtn.enabled && max;
});

// 进行登录操作后，如果用户没有绑定角色，则自动弹出绑定角色弹窗
watch(
  () => activityStore.activityAccountInfo,
  (newVal) => {
    if (loginFlag.value && !newVal?.role_info?.role_id) {
      loginFlag.value = false;
      activityStore._checkIn();
    }
  }
);

watch(
  () => activityStore.activityAccountInfo,
  (newVal) => {
    if (newVal?.role_info?.role_id) {
      if (joinFlag.value) {
        handleJoinTeam();
        joinFlag.value = false;
      }
    }
  }
);

watch(
  () => popupStore.showTeamInvite,
  (newVal) => {
    if (newVal) {
      handleInvite();
    } else {
      toggleInviteTeammates(false);
    }
  },
);

const uploadLogInvite = () => {
  // 事件上报:组队邀请
  uploadLog({
    event_name: 'click',
    click_id: 14,
    click_type: 3,
  });
};

/**
 * 点击创建队伍
 */
const handleCreate = () => {
  // 事件上报:创建队伍
  uploadLog({
    event_name: 'click',
    click_id: 13,
    click_type: 3,
  });

  popupStore.setShowTeamCreate(true);
}

// TODO: 这里逻辑跟重复了，看看如何抽离分享文案的公共处理
const genShareParams = async () => {
  const { userData } = userStore;
  const { activityAccountInfo } = activityStore;

  // 存在有一些活动不需要绑定角色
  let invite_user = userData?.phone_number || activityAccountInfo?.account_id || '';
  if (activityAccountInfo.role_info?.server_name) {
    invite_user = `${activityAccountInfo.role_info?.server_name}-${activityAccountInfo.role_info?.role_name}`;
  }
  const scene_id = localStorage.getLocalStorage('SY_LOG_DATA_scene_id') || '';
  const params: Record<string, any> = {
    invite_code: activityAccountInfo.invite_code,
    recall_account_id: encodeURIComponent(activityAccountInfo.account_id),
    invite_user: encodeURIComponent(invite_user),
    invite_type: 1,
  }
  if (currenTeam.value?.team_id) {
    params.team_id = currenTeam.value.team_id;
  }
  if (scene_id) {
    params.c = scene_id;
  }
  return params;
};

/**
 * 设置分享参数
 */
const sendShareMessage = async (copyTemplate: { text: string, poster?: string }) => {
  const shareParams = await genShareParams();

  // 设置微信小程序内嵌分享参数
  if (isUserAgentType === 'WX_WEBVIEW') {
    const shareContent: ShareMessageConfigType = {};
    if (copyTemplate?.text) {
      shareContent.title = copyTemplate?.text;
    }
    if (copyTemplate?.poster) {
      shareContent.imgUrl = copyTemplate?.poster;
    }
    setWeappShareMessage(shareParams, shareContent);
  }
  // if (uploadLog) {
  //   uploadLogInvite();
  // }
  return true;
}

const handleInvite = () => {
  uploadLogInvite();

  // 找不到队伍配置（队伍下架了）
  if (!teamConfig.value) {
    showToast('活动已结束~');
    return;
  }

  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }

  if (!currenTeam.value?.team_id && !adminStore.editable) {
    // 创建队伍
    handleCreate();
    return;
  }

  // 随机选择一条邀请文案
  const list = setting.value.inviteCard.link?.copyTemplates || [];
  const textList = list.map(item => item.text) || [];
  const randomIndex = Math.floor(Math.random() * textList.length);
  const current = list[randomIndex];

  // 微信小程序内嵌环境
  if (isUserAgentType === 'WX_WEBVIEW') {
    sendShareMessage(current);
    toggleWeappShareGuide(true);
    return;
  }
  
  toggleInviteTeammates(true);
};

/**
 * 加入队伍
 */
const handleJoinTeam = async () => {
  const { activityAccountInfo } = activityStore;

  if (activityAccountInfo?.role_info?.role_id) {
    const teamId = query.value.team_id;
    const params = {
      act_id: activityAccountInfo.activity_id,
      act_acc_id: activityAccountInfo.act_acc_id,
      team_id: teamId,
      inviter: query.value.recall_account_id || '0',
      server_id: activityAccountInfo.role_info.zone_id,
      server_name: activityAccountInfo.role_info.server_name,
      role_id: +activityAccountInfo.role_info.role_id,
      role_name: activityAccountInfo.role_info.role_name,
      sex: activityAccountInfo.role_info.sex,
      career: +activityAccountInfo.role_info.career,
    };
    try {
      const res = await joinTeam(params, false);
      // 重新请求活动相关数据，并携带邀请参数
      // ** 这里是为了接受组队邀请的同时完成好友邀请任务，如果存在好友邀请模块的话
      activityStore.initActivityInfo(false, {
        inviteCode: query.value.invite_code as string,
        inviteType: query.value.invite_type ? +query.value.invite_type : undefined,
      });
      if (res.code === 0) {
        // 事件上报--被组队邀请成功加入队伍人数
        uploadLog({
          event_name: 'invite_join_team',
        });
        showToast('成功接受邀请');

        toggleInvitation(false);
        return;
      }
      // 加入队伍失败：受邀人只能是新用户或回归用户
      if (res.code === 5019) {
        toggleInvitation(false);
        if (setting.value.joinFailedModal?.enabled) {
          toggleJoinFailedModal(true);
        } else {
          showToast(res.message);
        }
        return;
      }
      showToast(res.message);
      toggleInvitation(false);
    } catch (error) {
      console.log('接受组队邀请失败', error);
    }
  }
};

/**
 * 点击加入队伍
 */
const handleJoin = () => {
  const pass1 = activityStore._checkIn();
  const callback = async () => {
    // 这里为了防止重复添加任务之后一直执行回调
    if (showInvitation.value) {
      // 如果已经登录并且已经进行绑定角色，直接加入队伍，否则等待登录 or 绑定角色之后再进行入队
      if (activityStore.activityAccountInfo?.role_info?.role_id) {
        handleJoinTeam();
      } else {
        joinFlag.value = true;
      }
    }
    return Promise.resolve();
  }
  const loginCallBack = () => {
    loginFlag.value = true;
    return callback();
  }
  if (!pass1) {
    if (!userStore.userData.token) {
      userStore.scheduler.add(loginCallBack);
    } else {
      if (!activityStore.activityAccountInfo?.role_info?.role_id) {
        userStore.bindRoleScheduler.add(callback);
      }
    }
    return;
  }
  callback();
};

const handleCloseInvite = () => {
  // 同步数据
  if (openInviteTeammates.value && !popupStore.showTeamInvite) {
    toggleInviteTeammates(false);
  }
  popupStore.setShowTeamInvite(false);
};

const handleCreateTeam = () => {
  toggleJoinFailedModal(false)
  handleCreate();
};

const handleCloseShareGuide = () => {
  toggleWeappShareGuide(false);
  // 这里得关闭邀请，否则下次不会触发 watch
  popupStore.setShowTeamInvite(false);
};
</script>

<style>
.team-invite {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.team-invite-affix {
  position: fixed;
  left: auto !important;
  z-index: 98;
}

.team-invite-content {
  font-weight: 400;
}

.team-invite-btn__disabled {
  filter: grayscale(100%);
}
</style>