import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'

const { VITE_ACTIVITY_URL } = useEnv()

export type ActivityInfoParams = {
  /**
   * 活动ID
   */
  actId: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  actAccId?: string
  /**
   * 邀请码，分享时需要
   */
  inviteCode?: string
}

export type ActivityInfoConfigTask = {
  /**
   * 活动id
   */
  id: number
  /**
   * 游戏id
   */
  project_id: number
  /**
   * 	任务名称
   */
  task_name: string
  /**
   * 任务事件code
   */
  task_code: string
  /**
   * 任务icon
   */
  task_icon: string
  /**
   * 任务刷新类型,刷新周期 1一次性 2每天 3每周 4每月
   */
  refresh_type: number
  /**
   * 	任务完成目标数
   */
  target_num: number
  /**
   * 奖励物品id
   */
  item_id: number
  /**
   * 奖励物品内容
   */
  item_icon_list: {
    /**
     * 奖励物品内容的图标
     */
    item_icon: string
    /**
     * 奖励物品内容的名称
     */
    item_name: string
  }[]
  /**
   * 任务排序,从小到大
   */
  sort: number
  /**
   * 任务内容
   */
  task_explain: number
  /**
   * 任务开始时间
   */
  start_time: number
  /**
   * 任务结束时间
   */
  end_time: number
  /**
   * 任务是否开启,1关闭2开启(前端可以不用管这个字段,暂时不用)
   */
  status: number
  /**
   * 每天限制完成次数,0不限制, 1以上就是限制次数
   */
  is_daily_limit: string
  /**
   * 是否需要展现 1展现 2不展现
   */
  is_show: number
  /**
   * 奖励类型 ：1是虚拟道具 2挑战次数 3道具红包混合奖励 4红包 5积分 6卡片
   */
  reward_type: number
}[]

export type ActivityInfoConfigGame = {
  /**
   * 游戏名字
   */
  game_name: string
  /**
   * 游戏icon
   */
  game_icon: string
  /**
   * 游戏官网
   */
  game_url: string
  /**
   * 游戏下载地址
   */
  game_download_url: string
  /**
   * 游戏开服时间
   */
  open_time: number
  /**
   * 游戏预下载开启时间
   */
  download_time: number
}

export type ActivityInfoConfigLoss = {
  /**
   * 流失判定节点
   */
  loss_judge_date: string
  /**
   * 流失判定天数
   */
  loss_span: string
}

export type ActivityInfoConfigSlide = {
  /**
   * 展示位置 1:H5 2:PC
   */
  slide: number[]
  /**
   * 轮播图名称
   */
  slideshow_name: string
  /**
   * 轮播图url
   */
  slideshow_icon: string
  /**
   * 轮播图跳转地址
   */
  jump_url: string
  /**
   * 排序权重
   */
  sort: string
}[]

export type ActivityInfoConfigReserve = {
  /**
   * icon图片
   */
  icon_img: string
  /**
   * logo图片
   */
  logo_img: string
  /**
   * 【待定】平台限制 json格式字符串，check表示不支持平台:1安卓 2ios text 前端提示语
   */
  platform_limit: string
  /**
   * 【待定】邮箱开启平台
   */
  email_open: string
  /**
   * 预约里程碑,具体看上边的返回例子
   */
  milestone: {
    /**
     * 预约里程碑,数量
     */
    stone_num: number
    /**
     * 预约里程碑,标识,前端暂时不用这个
     */
    appointment: number
  }[]
  /**
   * 当前预约数量
   */
  reservation_num: number
  /**
   * 开放领取奖励时间
   */
  award_time: string
}

export type ActivityInfoConfigTeam = {
  /**
   * 	队伍邀请配置id
   */
  id: number
  /**
   * 活动id
   */
  activity_id: number
  /**
   * 队伍活动名称
   */
  name: string
  /**
   * 组队分级信息
   */
  layered_rule: {
    /**
     * 等级
     */
    level: number
    /**
     * 数量？
     */
    num: string
  }[]
  /**
   * 组队人数上限
   */
  number_limit: number
  /**
   * 组队开始时间
   */
  start_time: number
  /**
   * 组队结束时间
   */
  end_time: number
  /**
   * 配置开启状态 2开启 1关闭
   */
  status: number
}[]

export type ActivityInfoConfigGroups = {
  /**
   * 	一键加群配置id
   */
  id: number
  /**
   * 	群名
   */
  name: string
  /**
   * 群介绍
   */
  introduce: string
  /**
   * 	加群方式 1: 跳转链接 2: 二维码
   */
  add_group_type: string
  /**
   * 若add_group_type=1, 则为跳转链接, add_group_type=2 则为二维码的链接地址
   */
  add_group_url: string
  /**
   * 配置开启状态 2开启 1关闭
   */
  status: number
}[]

export type ActivityInfoConfigLottery = {
  /**
   * 
   */
  id: number
  /**
   * 
   */
  activity_id: number
  /**
   * 
   */
  start_time: string
  /**
   * 
   */
  end_time: string
  /**
   * 
   */
  status: number
  /**
   * 
   */
  rule: string
  /**
   * 
   */
  lottery_prize: {
    /**
     * 抽奖奖品列id
     */
    id: number
    /**
     * 抽奖奖品所属活动id
     */
    activity_id: number
    /**
     * 抽奖奖品名称
     */
    name: string
    /**
     * 抽奖奖品icon
     */
    icon: string
    /**
     * 抽奖奖品排序,从小到大
     */
    sort: number
    /**
     * 开奖时间时间戳/秒
     */
    open_time: number
    /**
     * 开奖时间
     */
    open_at: string
    /**
     * 等级 0为常规 1为一等奖 2为二等奖 以此类推
     */
    grade: number
    /**
     * 是否已抽中 1为否  2为是
     */
    is_won: number
  }[]
  /**
   * 
   */
  ext_num_limit_all: number
}[]

export type ActivityInfoConfigCollect = {
  /**
   * 卡片id
   */
  id: number
  /**
   * 活动id
   */
  activity_id: number
  /**
   * 卡片名称
   */
  name: string
  /**
   * 卡片图标链接
   */
  icon: string
}[]

export type ActivityInfoConfigPreCreateRole = {
  /**
   * 预创角开始时间
   */
  start_time: string
  /**
   * 预创角开始时间
   */
  end_time: string
  /**
   * 预创角开始时间戳
   */
  cfg_start_time: number
  /**
   * 预创角结束时间戳
   */
  cfg_end_time: number
  /**
   * 开服时间
   */
  open_time: string
  /**
   * 预创角开始时间戳
   */
  platform: string
  /**
   * 区服ID
   */
  zone_id: string
  /**
   * 区服名
   */
  server_name: string
  /**
   * 渠道标识
   */
  channel: string
  /**
   * 状态，1关闭 2开启
   */
  is_open: number
}

export type ActivityInfoConfigPreCharge = {
  /**
   * 预充值开始时间
   */
  start_time: string
  /**
   * 预充值结束时间
   */
  end_time: string
  /**
   * 预充值开始时间戳
   */
  cfg_start_time: number
  /**
   * 预充值结束时间戳
   */
  cfg_end_time: number
  /**
   * 预创角开始时间戳
   */
  platform: string
  /**
   * 区服ID
   */
  zone_id: string
}

export type ActivityInfoConfigIntegral = {
  /**
   * 礼包领取需要角色等级
   */
  draw_role_level_limit: number
  /**
   * 可领取人群
   */
  draw_user_group_id: number
  /**
   * id
   */
  id: number
  /**
   * 是否可获取抽奖次数
   */
  is_add_lottery_num: number
  /**
   * 是否要求流失用户
   */
  is_loss_user: number
  /**
   * 礼包icon列表
   */
  item_icon_list: {
    /**
     * 礼包icon地址
     */
    item_icon: string
    /**
     * 礼包icon名称
     */
    item_name: string
  }[]
  /**
   * 礼包名称
   */
  item_name: string
  /**
   * 礼包id
   */
  item_id: number
  /**
   * 礼包领取需要积分
   */
  need_integral: number
  /**
   * 游戏项目id
   */
  project_id: number
  /**
   * 礼包状态 ： 1未开启 2已开启
   */
  status: number
}[]

export type ActivityInfoConfigRedEnvelope = {
  /**
   * id
   */
  id: number
  /**
   * activity_id
   */
  activity_id: number
  /**
   * 单用户每日金额上限
   */
  user_everyday_draw_money_limit: number
  /**
   * 单用户每日金额上限
   */
  user_everyday_draw_num_limit: number
  /**
   * 用户可领取总金额
   */
  user_draw_money_limit: number
  /**
   * 用户可领取总次数
   */
  user_draw_num_limit: number
  /**
   * 红包数量上限
   */
  red_total_num: number
  /**
   * 单角色领取总上限
   */
  role_draw_num_limit: number
  /**
   * 最低提现金额，0代表不限制
   */
  withdraw_min_amount: number
  /**
   * 可提现申请时间
   */
  withdraw_time: string
  /**
   * 首次提现最低金额
   */
  first_purse_withdraw_amount: number
}

export type ActivityInfoConfigCollectDrawDetail = {
  /**
   * 卡片id
   */
  id: number
  /**
   * 卡片名称
   */
  name: string
  /**
   * 卡片图片
   */
  icon: string
  /**
   * 是否限定卡 1为否 2为是
   */
  is_limit_card: number
}[]

export type ActivityInfoConfigCollectDraw = {
  /**
   * id
   */
  id: number
  /**
   * activity_id
   */
  activity_id: number
  /**
   * 卡片
   */
  detail: ActivityInfoConfigCollectDrawDetail
}

export type ActivityInfo = {
  /**
   * 活动信息
   */
  init: {
    /**
     * 活动id
     */
    id: number
    /**
     * 游戏id
     */
    project_id: number
    /**
     * 活动名称
     */
    activity_name: string
    /**
     * 活动组件启用列表
     */
    module_json: string[]
    /**
     * 活动规则
     */
    activity_rule: string
    /**
     * 活动开始时间
     */
    start_time: string
    /**
     * 活动结束时间
     */
    end_time: string
    /**
     * 活动开始时间戳，单位秒
     */
    activity_start_time: number
    /**
     * 活动结束时间戳，单位秒
     */
    activity_end_time: number
    /**
     * 活动状态，1关闭 2开启
     */
    status: number
    /**
     * 活动维度 1账号维度 2角色维度
     */
    dimension: number
    /**
     * 建立邀请关系类型 1:主动同意建立邀请关系（前端弹窗询问是否同意并请求邀请接口） 2: 默认建立邀请关系，前端无需处理
     */
    establish_invite_type: number
    /**
     * 开服时间
     */
    open_time: string
    /**
     * 区服名称
     */
    server_names: string[]
    /**
     * 是否需要实名 1不需要 2需要
     */
    is_need_real_name: number
    /**
     * 活动类型
     * - 0: 默认
     * - 1: 游戏回流活动
     * - 2: 游戏拉新活动
     * - 3: 私域拉新活动
     * - 4: 新服预约活动
     * - 5: 游戏促活活动
     */
    activity_type: number
    /**
     * 活动创建人
     */
    create_user: string
  }
  /**
   * 活动组件配置
   */
  config: {
    /**
     * 任务信息
     */
    task: ActivityInfoConfigTask
    /**
     * 游戏信息
     */
    game: ActivityInfoConfigGame
    /**
     * 流失用户判定
     */
    loss: ActivityInfoConfigLoss
    /**
     * 轮播图
     */
    slide: ActivityInfoConfigSlide
    /**
     * 预约活动信息
     */
    reserve: ActivityInfoConfigReserve
    /**
     * 组队配置
     */
    team: ActivityInfoConfigTeam
    /**
     * 快速加群配置
     */
    groups: ActivityInfoConfigGroups
    /**
     * 抽奖信息
     */
    lottery: ActivityInfoConfigLottery
    /**
     * 集卡配置
     */
    collect: ActivityInfoConfigCollect
    /**
     * 任务最大完成次数
     */
    ext_num_limit_all: number
    /**
     * 预创角信息
     */
    preCreateRole: ActivityInfoConfigPreCreateRole
    /**
     * 预创角信息
     */
    preCharge: ActivityInfoConfigPreCharge
    /**
     * 积分礼包
     */
    integral: ActivityInfoConfigIntegral
    /**
     * 红包信息
     */
    red_envelope: ActivityInfoConfigRedEnvelope
    /**
     * 集卡信息
     */
    collect_draw: ActivityInfoConfigCollectDraw
  }
}

/**
 * @description 获取活动信息
 * @param ActivityInfoParams
 * @returns
 */
export function postActivityInfo(params: ActivityInfoParams) {
  const { actId, actAccId, inviteCode } = params
  const data: {
    act_id: number;
    invite_code?: string;
    act_acc_id?: string;
  } = {
    act_id: actId,
    invite_code: inviteCode ? inviteCode : undefined,
  }
  if (actAccId) {
    data.act_acc_id = actAccId
  }
  return http.post<ActivityInfo>(`${VITE_ACTIVITY_URL}/activity/getActivityInfo`, data)
}

export type AccountInfoParams = {
  /**
   * 活动ID
   */
  actId: number
  /**
   * 登录方式 1,token登录，2,角色码登录，3,验证码免密直登(海外预约专用)，4,加密acc_id刷新登陆态
   */
  loginType?: number
  /**
   * 用户token，login_type = 1时使用
   */
  token?: string
  /**
   * 角色登录码，login_type = 2时使用
   */
  roleCode?: string
  /**
   * 验证码 tiket 验证码获取，login_type = 3时使用
   */
  captchaTiket?: string
  /**
   * 验证码，login_type = 3时使用
   */
  captchaCode?: string
  /**
   * 手机号，login_type = 3时使用
   */
  phoneNumber?: number
  /**
   * 加密acc_id，login_type = 4时使用
   */
  actAccId?: string
  /**
   * 邀请码，分享时需要
   */
  inviteCode?: string
  /**
   * 邀请类型 1链接邀请 3海报分享邀请
   */
  inviteType?: number
  /**
   * 微信的 openid 获取方式: 若进入活动的链接中包含了"openid"的字符串，则进行获取，没有则可以不用上传
   */
  wxOpenid?: string
  /**
   * 是否新注册 0否 1是
   */
  isNewReg?: number
}

export type AccountInfoRoleInfo = {
  /**
   * 职业
   */
  career: string
  /**
   * 平台标识
   */
  platform: string
  /**
   * 角色创建时间
   */
  reg_time: string
  /**
   * 角色id
   */
  role_id: string
  /**
   * 角色等级
   */
  role_level: string
  /**
   * 角色名称
   */
  role_name: string
  /**
   * 角色评分
   */
  role_score: number
  /**
   * 区服名称
   */
  server_name: string
  /**
   * 性别
   */
  sex: number
  /**
   * vip等级
   */
  vip: string
  /**
   * 区服id
   */
  zone_id: string
}

export type AccountInfo = {
  /**
   * 活动信息
   */
  act_acc_id: string
  /**
   * 账号ID
  */
  account_id: string
  /**
   * 账号ID
   */
  account_name: string
  /**
   * 账号ID
   */
  activity_id: number
  /**
   * 邀请码,用来生成分享连接的,给别人用的
   */
  invite_code: string
  /**
   * 被邀请情况,0不是被邀请的,1是被别人邀请的
   */
  invited_status: number
  /**
   * 邀请玩家的数量
   */
  invite_num: number
  /**
   * 用户状态：1正常，2流失
   */
  status: number
  /**
   * 是否预创角 1为否 2为是
   */
  is_pre_role: number
  /**
   * 角色id 12_symlf_1
   */
  role_id: string
  /**
   * 角色信息
   */
  role_info: AccountInfoRoleInfo
  /**
   * 	账号类型：1官方，2渠道
   */
  account_type: number
  /**
   * 点击分享按钮次数 默认为0
   */
  share_count: number
  /**
   * 微信绑定情况 0未绑定 1已绑定
   */
  wx_bind_status: number
  /**
   * 拓展信息
   */
  extra: {
    /**
     * 地址信息
     */
    address_info: {
      /**
       * 收件人
       */
      name: string
      /**
       * 手机号
       */
      phone_number: string
      /**
       * 详细地址
       */
      address: string
    }
  }
  /**
   * 是否活动已完成，1否 2是
   * 若是，则表示已经完成活动了，不能继续参与活动进行相关的操作（任务、抽奖之类的...）
   */
  is_close: 1 | 2
  /**
   * 是否暴击，0未设置 1否 2是
   */
  is_promote_earnings: 0 | 1 | 2
  /**
   * 用户分组id
   */
  user_group_id: number
}

/**
 * @description 获取活动账号信息
 * @param AccountInfoParams
 * @returns
 */
export function postAccountInfo(params: AccountInfoParams) {
  const { actId, loginType, token, roleCode, captchaTiket, captchaCode, phoneNumber, actAccId, inviteCode, inviteType, wxOpenid, isNewReg } = params
  const data: {
    act_id: string;
    login_type?: string;
    token?: string;
    role_code?: string;
    captcha_tiket?: string;
    captcha_code?: string;
    phone_number?: string;
    invite_code?: string;
    invite_type?: string;
    wx_openid?: string;
    is_new_reg?: string;
    act_acc_id?: string;
  } = {
    act_id: `${actId}`,
    login_type: loginType ? `${loginType}` : undefined,
    token: token ? token : undefined,
    role_code: roleCode ? roleCode : undefined,
    captcha_tiket: captchaTiket ? captchaTiket : undefined,
    captcha_code: captchaCode ? captchaCode : undefined,
    phone_number: phoneNumber ? `${phoneNumber}` : undefined,
    invite_code: inviteCode ? inviteCode : undefined,
    invite_type: inviteType ? `${inviteType}`: undefined,
    wx_openid: wxOpenid ? wxOpenid : undefined,
    is_new_reg: isNewReg ? `${isNewReg}` : undefined,
  }
  if (actAccId) {
    data.act_acc_id = actAccId
  }

  return http.post<AccountInfo>(`${VITE_ACTIVITY_URL}/activity/getAccountInfo`, data)
}

export type ComponentWithUserInfoParams = {
  /**
   * 活动ID
   */
  actId?: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  actAccId?: string
}

export type ComponentWithUserInfoTask = {
  /**
   * 任务id
   */
  id: number
  /**
   * 任名目标数
   */
  target_num: number
  /**
   * 任名目前完成数
   */
  progress_num: number
  /**
   * 任务完成状态 1:进行中 2:完成任务 3已领取
   */
  status: number
  /**
   * 任务完成id 用户有触达任务就会产生的任务完成日志id
   */
  task_log_id: number
  /**
   * 是否 已达次数上限，1否 2是
   */
  is_reach_max_draw: number
}[]

export type ComponentWithUserInfoLottery = {
  /**
   * 抽奖组件配置id
   */
  id: number
  /**
   * 剩下的抽奖次数
   */
  lottery_remain_num: number
  /**
   * 是否订阅了通知开奖的小程序
   */
  is_subscribe: number
  /**
   * 0表示不符合条件不能进行抽奖 , 1表示可以抽奖, 2表示已经抽奖了,不能再抽.
   */
  status: number
  /**
   * 任务完成次数
   */
  lottery_all_num: number
  /**
   * 奖品类型 1: 实物 2: 游戏道具 3: 序列号 4:积分 5:红包
   */
  item_type: number
}[]

export type ComponentWithUserInfoMembers = {
  /**
   * 账号id
   */
  account_id: string
  /**
   * 用户头像
   */
  portrait: number
  /**
   * 账号名
   */
  account_name: number
  /**
   * 加密后的手机号
   */
  phone_number: number
  /**
   * 门派
   */
  career: number
  /**
   * 	区服id
   */
  server_id: number
  /**
   * 区服名称
   */
  server_name: string
  /**
   * 	角色id
   */
  role_id: number
  /**
   * 角色等级
   */
  role_level: number
  /**
   * 角色名称
   */
  role_name: string
  /**
   * 	1为队长 2为队员
   */
  change_type: number
  /**
   * 性别
   */
  sex: number
  /**
   * 区id
   */
  zone_id: string
}[]

export type ComponentWithUserInfoTeamLog = {
  /**
   * 账号id
   */
  account_id: number
  /**
   * 操作者账号信息
   */
  operation_info: {
    /**
     * 账号名
     */
    account_id: number
    /**
     * 	头像
     */
    portrait: number
    /**
     * 账号名
     */
    account_name: number
    /**
     * 手机号
     */
    phone_number: string
    /**
     * 门派
     */
    career: string
    /**
     * 区服id
     */
    server_id: string
    /**
     * 区服名称
     */
    server_name: string
    /**
     * 	角色id
     */
    role_id: number
    /**
     * 角色等级
     */
    role_level: number
    /**
     * 角色名称
     */
    role_name: string
  }[]
  /**
   * 组队时间
   */
  created_at: string
}[]

export type ComponentWithUserInfoTeam = {
  /**
   * 组队配置id
   */
  id: number
  /**
   * 用户组队状态 0:未加入队伍 1: 已加入队伍
   */
  status: number
  /**
   * 用户创建的队伍id
   */
  team_id: number
  /**
   * 队伍是否存在流失 0不存在 1存在
   */
  is_exists_loss: number
  /**
   * 队伍是否存在新注册 0不存在 1存在
   */
  is_exists_new_reg: number
  /**
   * 退队次数
   */
  quit_team_count: number
  /**
   * 队名
   */
  team_name: string
  /**
   * 队伍宣言
   */
  team_slogan: string
  /**
   * 赛段 根据角色等级进行枚举 1为68-69 2为85-89
   */
  stage: number
  /**
   * 队伍状态 1正常 2解散,3组队成功,4入选,5未入选
   */
  team_status: number
  /**
   * 队伍成员
   */
  members: ComponentWithUserInfoMembers
  /**
   * 队伍成员
   */
  levels: {
    /**
     * 组队层级
     */
    level: number
    /**
     * 是否达到 0:未达到 1:达到
     */
    status: number
  }[]
  /**
   * 队伍日志
   */
  team_log: ComponentWithUserInfoTeamLog
  /**
   * 职业，0是无职业,1是奕剑,2是龙宫,3是青丘,4是高老庄,5是唐门,6是普陀,7是九玄
   */
  career_ask_collect: number[]
  /**
   * 未加入队伍的职业要求集合，0是无职业,1是奕剑,2是龙宫,3是青丘,4是高老庄,5是唐门,6是普陀,7是九玄
   */
  not_career_ask_collect: number[]
  /**
   * 在线时间，0是默认,1是经常在线,2是早上在线,3是晚上在线
   */
  online_time: number
  /**
   * 队伍风格，0是默认,1是竞技,2是情义,3是休闲
   */
  team_style: number
  /**
   * 小队(访问)状态，0是默认,1是公开,2是私密
   */
  access_status: number
  /**
   * 队伍充值金额
   */
  team_amount: number
  /**
   * 队伍积分
   */
  team_integral: number
}[]

export type ComponentWithUserInfoCollect = {
  /**
   * 是否完成预约 0: 否 1: 是
   */
  finish_reserve: number
  /**
   * 是否完成预约 0: 否 1: 是
   */
  finish_assist: number
  /**
   * 好友接受了索要/赠送时, 用户进入活动页弹出的消息, 返回一次之后后续返回的都是空数组
   */
  assist_notify: {
    /**
     * 消息类型 0: 索要,对应原型的”收到xxx送的信物” 1: 赠送, 对应原型的”xxx已领取你的信物”
     */
    type: number
    /**
     * 消息类型 0: 索要,对应原型的”收到xxx送的信物” 1: 赠送, 对应原型的”xxx已领取你的信物”
     */
    list: {
      /**
       * 协助者
       */
      assist_info: {
        /**
         * 协助者id
         */
        account_id: string
        /**
         * 协助者账号名
         */
        account_name: string
      }
      /**
       * 卡片信息
       */
      item: {
        /**
         * 卡片id
         */
        id: number
        /**
         * 卡片图标
         */
        icon: string
      }
    }[]
  }[]
}

export type ComponentWithUserInfoAwardList = {
  /**
   * 中奖记录id
   */
  id: number
  /**
   * 奖励名称
   */
  item_name: string
  /**
   * 角色信息,当没有角色信息时,为空
   */
  role_meta: []
  /**
   * 奖品类型 1: 序列号 2: 实物 3: 游戏道具 4:积分
   */
  item_type: number
  /**
   * 奖励来源 , 1 任务, 2抽奖
   */
  order_type: number
  /**
   * 发货状态 , 0=>未发货,1 =>发货中, 2=>已发货, 3=>发货失败
   */
  status: number
  /**
   * 领取时间
   */
  created_at: string
  /**
   * 发送状态
   * 1:未创角 2:发放成功 3:发放失败 4:未达25级 5: 未发放
   */
  send_status: string
}[]

export type ComponentWithUserInfoInviteList = {
  /**
   * 被邀请用户id
   */
  invited_account_id: string
  /**
   * 	被邀请用户name
   */
  invited_account_name: string
  /**
   * 邀请类型 1:分享链接邀请 2:短信召回邀请
   */
  invite_type: number
  /**
   * 邀请关系建立时间
   */
  created_at: number
  /**
   * 邀请关系成立时间
   */
  updated_at: number
  /**
   * 角色信息
   */
  role_info: {
    /**
     * 角色名称
     */
    role_name: string
    /**
     * 区服名称
     */
    server_name: string
  }
  /**
   * 账号额外信息
   */
  invited_account_extra: {
    /**
     * 手机号
     */
    phone_number: string
  }
}[]

export type ComponentWithUserInfoIntegral = {
  /**
   * 用户拥有总积分
   */
  total_integral: number
  /**
   * 积分礼包列表
   */
  award_list: {
    /**
     * 礼包记录id
     */
    id: number
    /**
     * 礼包领取需要积分
     */
    need_integral: string
    /**
     * 礼包名称
     */
    item_name: string
    /**
     * 礼包领取需要角色等级
     */
    draw_role_level_limit: number
    /**
     * 礼包状态 ： 1未开启 2已开启
     */
    status: number
    /**
     * 礼包是否领取状态 ： 1未领取 2已领取
     */
    receive_status: number
    /**
     * 礼包icon列表
     */
    item_icon_list: {
      /**
       * 礼包icon地址
       */
      item_icon: string
      /**
       * 礼包icon名称
       */
      item_name: string
    }[]
    /**
     * 可领取人群
     */
    draw_user_group_id: number
  }[]
}

export type ComponentWithUserInfoRedEnvelope = {
  /**
   * 可领取余额
   */
  can_withdraw_money: number
  /**
   * 领取记录列表
   */
  draw_list: {
    /**
     * 红包领取记录id
     */
    id: number
    /**
     * 红包名称
     */
    name: string
    /**
     * 	红包金额
     */
    num: number
    /**
     * 领取时间
     */
    draw_time: number
    /**
     * 发送状态 1待发送 2已发送 3发送失败
     */
    send_status: number
  }[]
  /**
   * 累计获得金额
   */
  total_amount: number
  /**
   * 提现结束时间
   */
  withdraw_end_time: number
}

export type ComponentWithUserInfoCollectDrawCardListReceiveLog = {
  /**
   * 领取记录id
   */
  id: number
  /**
   * 领取类型 1领取 2赠送
   */
  receive_type: number
  /**
   * 是否已领取卡片奖励 1为否 2为是
   */
  is_receive_gift: number
}[]

export type ComponentWithUserInfoCollectDrawCardList = {
  /**
   * 卡片id
   */
  id: number
  /**
   * 卡片名称
   */
  name: string
  /**
   * 卡片图片
   */
  icon: string
  /**
   * 是否限定卡 1为否 2为是
   */
  is_limit_card: number
  /**
   * 用户已获取卡片记录
   */
  receive_log: ComponentWithUserInfoCollectDrawCardListReceiveLog
}[]

export type ComponentWithUserInfoCollectDraw = {
  /**
   * 集卡卡片列表
   */
  card_list: ComponentWithUserInfoCollectDrawCardList
  /**
   * 是否已集满
   */
  is_full_collect: boolean
  /**
   * 集满排名
   */
  rank: number
  /**
   * 是否已开奖
   */
  is_open_award: boolean
}

export type ComponentWithUserInfoData = {
  /**
   * 用户与活动组件产生的信息
   */
  component_with_user_info: {
    /**
     * 用户的任务完成情况
     */
    task: ComponentWithUserInfoTask
    /**
     * 用户的预约完成情况
     */
    reserve: {
      /**
       * 	预约情况,0未预约,1已预约
       */
      reserved_status: number
      /**
       * 	领取奖励状态,0表示不符合条件不能领取 , 1表示可以领取, 2表示已经领取了.
       */
      receive_award_status: number
    }
    /**
     * 用户流失情况
     */
    loss: {
      /**
       * 流失状态, 1=>未流失, 2=>已经流失
       */
      loss_status: number
    }
    /**
     * 用户抽奖情况
     */
    lottery: ComponentWithUserInfoLottery
    /**
     * 用户在活动期间小游戏的情况
     */
    miniGame: {
      /**
       * 累计积分
       */
      score: number
      /**
       * 挑战次数
       */
      challenge_count: number
    }
    /**
     * 用户在活动期间充值代币商城的情况
     */
    total_token_mall_charge : {
      /**
       * 充值总额
       */
      total_charge: number
    }
    /**
     * 用户组队情况
     */
    team: ComponentWithUserInfoTeam
    /**
     * 用户集卡情况
     */
    collect: ComponentWithUserInfoCollect
    /**
     * 积分礼包情况
     */
    integral: ComponentWithUserInfoIntegral
    /**
     * 用户红包情况列表
     */
    red_envelope: ComponentWithUserInfoRedEnvelope
    /**
     * 问卷相关
     */
    asq: {
      /**
       * 	是否已提交
       */
      is_submit: boolean
    }
    /**
     * 集卡抽卡
     */
    collect_draw: ComponentWithUserInfoCollectDraw
  }
  /**
   * 用户奖品列表
   */
  award_list: ComponentWithUserInfoAwardList
  /**
   * 用户邀请列表
   */
  invite_list: ComponentWithUserInfoInviteList
}

/**
 * @description 用户与组件产生的信息
 * @param ComponentWithUserInfoParams
 * @returns
 */
export function postComponentWithUserInfo(params: ComponentWithUserInfoParams) {
  const { actId, actAccId } = params
  return http.post<ComponentWithUserInfoData>(`${VITE_ACTIVITY_URL}/activity/getComponentWithUserInfo`, {
    act_id: `${actId}`,
    act_acc_id: actAccId ? actAccId : undefined,
  })
}


export type ActivityBindAccRoleParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 加密acc_id
   */
  act_acc_id: string
  /**
   * 平台标识, 1.4的server_id, 字符串切割"_"的第一部分
   */
  platform: string
  /**
   * 区服id, 1.4的server_id, 字符串切割"_"的第二部分
   */
  zone_id: number
  /**
   * 角色id
   */
  role_id: number
  /**
   * 角色名称
   */
  role_name: string
  /**
   * 区服名称
   */
  server_name: string
  /**
   * 战力值
   */
  role_score?: number
  /**
   * vip等级
   */
  vip: number
  /**
   * 角色等级
   */
  role_level: number
  /**
   * 注册时间(时间戳到秒)
   */
  reg_time: string
  /**
   * 职业
   */
  career?: number
  /**
   * 性别
   */
  sex?: number
  /**
   * 最近登录时间戳
   */
  last_login_time?: number
}
/**
 * @description 活动角色绑定
 * @param ActivityBindAccRoleParams
 * @returns
 */
export function postActivityBindAccRole(params: ActivityBindAccRoleParams) {
  const { act_id, act_acc_id, platform, zone_id, role_id, role_name, server_name, role_score, vip, role_level, reg_time, career, sex, last_login_time } = params
  return http.post(`${VITE_ACTIVITY_URL}/activity/bindAccRole`, {
    act_id: `${act_id}`,
    act_acc_id: act_acc_id ? `${act_acc_id}` : undefined,
    platform: `${platform}`,
    zone_id: `${zone_id}`,
    role_id: `${role_id}`,
    role_name: `${role_name}`,
    server_name: `${server_name}`,
    role_score: role_score ? `${role_score}` : undefined,
    vip: `${vip}`,
    role_level: `${role_level}`,
    reg_time: `${reg_time}`,
    career: career ? `${career}` : undefined,
    sex: sex ? `${sex}` : undefined,
    last_login_time: last_login_time ? last_login_time : undefined,
  })
}


export type ActivityTaskGetAwardTaskParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
  /**
   * 任务日志id 通过【用户与组件产生的信息】接口获取
   */
  task_log_id: number
  /**
   * 选择类型（任务类型reward_type为3或者4 道具红包混合奖励才使用到）：1游戏道具 2红包 3集卡 4召回红包 5暴击红包
   */
  select_type?: number
  /**
   * 指定角色领取，角色名称
   */
  role_name?: string
  /**
   * 指定角色领取，角色id 例子：23_sy_3
   */
  role_id?: string
}

export type ActivityTaskGetAwardTaskData = {
  /**
   * 卡片名称
   */
  card_name: string
  /**
   * 卡片图片地址
   */
  card_icon: string
  /**
   * 用户集卡记录id
   */
  card_log_id: number
}

/**
 * @description 领取任务奖励
 * @param ActivityTaskGetAwardTaskParams
 * @returns
 */
export function postActivityTaskGetAwardTask(params: ActivityTaskGetAwardTaskParams, errToast = true) {
  const { act_id, act_acc_id, task_log_id, select_type, role_name, role_id } = params
  return http.post<ActivityTaskGetAwardTaskData>(
    `${VITE_ACTIVITY_URL}/activity/task/getAwardTask`,
    {
      act_id: `${act_id}`,
      act_acc_id: act_acc_id,
      task_log_id: `${task_log_id}`,
      select_type: select_type ? `${select_type}` : undefined,
      role_name: role_name ? `${role_name}` : undefined,
      role_id: role_id ? `${role_id}` : undefined,
    },
    { errToast },
  )
}

export type ActivityAcceptInviteParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
  /**
   * 邀请码
   */
  invite_code: string
  /**
   * 是否海报 0不是 1是
   */
  is_poster: number
}

/**
 * @description 玩家接受邀请
 * ActivityAcceptInviteParams
 * @returns
 */
export function postActivityAcceptInvite(params: ActivityAcceptInviteParams, errToast = true) {
  const { act_id, act_acc_id, invite_code, is_poster } = params
  return http.post(
    `${VITE_ACTIVITY_URL}/activity/acceptInvite`,
    {
      act_id: `${act_id}`,
      act_acc_id: act_acc_id,
      invite_code: `${invite_code}`,
      is_poster: `${is_poster}`,
    },
    { errToast },
  )
}

export type ActivityTaskUploadTaskParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
  /**
   * 任务标识
   */
  task_code: string
  /**
   * 任务ID
   */
  task_id: number
}

/**
 * @description web上报任务完成动作
 * @param ActivityTaskUploadTaskParams
 * @returns
 */
export function postActivityTaskUploadTask(params: ActivityTaskUploadTaskParams, errToast = true) {
  const { act_id, act_acc_id, task_code, task_id } = params
  return http.post(
    `${VITE_ACTIVITY_URL}/activity/task/uploadTask`,
    {
      act_id: `${act_id}`,
      act_acc_id: act_acc_id,
      task_code: `${task_code}`,
      task_id: `${task_id}`,
    },
    { errToast },
  )
}


export type AwardIntegralParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
  /**
   * 积分奖励配置ID
   */
  integral_cfg_id: number
}

/**
 * @description 领取积分奖励
 * @param AwardIntegralParams
 * @returns
 */
export function postAwardIntegral(params: AwardIntegralParams, errToast = true) {
  const { act_id, act_acc_id, integral_cfg_id } = params
  return http.post(
    `${VITE_ACTIVITY_URL}/activity/integral/getAwardIntegral`,
    {
      act_id: `${act_id}`,
      act_acc_id: act_acc_id,
      integral_cfg_id: `${integral_cfg_id}`,
    },
    { errToast },
  )
}

export type ActivitySaveAddressParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
  /**
   * 真实姓名
   */
  name: string
  /**
   * 手机号码
   */
  phone_number: string
  /**
   * 详细地址
   */
  address: string
}

/**
 * @description 收货地址填写接口
 * @param {*} params.act_id 活动ID
 * @param {*} params.act_acc_id 活动系统加密的账号id，登录状态需要
 * @param {*} params.name 真实姓名
 * @param {*} params.phone_number 手机号码
 * @param {*} params.address 详细地址
 * @returns
 */
export function postActivitySaveAddress(params: ActivitySaveAddressParams, errToast = true) {
  const { act_id, act_acc_id, name, phone_number, address } = params
  return http.post(
    `${VITE_ACTIVITY_URL}/activity/update_contact_address`,
    {
      act_id: `${act_id}`,
      act_acc_id: act_acc_id,
      name: `${name}`,
      phone_number: `${phone_number}`,
      address: `${address}`,
    },
    { errToast }
  )
}

export type SpokespersonTagsParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
}

export type SpokespersonTagsData = {
  /**
   * 活动ID
   */
  activity_id: number
  /**
   * 账号ID
   */
  account_id: string
  /**
   * 角色ID
   */
  role_id: string
  /**
   * 基础充值标签
   */
  charge_label: string
  /**
   * 基础等级标签
   */
  lv_label: string
  /**
   * 	玩法代言标签
   */
  gameplay_label: string
  /**
   * 	美术代言标签
   */
  arts_label: string
  /**
   * 福利代言标签
   */
  welfare_label: string
}

/**
 * @description 获取用户代言人标签
 * @param {*} params.act_id 活动ID
 * @param {*} params.act_acc_id 活动系统加密的账号id，登录状态需要
 * @returns
 */
export function postSpokespersonTags(params: SpokespersonTagsParams) {
  return http.post<SpokespersonTagsData>(
    `${VITE_ACTIVITY_URL}/activity/get_spokesperson_tags`,
    params,
  )
}

export type LossFriendsListParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
}

export function postLossFriendsList(params: LossFriendsListParams) {
  return http.post<any[]>(
    `${VITE_ACTIVITY_URL}/recall/get_friend_list`,
    params,
  )
}