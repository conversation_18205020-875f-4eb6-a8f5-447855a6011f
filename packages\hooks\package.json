{"name": "@bish/hooks", "version": "0.0.26", "description": "", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "files": ["src"], "keywords": [], "author": "", "license": "ISC", "dependencies": {"@bish/api": "workspace:^", "@bish/core": "workspace:^", "@bish/hooks": "workspace:^", "@bish/lang": "workspace:^", "@bish/request": "workspace:^", "@bish/store": "workspace:^", "@bish/types": "workspace:^", "@bish/ui": "workspace:^", "@bish/utils": "workspace:^", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0"}, "peerDependencies": {"@dcloudio/uni-app": "3.0.0-3081220230817001", "vant": "4.9.3", "vue": ">=3.2.0", "vue-router": "4.3.3"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@types/crypto-js": "^4.2.2", "@vue/tsconfig": "^0.1.3"}, "publishConfig": {"registry": "http://sy-registry.shiyue.com"}}