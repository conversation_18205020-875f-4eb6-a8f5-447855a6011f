<!--
* @Description: 基础组队-队伍积分
-->
<template>
  <UiText
    v-model:setting="setting"
    :interpolation="interpolation"
    class="team-integral"
  />
</template>

<script lang="ts">
export default {
  name: 'team-integral',
}
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import UiText from '../../../ui/text/index.vue';
import type { TeamIntegralSetting } from './team-integral';
import { defaultConfig } from './index';

export interface TeamIntegralProps {
  /**
   * 配置
   */
  setting: TeamIntegralSetting
}

const props = withDefaults(defineProps<TeamIntegralProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TeamIntegralSetting): void;
}>();

const setting = useVModel(props, 'setting', emits);

const activityStore = useActivityStore();

// 与配置对应的队伍信息
const currenTeam = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.team?.find(item => item.id === setting.value.teamId);
});

const interpolation = computed(() => {
  return {
    integral: currenTeam.value?.team_integral || 0,
  };
});
</script>

<style>
.team-integral {}
</style>