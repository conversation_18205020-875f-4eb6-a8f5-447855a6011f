import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'

const { VITE_ACTIVITY_URL, VITE_COMMON_URL } = useEnv()

export type ActivityPreBindAccRoleParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 角色ID 存在角色时必填
   */
  role_id?: number
  /**
   * 角色名称，不得超过6个字符
   */
  role_name: string
  /**
   * 性别 1为男 0为女
   */
  gender: number
  /**
   * 角色职业， 详情查看各个项目具体角色职业说明
   */
  career: number
  /**
   * 邀请码， 被邀请完成预创角
   */
  invite_code?: string
}

/**
 * @description 创建预服角色
 * @param {number} params.act_id 活动ID
 * @param {number} params.role_id 角色ID 存在角色时必填
 * @param {number} params.role_name 角色名称，不得超过6个字符
 * @param {number} params.gender 性别 1为男 0为女
 * @param {number} params.career 角色职业， 详情查看各个项目具体角色职业说明
 * @param {number} params.invite_code 邀请码， 邀请链接上的个人邀请码
 * @returns
 */
export function postActivityPreBindAccRole(params: ActivityPreBindAccRoleParams) {
  const { act_id, role_id, role_name, gender, career, invite_code } = params
  return http.post<any>(
    `${VITE_ACTIVITY_URL}/activity/preBindAccRole`,
    {
      act_id: `${act_id}`,
      role_name: role_name,
      gender: `${gender}`,
      career: `${career}`,
      role_id: role_id ? `${role_id}` : undefined,
      invite_code: invite_code ? `${invite_code}` : undefined,
    },
    { errToast: false }
  )
}

export type WebRolesGetPreRoleListParams = {
  /**
   * 游戏ID
   */
  project_id: number
  /**
   * 长安幻想写固定 cahx
   */
  product_name: string
  /**
   * 区服标识
   */
  platform_name: string
  /**
   * 区服ID
   */
  zone_id: string
}

export type WebRolesGetPreRoleListData = {
  /**
   * 账号ID
   */
  account_id: string
  /**
   * 	角色id
   */
  role_id: string
  /**
   * 	角色名称
   */
  role_name: string
  /**
   * 职业（0是无职业,1是奕剑,2是龙宫,3是青丘,4是高老庄,5是唐门,6是普陀,7是九玄）
   */
  career: string
  /**
   * 性别，0是默认女，1是男
   */
  sex: string
}[]

/**
 * @description 获取得到预创角的角色数据
 * @param {number} params.project_id 游戏ID
 * @param {number} params.product_name 长安幻想写固定 cahx
 * @param {number} params.platform_name 区服标识
 * @param {number} params.zone_id 区服ID
 * @returns
 */
export function postWebRolesGetPreRoleList(params: WebRolesGetPreRoleListParams) {
  const { project_id, product_name, platform_name, zone_id } = params
  return http.post<WebRolesGetPreRoleListData>(`${VITE_COMMON_URL}/web/roles/getPreRoleList`, {
    project_id: `${project_id}`,
    product_name: product_name,
    platform_name: platform_name,
    zone_id: zone_id,
  })
}

export type SubmitReservationParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 手机号
   */
  phone_number?: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 系统类型 1安卓 2IOS
   */
  system_type: number
  /**
   * 来源渠道： 1官网/游戏主页 2好友邀请 3广告 4微信 5社区
   */
  src_channel: number
  /**
   * 来源渠道： 来源平台 1pc 2移动端
   */
  src_platform: number
  /**
   * 唤起预约位置 1主页预约按钮 2邀请预约按钮 3抽奖预约按钮 4集卡预约
   */
  location: number
  /**
   * 邀请码，可不传
   */
  invite_code?: string
}

/**
 * @description 提交预约
 * @param {number} params.act_id 活动ID
 * @param {number} params.phone_number 手机号
 * @param {string} params.act_acc_id 活动用户id
 * @param {number} params.system_type 系统类型 1安卓 2IOS
 * @param {number} params.src_channel 来源渠道： 1官网/游戏主页 2好友邀请 3广告 4微信 5社区
 * @param {number} params.src_platform 来源平台 1pc 2移动端
 * @param {number} params.location 唤起预约位置 1主页预约按钮 2邀请预约按钮 3抽奖预约按钮 4集卡预约
 * @param {string} params.invite_code 邀请码，可不传
 * @returns
 */
export function postSubmitReservation(params: SubmitReservationParams) {
  const { act_id, phone_number, act_acc_id, system_type, src_channel, src_platform, location, invite_code } = params
  return http.post<any>(
    `${VITE_ACTIVITY_URL}/reservation/submitReservation`,
    {
      act_id: `${act_id}`,
      phone_number: phone_number ? `${phone_number}` : undefined,
      act_acc_id: act_acc_id,
      system_type: `${system_type}`,
      src_channel: `${src_channel}`,
      src_platform: `${src_platform}`,
      location: `${location}`,
      invite_code: invite_code ? invite_code : undefined,
    },
    { errToast: false },
  )
}
