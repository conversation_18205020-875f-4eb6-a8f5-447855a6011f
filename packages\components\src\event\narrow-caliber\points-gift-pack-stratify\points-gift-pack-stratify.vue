<!--
 * @Description: 私域窄口径-积分礼包领取
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    :position="setting.position"
    class="points-gift-pack-stratify"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 道具展示 -->
      <Resizable
        v-model:x="setting.list.x"
        v-model:y="setting.list.y"
        v-model:width="setting.list.width"
        v-model:height="setting.list.height"
        :style="{
          backgroundImage: `url(${setting.list.bgImage})`,
        }"
      >
        <div class="points-gift-pack-stratify-list">
          <Resizable
            v-for="(item, index) in currentPack?.items"
            :x="0"
            :y="0"
            v-model:width="setting.item.width"
            v-model:height="setting.item.height"
            :movable="false"
            :style="{
              backgroundImage: `url(${setting.item.bgImage})`,
            }"
            class="points-gift-pack-stratify-list-item"
          >
            <!-- 奖品图片 -->
            <Resizable
              v-model:x="setting.item.itemImg.x"
              v-model:y="setting.item.itemImg.y"
              v-model:width="setting.item.itemImg.width"
              v-model:height="setting.item.itemImg.height"
            >
              <img
                v-if="item.imgLink"
                :src="item.imgLink"
                style="width: 100%; height: 100%;"
              />
            </Resizable>
  
            <!-- 奖品名称 -->
            <UiText
              v-model:setting="setting.item.name"
              :confined="false"
              :scrollspy="false"
              :editableContent="false"
              class="points-gift-pack-stratify-list-item-name"
            >
              {{ item.name || '--' }}
            </UiText>
          </Resizable>
        </div>

        <!-- 用于元素可见性 hook -->
        <div ref="targetRef" style="position: absolute;" />
      </Resizable>
  
      <!-- 区服-角色选择器 -->
      <template v-if="!props.setting.bindRole">
        <ServerRolePicker
          v-model:setting="setting.serverRolePicker"
          v-model:open="showServerRolePicker"
          v-model="serverRole"
          placeholder="请选择领取角色"
          @click="handleShowServerRolePicker"
          @confirm="handleServerRoleConfirm"
        >
          <template #default="{ selectedServer, selectedRole }">
            {{ `领取角色：${selectedServer?.text || ''}-${selectedRole?.text || ''}` }}
          </template>
        </ServerRolePicker>
      </template>
  
      <!-- 立即领取按钮 -->
      <template v-if="integralUserState?.receive_status !== 2">
        <UiImage
          v-if="setting.receiveBtnImage.imgLink"
          v-model:setting="setting.receiveBtnImage"
          :confined="false"
          @click="handleReceive"
        />
      </template>
      <template v-else>
        <!-- 已领取按钮 -->
        <UiImage
          v-if="setting.receivedBtnImage.imgLink"
          :setting="receivedBtnSetting"
        />
      </template>
  
      <!-- 下载按钮 -->
      <DownloadGame
        v-if="showDownloadBtn"
        v-model:setting="setting.downloadBtn"
        :confined="false"
      />
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'narrow-caliber-points-gift-pack-stratify',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, ref, computed } from 'vue';
import { useToggle, useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import usePopupStore from '@bish/store/src/modules/popup';
import useBindRole from '@bish/hooks/src/business/useBindRole';
import { useLog } from '@bish/hooks/src/useLog';
import { postAwardIntegral } from '@bish/api/src/activity';
import { useElementVisibility } from '@bish/hooks/src/useElementVisibility';
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../ui/image/index.vue';
import UiText from '../../../ui/text/index.vue';
import ServerRolePicker from '../../../business/server-role-picker.vue';
import type { ServerRolePickerValue } from '../../../business/server-role-picker.vue';
import DownloadGame from '../../../business/download-game.vue';
import { defaultConfig, itemsDefaultGiftId } from './index';
import type { PointsGiftPackStratifySetting } from './points-gift-pack-stratify';

export interface PointsGiftPackStratifyProps {
  /**
   * 配置
   */
  setting: PointsGiftPackStratifySetting
}

const props = withDefaults(defineProps<PointsGiftPackStratifyProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: PointsGiftPackStratifySetting): void
}>();

const serverRole = ref<ServerRolePickerValue>([]);
const receiving = ref(false);

const activityStore = useActivityStore();
const popupStore = usePopupStore();

const setting = useVModel(props, 'setting', emits);

const [showServerRolePicker, toggleShowServerRolePicker] = useToggle();

const { bindRole } = useBindRole();
const { uploadLog } = useLog();

const integralConfig = computed(() => activityStore.activityInfo?.config?.integral || []);

const showDownloadBtn = computed(() => {
  // 兼容旧数据
  return setting.value.downloadBtn?.enabled
    || (
      setting.value.downloadBtn.enabled === undefined
      && setting.value.downloadBtn
      && setting.value.downloadBtn.imgLink
    );
});

const receivedBtnSetting = computed(() => {
  return {
    imgLink: setting.value.receivedBtnImage.imgLink,
    x: setting.value.receiveBtnImage.x,
    y: setting.value.receiveBtnImage.y,
    width: setting.value.receiveBtnImage.width,
    height: setting.value.receiveBtnImage.height,
  };
});

/**
 * 当前用户分组id
 */
const userGroupId = computed(() => {
  return activityStore.activityAccountInfo?.user_group_id;
});

/**
 * 当前用户礼包配置
 */
const currentIntegralConfig = computed(() => {
  return integralConfig.value.find((item) => item.draw_user_group_id === userGroupId.value);
});

/**
 * 当前用户礼包信息
 */
const integralUserState = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.integral?.award_list?.find((item) => item.draw_user_group_id === userGroupId.value);
});

/**
 * 当前礼包配置列表
 */
const currentPack = computed(() => {
  return (
    setting.value.items.find((item) => item.gifPackId === currentIntegralConfig.value?.id) ||
      setting.value.items.find((item) => item.gifPackId === itemsDefaultGiftId)
  );
});

const handleShowServerRolePicker = () => {
  const pass = activityStore._checkIn(false);
  if (!pass) {
    return;
  }
  toggleShowServerRolePicker(true);
};

const handleServerRoleConfirm = () => {
  const [server_id, role_id] = serverRole.value;

  bindRole({
    server: [`${server_id}`],
    role: [+role_id],
  });
};

const handleReceive = async () => {
  // 数据上报
  uploadLog({
    event_name: 'click',
    click_id: 9,
    click_type: 3,
  });
  // 登录态拦截
  const pass = activityStore._checkIn(!!props.setting.bindRole);
  if (!pass) {
    return;
  }
  // 这里匹配不到礼包配置
  if (!integralUserState.value) {
    showToast('您不符合领取条件，如有疑问，请联系客服');
    return;
  }
  const { role_level, role_id } = activityStore.bindRoleInfo;
  // 未选择领取角色
  if (!role_id) {
    toggleShowServerRolePicker(true);
    return;
  }
  const { draw_role_level_limit = 0 } = currentIntegralConfig.value || {};

  if (+role_level < draw_role_level_limit) {
    showToast('角色等级未满足条件');
    return;
  }
  if (receiving.value) {
    return;
  }
  try {
    receiving.value = true;
    const res = await postAwardIntegral({
      act_id: activityStore.activityInfo.init?.id,
      act_acc_id: activityStore.activityAccountInfo.act_acc_id,
      integral_cfg_id: integralUserState.value.id,
    });
    if (res.code === 0) {
      // 打开下载游戏弹窗
      popupStore.setDownloadGameModal(true);

      if (!showDownloadBtn.value) {
        showToast('领取成功');
      }
    }
  } catch (error) {
    console.warn('领取积分奖励失败', error);
  } finally {
    receiving.value = false;
    // 重新获取用户与组件的数据
    activityStore.getComponentWithUserInfo();
  }
};

// 使用元素可见性 hook
const { targetRef } = useElementVisibility(() => {
  // 当组件首次出现在视口中时，上报预览事件
  uploadLog({
    event_name: 'page_view',
    exposure_id: 1, // 积分礼包
  });
});
</script>

<style>
.points-gift-pack-stratify {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.points-gift-pack-stratify :deep(.server-role-switch) {
  text-decoration: none;
}

.points-gift-pack-stratify-list {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
}

.points-gift-pack-stratify-list .points-gift-pack-stratify-list-item {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
