import { reactive, onMounted, nextTick } from 'vue';
import { checkMore } from '@bish/utils/src/utils';

const PAGE_SIZE = 15;

export type LoadTimeValue = 'first' | 'more' | 'refresh' | 'reset'
export type RunServiceResult = 'success' | 'error'

/**
 * 数据加载时机，first 表示首次加载；more 表示加载更多；refresh 下拉刷新
 */
const loadTime: Record<string, LoadTimeValue> = {
  first: 'first',
  more: 'more',
  refresh: 'refresh',
  reset: 'reset',
};

export type PaginationParams = {
  page: number,
  page_rows: number
}

export type PaginationOption = {
  loadTime: LoadTimeValue,
}

export interface ListData<L> { list: L[], count: number }

export type PaginationService<L = never> = (params: PaginationParams, option: PaginationOption) => Promise<ListData<L>>

export type PaginationOptions = {
  /**
   * 请求页数，默认 10
   */
  defaultPageSize?: number
  /**
   * 是否手动触发首次加载数据请求，如果为 false 这内部会在 onMounted 触发首次加载
   * 如果为 true，则需要手动调用 firstLoad 方法。
   * 默认 false
   */
  manual?: boolean
  /**
   * 首次获取数据后回调
   */
  afterFirstLoad?: <L>(res: ListStateType<L> | Error, type: RunServiceResult) => void
  /**
   * 加载更多数据后回调
   */
  afterLoadMore?: <L>(res: ListStateType<L> | Error, type: RunServiceResult) => void
  /**
   * 下拉刷新数据后回调
   */
  afterRefresh?: <L>(res: ListStateType<L> | Error, type: RunServiceResult) => void
}

export interface ListStateType<L> {
  list: L[]
  loading: boolean
  finished: boolean
  refreshing: boolean
  page: number
  pageSize: number
}

/**
 * @description 一个用于常见列表页的数据获取、上拉加载、下拉刷新 组合式函数
 * @param {*} service 请求方法
 * @param {*} options.defaultPageSize 页数，默认 10
 * @param {*} options.manual 是否手动触发首次加载数据
 * @param {*} options.afterFirstLoad 首次获取数据后回调
 * @param {*} options.afterLoadMore 加载更多数据后回调
 * @param {*} options.afterRefresh 下拉刷新数据后回调
 * @returns { listState, firstLoad, loadMore, refreshList }
 */
export function usePagination<L>(service: PaginationService<L>, options?: PaginationOptions) {
  const {
    defaultPageSize = PAGE_SIZE,
    manual = false,
    afterFirstLoad,
    afterLoadMore,
    afterRefresh,
  } = options || {};
  const listState = reactive({
    list: [],
    loading: true,
    finished: false,
    refreshing: false,
    page: 1,
    pageSize: defaultPageSize,
  }) as ListStateType<L>;

  const runService = (time: LoadTimeValue, extraParams?: Partial<PaginationParams>): Promise<ListData<L>> => {
    listState.loading = true;
    return new Promise((resolve, reject) => {
      const params = {
        page: listState.page,
        page_rows: listState.pageSize,
        ...extraParams,
      };
      service(
        params,
        {
          loadTime: time,
        },
      ).then(data => {
        const list = data.list || [];
        let finished = false;
        if (data.count) {
          finished = !checkMore(listState.page, listState.pageSize, list.length, data.count);
        } else {
          finished = !(list.length && list.length === params.page_rows);
        }
        listState.finished = finished;
        resolve(data);
        nextTick(() => {
          listState.loading = false;
        });
      }).catch(error => {
        // 这里首次请求出错设置 finished 为 true，
        // 否则 List 组件会不停的请求 onLoad 事件
        listState.finished = true;
        reject(error);
        nextTick(() => {
          listState.loading = false;
        });
      });
    });
  };


  /**
   * 加载首次数据
   * @param clearList 是否清空列表，默认 true，一些场景需要不清空 
   */
  const firstLoad = (clearList = true, cb?: PaginationOptions['afterFirstLoad']) => {
    listState.finished = false;
    listState.page = 1;
    if (clearList) {
      listState.list = [];
    }
    runService(loadTime.first).then(data => {
      listState.list = data.list;
      afterFirstLoad?.(listState, 'success');
      cb?.(listState, 'success');
    }).catch(error => {
      afterFirstLoad?.(error, 'error');
      cb?.(error, 'error');
    });
  };

  onMounted(() => {
    if (!manual) {
      firstLoad();
    }
  });

  /**
   * 上拉加载更多
   */
  const loadMore = () => {
    if (listState.finished || listState.loading) {
      return;
    }
    listState.page++;
    runService(loadTime.more).then(data => {
      listState.list = listState.list.concat(data.list);
      afterLoadMore?.(listState, 'success');
    }).catch(error => {
      // 回退上一页
      listState.page--;
      afterLoadMore?.(error, 'error');
    });
  };

  /*
   * 下拉刷新列表
   */
  const refreshList = () => {
    if (listState.refreshing) {
      return;
    }
    listState.page = 1;
    listState.refreshing = true;
    listState.finished = false;
    listState.list = [];
    runService(loadTime.refresh).then(data => {
      listState.list = data.list;
      listState.refreshing = false;
      afterRefresh?.(listState, 'success');
    }).catch(error => {
      listState.refreshing = false;
      afterRefresh?.(error, 'error');
    });
  };

  /*
   * 重置列表，一般用于列表删除重新获取列表操作
   */
  const resetList = () => {
    runService(
      loadTime.reset,
      {
        page: 1,
        page_rows: listState.list.length > defaultPageSize ? listState.list.length : defaultPageSize,
      },
    ).then(data => {
      listState.list = data.list;
    });
  };

  return {
    listState,
    firstLoad,
    loadMore,
    refreshList,
    resetList,
  };
}
