<!--
* @Description: 邀请好友进度条
-->
<template>
  <Milestone
    v-model:setting="vSetting"
    :percent="percent"
    :marks="vSetting.marks"
    class="invite-friends-progress"
  >
    <template #mark="{ item, isActive }">
      <div class="invite-friends-progress-mark">
        <!-- 节点内容 -->
        <Resizable
          v-model:x="vSetting.mark.content.x"
          v-model:y="vSetting.mark.content.y"
          v-model:width="vSetting.mark.content.width"
          v-model:height="vSetting.mark.content.height"
          :confined="false"
          class="invite-friends-progress-mark-content"
        >
          <ResizableProvider>
            <!-- 节点达成情况 -->
            <Resizable
              v-model:x="vSetting.mark.achieved.x"
              v-model:y="vSetting.mark.achieved.y"
              v-model:width="vSetting.mark.achieved.width"
              v-model:height="vSetting.mark.achieved.height"
              class="invite-friends-progress-mark-achieved"
              :style="{
                backgroundImage: `url(${isActive ? (item as MarksArrItem).completed : (item as MarksArrItem).incomplete})`,
              }"
            >
            </Resizable>
            <!-- 好友头像 -->
            <Resizable
              v-model:x="vSetting.avatar.x"
              v-model:y="vSetting.avatar.y"
              v-model:width="vSetting.avatar.width"
              v-model:height="vSetting.avatar.height"
              class="item-slot"
              :style="{
                backgroundImage: `url(${vSetting.avatar.bgImage})`,
              }"
              :confined="false"
            >
              <Avatar
                v-if="isActive"
                :width="vSetting.avatar.width"
                :height="vSetting.avatar.height"
                :default-avatar="vSetting.avatar.defaultAvatar"
              />
            </Resizable>
            <!-- 节点名称 -->
            <UiText
              v-if="vSetting.showMarkName"
              v-model:setting="vSetting.mark.name"
              class="invite-friends-progress-mark-name"
              :confined="false"
            >
              {{ (item as MarksArrItem).name }}
            </UiText>
          </ResizableProvider>
        </Resizable>
      </div>
    </template>
  </Milestone>
</template>

<script lang="ts">
import type { MarksArrItem } from '../../../common/milestone/index.vue';
import type { InviteFriendsProgressSetting } from './invite-friends-progress'

export interface InviteFriendsProgressProps {
  /**
   * 配置
   */
  setting: InviteFriendsProgressSetting
}

export default {
  name: 'invite-friends-progress',
};
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import UiText from '../../../ui/text/index.vue';
import Milestone from '../../../common/milestone/index.vue';
import { defaultConfig as TASK_INVITE_FRIENDS_PROGRESS_CONFIG } from './index';
import Avatar from '../../../common/avatar/index.vue'

const props = withDefaults(defineProps<InviteFriendsProgressProps>(), {
  setting: TASK_INVITE_FRIENDS_PROGRESS_CONFIG,
});

const emits = defineEmits<{
  (event: 'update:setting', value: InviteFriendsProgressSetting): void;
}>();

const activityStore = useActivityStore();

const vSetting = useVModel(props, 'setting', emits);

const taskInfo = computed(() => {
  const taskList = activityStore.componentWithUserInfo?.component_with_user_info?.task || [];
  return taskList.find((item) => String(item.id) === String(vSetting.value.taskId)) || { progress_num: 0 };
});

const percent = computed(() => {
  const { marks = [], multiple = 1 } = props.setting;
  const newMarks = [...marks];
  // 区间数
  const sectionNum = 1 / (marks.length * 2);
  // 邀请人数
  let num = taskInfo.value.progress_num; // !!!!

  // 升序
  newMarks.sort((a, b) => a.point! - b.point!);
  
  const minPoint = newMarks[0].point || 0;
  const maxPoint = newMarks[newMarks.length - 1].point || 0;

  // 超过最大里程阀值，直接 100
  if (num >= maxPoint) {
    return 100;
  }
  // 进度处在 0 到 第一节点值
  if (num <= minPoint) {
    return +((num / minPoint) * sectionNum * 100).toFixed(2);
  }
  for (let i = 0; i < newMarks.length; i++) {
    const currentPoint = newMarks[i].point || 0;
    const nextPoint = newMarks[i + 1].point || 0;;

    if (num > currentPoint && num <= nextPoint) {
      // 当前节点前面的区间数，是为 1、3、5、7 的等差数列
      const frontSectionNum = sectionNum * (2 * (i + 1) - 1);
      // sectionNum * 2，表示占两段的区间
      return +((((num - currentPoint) / (nextPoint - currentPoint)) * sectionNum * 2 + frontSectionNum) * 100).toFixed(2);
    }
  }
  return 0;
})
</script>

<style lang="less">
.invite-friends-progress {
  &-mark {
    &-item {
      > img {
        width: 100%;
        height: 100;
      }
    }

    &-content {
      .item-slot {
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }

    &-point {}

    &-name {}
  }

  &-mark-achieved {
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}
</style>
