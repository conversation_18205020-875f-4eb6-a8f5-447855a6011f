import { type } from 'os'

export type MiniGameStartGameParams = {
  /**
   * 活动账号数据 由getAccountInfo接口提供
   */
  act_acc_id: string
  /**
   * 活动id
   */
  act_id: number
}

export type MiniGameStartGameDetailType = {
  /**
   * uuid
   */
  uuid: string
}

export type MiniGameAcquireScoreParams = {
  /**
   * 活动账号数据 由getAccountInfo接口提供
   */
  act_acc_id: string
  /**
   * 活动id
   */
  act_id: number
  /**
   * 积分
   */
  score: string
  /**
   * uuid
   */
  uuid: string
}

export type MiniGameAcquireRankListParams = {
  /**
   * 用于活动小游戏个人排行榜和组队排行榜获取
   */
  /**
   * 活动id
   */
  act_id: number
  /**
   * 排行榜类型 1个人 2队伍
   */
  rank_type: number
  /**
   * 页数
   */
  page: number
  /**
   *每页数量
   */
  page_rows: number
}
