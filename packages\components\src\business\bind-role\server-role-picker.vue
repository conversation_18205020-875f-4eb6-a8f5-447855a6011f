<template>
  <Resizable
    class="server-role"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    @click="showModal"
  >
    <div :style="selectorStyle" class="server-role-selector">
      {{ placeholder }}
    </div>
    
    <Popup
      z-index="101"
      :show="open"
      v-model:setting="setting.modalSetting"
      @close="handleClose"
      @ok="handleOk"
    >
      <!-- 表单配置 -->
      <Resizable
        v-model:x="setting.modalSetting.form.position.x"
        v-model:y="setting.modalSetting.form.position.y"
        v-model:width="setting.modalSetting.form.position.width"
        v-model:height="setting.modalSetting.form.position.height"
        class="server-role-form"
      >
        <div class="server-role-form-item" @click="handleServerPicker">
          <div class="server-role-form-item-label" :style="labelStyle">
            {{ $t('bind-role-server-label', '区服') }}:
          </div>
          <div class="server-role-form-item-control">
            <div class="server-role-form-input" :style="inputStyle">
              <div class="server-role-form-input-text">
                {{ serverName }}
              </div>
              <span>
                <span :style="arrowStyle" class="server-role-arrow server-role-arrow-l" />
                <span :style="arrowStyle" class="server-role-arrow server-role-arrow-r" />
              </span>
            </div>
          </div>
        </div>
        <div class="server-role-form-item" @click="handleRolePicker">
          <div class="server-role-form-item-label" :style="labelStyle">
            {{ $t('bind-role-role-label', '角色') }}:
          </div>
          <div class="server-role-form-item-control">
            <div class="server-role-form-input" :style="inputStyle">
              <div class="server-role-form-input-text">
                {{ roleName }}
              </div>
              <span>
                <span :style="arrowStyle" class="server-role-arrow server-role-arrow-l" />
                <span :style="arrowStyle" class="server-role-arrow server-role-arrow-r" />
              </span>
            </div>
          </div>
        </div>
      </Resizable>

      <!-- 提示文案 -->
      <UiText v-if="setting.modalSetting.tipText?.content" v-model:setting="setting.modalSetting.tipText" />

      <!-- 取消按钮 -->
      <UiImage v-if="setting.modalSetting.cancelBtn?.imgLink" v-model:setting="setting.modalSetting.cancelBtn" @click.stop="handleClose" />
    </Popup>

    <!-- 区服选择 -->
    <div @touchmove="(e) => e.stopPropagation()">
      <bish-popup v-model:show="openServerPopup" position="bottom" lock-scroll>
        <!-- #ifdef WEB -->
        <template v-if="!isWeapp">
          <van-picker
            v-model="interServerValue"
            :columns="serverOptions"
            @confirm="handleServerConfirm"
            @cancel="() => toggleOpenServerPopup(false)"
          />
        </template>
        <!-- #endif -->
        <template v-if="isWeapp">
          <common-picker
            v-model="interServerValue"
            :columns="serverOptions"
            @confirm="handleServerConfirm"
            @cancel="() => toggleOpenServerPopup(false)"
          />
        </template>
      </bish-popup>
    </div>
    <!-- 角色选择选择 -->
    <div @touchmove="(e) => e.stopPropagation()">
      <bish-popup v-model:show="openRolePopup" position="bottom">
        <!-- #ifdef WEB -->
        <template v-if="!isWeapp">
          <van-picker
            v-model="interRoleValue"
            :columns="roleOptions"
            @confirm="handleRoleConfirm"
            @cancel="() => toggleOpenRolePopup(false)"
          />
        </template>
        <!-- #endif -->
        <template v-if="isWeapp">
          <common-picker
            v-model="interRoleValue"
            :columns="roleOptions"
            @confirm="handleRoleConfirm"
            @cancel="() => toggleOpenRolePopup(false)"
          />
        </template>
      </bish-popup>
    </div>
  </Resizable>
</template>

<script lang="ts">
import type { AccountInfoRoleInfo } from '@bish/api/src/activity';
import type { ServerRoleSetting, ServerRoleValue, ServerRoleOptions } from './server-role-picker';

export interface ServerRoleProps {
  /**
   * 配置
   */
  setting: ServerRoleSetting
  /**
   * 当前绑定的角色
   */
  role: AccountInfoRoleInfo
  /**
   * 值
   */
  modelValue: ServerRoleValue
  /**
   * 区服选项
   */
  serverOptions: ServerRoleOptions
  /**
   * 角色选项
   */
  roleOptions: ServerRoleOptions
  /**
   * 是否显示弹窗
   */
  open: boolean
}

export const defaultConfig = (): ServerRoleSetting => {
  return {
    x: 165,
    y: 95,
    width: 210,
    height: 23,
    themeColor: 'rgb(36, 43, 69)',
    align: 'right',
    limitSwitch: false,
    limitServer: false,
    invitee: false,
    modalSetting: {
      x: 0,
      y: 0,
      width: 373,
      height: 353,
      bgImage: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719397012420_.png',
      okBtn: {
        x: 198,
        y: 251,
        width: 121,
        height: 34,
        imgLink: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719396520604_.png',
      },
      cancelBtn: {
        x: 61,
        y: 251,
        width: 121,
        height: 34,
        imgLink: 'https://customer-media-**********.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719397019532_.png',
      },
      closeBtn: {
        x: 324,
        y: -35,
        width: 35,
        height: 35,
        imgLink: 'https://cms-**********.cos.ap-shanghai.myqcloud.com/public/2024-07-31/1722414059018-%E5%85%B3%E9%97%AD%20%E6%8B%B7%E8%B4%9D%202.png'
      },
      form: {
        position: {
          x: 56,
          y: 125,
          width: 263,
          height: 81,
        },
        input: {
          height: 31,
          background: 'rgba(26, 36, 84, 0.1)',
          color: '#1A2454',
        },
        label: {
          show: false,
          color: '#1A2454',
        },
      },
      tipText: {
        x: 68,
        y: 226,
        width: 236,
        height: 20,
        fontSize: 10,
        color: '#1A2454',
        align: 'center',
        content: '*当前绑定角色为发奖角色',
      },
    },
  };
};

export default {
  name: 'server-role-picker',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, computed, watch } from 'vue';
import type { HTMLAttributes } from 'vue';
import { useVModel, useToggle } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
// #ifdef H5
import { showConfirmDialog } from 'vant'
// #endif
import { pxTransform } from '@bish/utils/src/viewport';
import i18n from '@bish/lang/src';
import BishPopup from '@bish/ui/src/popup/Popup.vue';
import { userAgent } from '@bish/utils/src/utils';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { useEnv } from '@bish/hooks/src/useEnv';
import { applyAlphaToColor } from '../../__utils/color';
import Resizable from '../../ui/resizable/index.vue';
import UiImage from '../../ui/image/index.vue';
import UiText from '../../ui/text/index.vue';
import Popup from '../../common/popup.vue'
import CommonPicker from '../../common/picker/Picker.vue';
import type { PickerBaseEvent } from '../../common/picker/Picker';

const props = withDefaults(defineProps<ServerRoleProps>(), {
  setting: defaultConfig,
  role: () => ({} as AccountInfoRoleInfo),
  modelValue: () => ({} as ServerRoleValue),
  serverOptions: () => [],
  roleOptions: () => [],
  open: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: ServerRoleSetting): void;
  (event: 'update:open', value: boolean): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
  (event: 'update:modelValue', value: ServerRoleValue): void;
  (event: 'confirm', value: ServerRoleValue): void;
}>();

const interServerValue = ref<ServerRoleValue['server']>([]);
const interRoleValue = ref<ServerRoleValue['role']>([]);

const activityStore = useActivityStore();
const adminStore = useAdminStore();

const { isUserAgentType } = userAgent();
const isWeapp = isUserAgentType === 'WX_MINI'; // 微信小程序环境

const setting = useVModel(props, 'setting', emits);
const open = useVModel(props, 'open', emits);
const [openServerPopup, toggleOpenServerPopup] = useToggle();
const [openRolePopup, toggleOpenRolePopup] = useToggle();
const { VITE_APP_TYPE } = useEnv();

const placeholder = computed(() => {
  return (
    !props.role?.server_name ?
      `${i18n.global.t('bind-role-placeholder', '请绑定角色')} `:
      `${props.role.server_name}-${props.role.role_name}${!props.setting.limitSwitch ? i18n.global.t('bind-role-rebind') : ''}`
  );
});

const serverName = computed(() => {
  const server = props.modelValue.server?.[0]
  if (server) {
    const current = props.serverOptions.find(item => item.value === server)
    return current?.text || i18n.global.t('bind-role-server-placeholder', '请选择区服')
  }
  return i18n.global.t('bind-role-server-placeholder', '请选择区服')
});

const roleName = computed(() => {
  const role = props.modelValue.role?.[0]
  if (role) {
    const current = props.roleOptions.find(item => item.value === role)
    return current?.text || i18n.global.t('bind-role-role-placeholder', '请选择游戏角色')
  }
  return i18n.global.t('bind-role-role-placeholder', '请选择游戏角色')
});

const linearColor = computed(() => {
  return props.setting.themeColor ? applyAlphaToColor(props.setting.themeColor, 0.01) : props.setting.themeColor;
});

const selectorStyle = computed(() => {
  const { setting } = props;
  return {
    backgroundImage: `linear-gradient(to ${setting.align === 'left' ? 'right' : 'left'}, ${setting.themeColor}, ${linearColor.value})`,
    justifyContent: setting.align === 'left' ? 'flex-start' : 'flex-end',
    padding: `0 ${pxTransform(8)}`,
    fontSize: pxTransform(12),
  } as HTMLAttributes['style'];
});

const labelStyle = computed(() => {
  const { setting } = props;
  let width = 38;
  let textAlign = 'left';

  if (i18n.global.locale === 'en-US') {
    width = 76;
    textAlign = 'right';
  }
  return {
    display: setting.modalSetting.form.label.show ? 'block': 'none',
    color: setting.modalSetting.form.label.color,
    fontSize: pxTransform(16),
    marginRight: `${pxTransform(14)}`,
    width: `${pxTransform(width)}`,
    textAlign,
  } as HTMLAttributes['style'];
});

const inputStyle = computed(() => {
  const { setting } = props;
  return {
    color: setting.modalSetting.form.input.color,
    backgroundColor: setting.modalSetting.form.input.background,
    height: pxTransform(setting.modalSetting.form.input.height!),
    padding: `0 ${pxTransform(12)}`,
    fontSize: pxTransform(12),
  } as HTMLAttributes['style'];
});

const arrowStyle = computed(() => {
  const { setting } = props;
  return {
    backgroundColor: applyAlphaToColor(setting.modalSetting.form.input.color!, 0.7),
  } as HTMLAttributes['style'];
});

watch(
  () => props.modelValue.server,
  (newVal) => {
    interServerValue.value = newVal
  },
  { immediate: true },
)

watch(
  () => props.modelValue.role,
  (newVal) => {
    interRoleValue.value = newVal
  },
  { immediate: true },
)

const initializeRole = () => {
  const { roleOptions, modelValue: { server, role } } = props;
  // 默认选中第一个角色
  if (roleOptions.length && server?.length && !role?.length) {
    emits('update:modelValue', { ...props.modelValue, role: [+roleOptions[0].value] });
  }
};

watch(
  () => props.roleOptions,
  () => {
    initializeRole();
  },
  { immediate: true },
);

const showModal = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }
  if (props.role?.server_name && props.setting.limitSwitch) {
    return;
  }
  const pass = activityStore._checkIn(false);
  if (!pass) {
    return;
  }
  open.value = true;
};

const handleClose = () => {
  open.value = false;
};

const handleServerConfirm = (e: PickerBaseEvent) => {
  if (!e.selectedValues.length) {
    showToast(i18n.global.t('bind-role-server-tip', '请选择区服'));
    return;
  }
  toggleOpenServerPopup(false);
  // 防止重复提交
  if (e.selectedValues?.[0] === props.modelValue.server?.[0]) {
    return;
  }
  emits('update:modelValue', { ...props.modelValue, server: [...e.selectedValues] as ServerRoleValue['server'], role: [] });
}

const handleRoleConfirm = (e: PickerBaseEvent) => {
  if (!e.selectedValues.length) {
    showToast(i18n.global.t('bind-role-role-tip', '请选择游戏角色'));
    return;
  }
  toggleOpenRolePopup(false);
  // 防止重复提交
  if (e.selectedValues?.[0] === props.modelValue.role?.[0]) {
    return;
  }
  emits('update:modelValue', { ...props.modelValue, role: [...e.selectedValues] as ServerRoleValue['role'] });
}

const handleOk = () => {
  const { modelValue } = props;
  if (!modelValue.server.length) {
    showToast(i18n.global.t('bind-role-server-tip', '请选择区服'));
    return;
  }
  if (!modelValue.role.length) {
    showToast(i18n.global.t('bind-role-role-tip', '请选择游戏角色'));
    return;
  }
  // 限制换绑
  if (props.setting.limitSwitch) {
    // 微信小程序api不一样
    if (VITE_APP_TYPE === 'wx') {
      uni.showModal({
        title: '提示',
        content: '账号有且仅能绑定一个角色，角色绑定后不支持换绑',
        success: (res) => {
          if (res.confirm) {
            emits('update:modelValue', modelValue);
            emits('confirm', modelValue);
          }
        }
      })
    } else {
      showConfirmDialog({
        title: '提示',
        message: `账号有且仅能绑定一个角色，角色绑定后不支持换绑`,
        beforeClose: (action) =>
          new Promise((closeResolve) => {
            if (action === 'cancel') {
              closeResolve(true);
            }
            if (action === 'confirm') {
              closeResolve(true);
              emits('update:modelValue', modelValue);
              emits('confirm', modelValue);
            }
          }),
      })
    }
    return;
  }
  emits('update:modelValue', modelValue);
  emits('confirm', modelValue);
}

const handleServerPicker = () => {
  if (!props.serverOptions.length) {
    showToast('暂未有满足条件的区服');
    return;
  }
  toggleOpenServerPopup(true);
}

const handleRolePicker = () => {
  if (!props.roleOptions.length) {
    showToast('请先选择区服');
    return;
  }
  toggleOpenRolePopup(true);
}
</script>

<style lang="less">
.server-role {
  &-selector {
    display: flex;
    align-items: center;
    height: 100%;
    color: #ffffff;
  }

  &-form-wrapper {
    // padding-top: 270px;
    position: relative;
    background-repeat: no-repeat;
    background-size: 100% auto;
    box-sizing: border-box;
  }

  &-form {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-item {
      display: flex;
      align-items: center;

      &-label {
        font-weight: 600;
      }

      &-control {
        flex: 1;
      }
    }

    &-input {
      height: 44px;
      display: flex;
      align-items: center;

      &-text {
        flex: 1;
      }
    }
  }

  &-arrow {
    height: 2PX;
    width: 7PX;
    display: inline-block;
    position: relative;
    top: -2px;

    &-l {
      transform: rotate(40deg);
    }

    &-r {
      transform: rotate(-40deg);
      margin-left: -2PX;
    }
  }
}
</style>