import type { CommonSetting } from '@bish/types/src';
import type { ImageSetting } from '../../../ui/image/index.vue';
import type { TextSetting } from '../../../ui/text/index.vue';
import type { MyWalletSetting } from '../my-wallet';
import type { TaskProgressSetting } from '../../../business/task-collection/task-progress/task-progress';
import type { PopupSetting } from '../../../common/popup.vue';

export interface WithdrawalTaskProgressSetting extends CommonSetting {
  /**
   * 进度条
   */
  progress: TaskProgressSetting
  /**
   * 红包图片
   */
  redPacket: ImageSetting
  /**
   * 我的钱包
   */
  myWallet: MyWalletSetting
  /**
   * 提示语
   */
  tips: TextSetting
  /**
   * 总是显示, 1是 0否，默认 0
   */
  alwaysShow: number
  /**
   * 暴击任务id
   */
  taskIdHit: number
  /**
   * 暴击引导充值弹窗
   */
  hit: PopupSetting & {
    /**
     * 标题
     */
    title: TextSetting
    /**
     * 副标题
     */
    subTitle: TextSetting
    /**
     * 放弃按钮
     */
    cancelBtn: ImageSetting
    /**
     * 确认按钮
     */
    confirmBtn: ImageSetting
    /**
     * 放弃提示
     */
    cancelTips: TextSetting
  }
  /**
   * 暴击引导弹窗
   */
  bullseye: PopupSetting & {
    /**
     * 标题
     */
    title: TextSetting
    /**
     * 放弃按钮
     */
    cancelBtn: ImageSetting
    /**
     * 确认按钮
     */
    confirmBtn: ImageSetting
  }
  /**
   * 是否内嵌微信小程序，1是 0否
   */
  weappWebview: number
  /**
   * 内嵌中间页配置
   */
  weappEmbedSetting?: MallRechargeWeappEmbedSetting
}

/**
 * 提现状态，分别为：未开始 | 进行中 | 已完成任务 | 未提现 | 已提现 | 已结束
 */
export type WithdrawalStatus = 'not-started' | 'in-progress' | 'completed' | 'not-withdrawn' | 'cashed' | 'ended';

export interface MallRechargeWeappEmbedSetting {
  /**
   * 弹窗基础配置
   */
  modalSetting: PopupSetting
}

