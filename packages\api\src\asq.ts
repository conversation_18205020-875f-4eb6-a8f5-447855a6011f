import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'

const { VITE_ACTIVITY_URL } = useEnv()

export type AsqTopicParams = {
  act_id: number
}

export type AsqTopicData = {
  id: number
  activity_id: number
  topic: number
  explain: number
  /**
   * 类型 1单选 2多选 3填空
   */
  type: 1 | 2 | 3
  /**
   * 状态 1为禁用 2为启用
   */
  status: 1 | 2
  /**
   * 最多可选项 多选题限制选择选项数量
   */
  max_option: number
  /**
   * 选项 填空题返回空数组
   */
  options: {
    id: number
    topic_id: number
    /**
     * 选项
     */
    option: string
    /**
     * 是否其他选项 1为否 2为是
     */
    is_other: number
  }[]
}[]

/**
 * @description: 获取问卷内容
 * @param {object} params
 * @return {*}
 */
export function postAsqTopic(params: AsqTopicParams) {
  return http.post<AsqTopicData>(`${VITE_ACTIVITY_URL}/activity/asq/get_topic`, params)
}

export type AsqSubmitParams = {
  act_id: number
  act_acc_id: string
  answers: {
    topic_id: number
    option_ids: number[]
    answer: string
  }[]
}

/**
 * @description: 提交问卷答案
 * @param {object} params
 * @return {*}
 */
export function postAsqSubmit(params: AsqSubmitParams) {
  return http.post<AsqTopicData>(`${VITE_ACTIVITY_URL}/activity/asq/submit`, params, {
    headers: { 'Content-Type': 'application/json' },
  })
}