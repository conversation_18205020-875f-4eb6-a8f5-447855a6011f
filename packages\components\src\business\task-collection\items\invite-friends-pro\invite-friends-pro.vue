<!--
* @Description: 邀请好友/邀请好友预创角任务（带海报版）
-->
<template>
  <TaskItem v-model:setting="setting">
    <!-- 去邀请 -->
    <template #doBtn="{ show }">
      <template v-if="show">
        <UiImage
          :setting="setting.doBtn"
          @click="handleClaim"
          :confined="false"
        />
        <!-- 去邀请：微信小程序环境 -->
        <button
          v-if="showShareBtn"
          :style="shareBtnStyle"
          class="task-item-wx-btn"
          open-type="share"
          @click="handleWeappShare"
        />
      </template>
    </template>

    <!-- 邀请组队弹窗 -->
    <InviteModalPro
      :show="openInvite"
      :keyName="friendInvitationKey"
      :bindRole="!!setting.bindRole"
      v-model:setting="setting.inviteCard"
      @close="() => toggleInvite(false)"
    />

    <!-- 接受队伍弹窗 -->
    <InvitationModal
      :show="showInvitation"
      v-model:setting="setting.invitationCard"
      :inviter="friend"
      @close="() => toggleInvitation(false)"
      @ok="handleAccept"
    />

    <!-- 微信小程序内嵌-引导右上角分享 -->
    <ShareGuide
      v-if="setting.shareGuide"
      :show="openWeappShareGuide"
      v-model:setting="setting.shareGuide"
      @close="() => toggleWeappShareGuide(false)"
    />

    <!-- 分享提示 -->
    <Popup
      v-if="setting.shareTips?.open"
      z-index="100"
      :show="openWeappShareGuide"
      :lock-scroll="false"
      :overlay="false"
      position="bottom"
      :contentStyle="shareTipsContentStyle"
      v-model:setting="setting.shareTips"
    >
      <!-- 操作按钮 -->
      <UiImage v-if="setting.shareTips?.actionBtn?.imgLink" v-model:setting="setting.shareTips.actionBtn" />
    </Popup>
    
    <!-- 接受邀请后二维码弹窗 -->
    <Popup
      v-if="setting.invitationCard.afterAccept?.optionType === 2"
      z-index="100"
      :show="openInvitedPopup"
      :lock-scroll="false"
      v-model:setting="setting.invitationCard.afterAccept.popup"
      @close="toggleInvitedPopup(false)"
    >
      <UiImage
        v-if="imgSetting?.imgLink"
        :setting="imgSetting"
        :movable="false"
        :resizable="false"
        show-menu-by-longpress
      />
    </Popup>
  </TaskItem>
</template>

<script lang="ts">
export default {
  name: 'task-invite-friends-pro',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, computed, watch, onMounted } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import type { CustomShareContent } from '@bish/store/src/modules/activityPage';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { useActivityPageConfig } from '@bish/hooks/src/business/useActivityPageConfig';
import type { ShareMessageConfigType } from '@bish/hooks/src/business/useActivityPageConfig';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { useFriendInvitation } from '@bish/hooks/src/business/useFriendInvitation';
import { useLog } from '@bish/hooks/src/useLog';
import type { StatefulComponent } from '@bish/types/src/admin';
import { userAgent, maskPhoneNumber } from '@bish/utils/src/utils';
import { pxTransform } from '@bish/utils/src/viewport';
import useRouteQuery from '@bish/hooks/src/useRouteQuery';
import { showToast } from '@bish/ui/src/toast';
import { postActivityTaskUploadTask } from '@bish/api/src/activity';
import localStorage from '@bish/utils/src/storage/localStorage';
import { defaultConfig as TASK_INVITE_FRIENDS_CONFIG } from './index';
import type { TaskInviteFriendsProSetting } from './invite-friends-pro';
import { navigateScroll } from '../../../../__utils/location';
import UiImage from '../../../../ui/image/index.vue';
import InviteModalPro from '../../../../business/invite-modal-pro/invite-modal-pro.vue';
import InvitationModal from '../../../../business/invitation-modal.vue';
import Popup from '../../../../common/popup.vue';
import ShareGuide from '../../../../business/share-guide/share-guide.vue';
import TaskItem from '../item.vue';

export interface TaskInviteFriendsProProps {
  /**
   * 配置
   */
  setting: TaskInviteFriendsProSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TaskInviteFriendsProProps>(), {
  setting: () => TASK_INVITE_FRIENDS_CONFIG(),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskInviteFriendsProSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityPageStore = useActivityPageStore();
const userStore = useUserStore();
const activityStore = useActivityStore();
const adminStore = useAdminStore();

const { setWeappShareMessage } = useActivityPageConfig();

const [openInvite, toggleInvite] = useControllableStatus(props, emits, { fieldName: 'showInvite' });
const [openWeappShareGuide, toggleWeappShareGuide] = useControllableStatus(props, emits, { fieldName: 'showWeappShareGuide' });
const [openInvitedPopup, toggleInvitedPopup ] = useControllableStatus(props, emits, { fieldName: 'showInvitedPopup' });
const friendInvitationKey = props.setting.cid || 'invite_friends'; // 弹窗标识
const { showInvitation, toggleInvitation, handleAccept, handleReject, friend } = useFriendInvitation(
  friendInvitationKey,
  activityStore._checkIn,
  true,
  () => {
    // 接受邀请后续操作（跳转到指定页面/弹出弹窗）
    const optionType = props.setting.invitationCard.afterAccept?.optionType;
    if (optionType === 1) {
      setTimeout(() => {
        const link = props.setting.invitationCard.afterAccept?.link;
        if (link) {
          navigateScroll(link);
        }
      }, 800);
    } else if (optionType === 2) {
      toggleInvitedPopup(true);
    }
  },
  props,
  emits, 
  {
    fieldName: 'showInvitation',
  },
);

const setting = useVModel(props, 'setting', emits);

const { uploadLog } = useLog();
const { query } = useRouteQuery();

const preOperationInviteFriends = query.value.pre_op === 'invite-friends';

const { isUserAgentType } = userAgent();

// 微信小程序环境
const isWeapp = computed(() => {
  return isUserAgentType === 'WX_MINI';
});

const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

const userTask = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
});

const showShareBtn = computed(() => {
  return isWeapp.value && activityStore.activityAccountInfo?.account_id && !activityStore.isSummit(false);
});

const shareTipsContentStyle = computed<CSSProperties>(() => {
  return {
    // left: '50%',
    // transform: 'translateX(-50%)',
  }
});

const shareBtnStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.doBtn?.x || 0),
    top: pxTransform(setting.value.doBtn?.y || 0),
    width: pxTransform(setting.value.doBtn?.width || 0),
    height: pxTransform(setting.value.doBtn?.height || 0),
  };
});

const imgSetting = computed(() => {
  const popup = setting.value.invitationCard.afterAccept?.popup;
  return {
    x: popup?.x || 0,
    y: popup?.y || 0,
    width: popup?.width || 0,
    height: popup?.height || 0,
    imgLink: popup?.bgImage || 0,
  };
})

/**
 * 初始展示弹窗
 */
const initOpenModal = () => {
  if (preOperationInviteFriends) {
    toggleInvite(true);
  }
};

onMounted(() => {
  initOpenModal();
});

const uploadLogInvite = () => {
  // 事件上报：点击邀请
  uploadLog({
    event_name: 'click',
    click_id: 10,
    click_type: 3,
  });
};

/**
 * 设置分享参数
 */
const sendShareMessage = (uploadLog = true) => {
  const { userData } = userStore;
  const { activityAccountInfo } = activityStore;
  let shareParams: Record<string, any> = {};
  
  if (!userData.token) {
    return;
  }
  
  // 存在有一些活动不需要绑定角色
  let invite_user = maskPhoneNumber(userData?.phone_number) || activityAccountInfo?.account_id || '';
  
  // 已经登录了，并且绑定角色
  if (activityAccountInfo?.role_info?.server_name) {
    invite_user = `${activityAccountInfo.role_info?.server_name}-${activityAccountInfo.role_info?.role_name}`;
  }
  const scene_id = localStorage.getLocalStorage('SY_LOG_DATA_scene_id') || '';
  shareParams = {
    invite_code: activityAccountInfo.invite_code,
    recall_account_id: encodeURIComponent(activityAccountInfo.account_id),
    invite_user: encodeURIComponent(invite_user),
    invite_type: 1,
    [friendInvitationKey]: 1,
  };
  if (scene_id) {
    shareParams.scene_id = scene_id;
  }
  // 随机选择一条邀请文案
  const list = setting.value.inviteCard?.link?.copyTemplates || [];
  const randomIndex = Math.floor(Math.random() * list.length);
  const current = list[randomIndex];
  // 微信小程序环境
  if (isUserAgentType === 'WX_MINI') {
    const shareContent: Partial<CustomShareContent> = {
      pathQuery: shareParams,
    }
    if (current?.text) {
      shareContent.title = current?.text;
    }
    if (current?.poster) {
      shareContent.imageUrl = current?.poster;
    }
    activityPageStore.setShareContent(shareContent);
  }
  // 设置微信小程序内嵌分享参数
  if (isUserAgentType === 'WX_WEBVIEW') {
    const shareContent: ShareMessageConfigType = {};
    if (current?.text) {
      shareContent.title = current?.text;
    }
    if (current?.poster) {
      shareContent.imgUrl = current?.poster;
    }
    setWeappShareMessage(shareParams, shareContent);
  }
  if (uploadLog) {
    uploadLogInvite();
  }
}

watch(
  () => activityStore.activityAccountInfo.invite_code,
  (newVal) => {
    // 微信小程序环境 or 微信小程序内嵌环境
    if ((isUserAgentType === 'WX_MINI' || isUserAgentType === 'WX_WEBVIEW') && newVal) {
      // 这里延迟各 300 毫毛，场景是：邀请好友模块 跟 组队模块 共存时，需要确保 邀请组队 的参数优先级>邀请好友
      // 邀请好友无需延迟 0 或者 不延迟
      sendShareMessage();
    }
  },
  {
    immediate: true,
  },
);

const handleClaim = async () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return
  }

  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfig.value) {
    showToast('活动已结束~');
    return;
  }

  // 事件上报：点击邀请
  uploadLogInvite();

  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }

  if (setting.value.notAccomplishTip) {
    showToast(setting.value.notAccomplishTip);
  }

  // 伪任务自动完成
  if (setting.value.isFakeTask === 1) {
    try {
      await postActivityTaskUploadTask({
        act_id: activityStore.activityInfo.init?.id,
        act_acc_id: activityStore.activityAccountInfo.act_acc_id,
        task_code: taskConfig.value?.task_code!,
        task_id: userTask?.value?.id!,
      });
    } catch (error) {
      console.warn('上报任务完成失败', error);
    } finally {
      activityStore.getComponentWithUserInfo();
    }
  }

  // 微信小程序内嵌环境：弹出右上角分享引导
  if (isUserAgentType === 'WX_WEBVIEW') {
    sendShareMessage(false);
    toggleWeappShareGuide(true);
    return;
  }

  if (setting.value.notAccomplishUrl) {
    navigateScroll(setting.value.notAccomplishUrl);
    return;
  }

  toggleInvite(true);
};

const handleWeappShare = () => {
  uploadLogInvite();

  // 设置微信页面分享参数
  sendShareMessage();
};
</script>

<style>
.task-item {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.task-item-wx-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: transparent;
}

.task-item-wx-btn::after {
  border: none;
}
</style>
