import { Subscribable } from '@bish/shared/src/subscribable'

export class Observer<Payload = any> {
  private subscribable: Subscribable<Payload>;

  constructor() {
    this.subscribable = new Subscribable<Payload>();
  }

  public subscribe(callback: (payload: Payload) => void) {
    this.subscribable.subscribe(callback);
  }

  public notify(payload?: Payload): void {
    this.subscribable.notify(payload);
  }

  public unsubscribe(): void {
    this.subscribable.unsubscribe();
  }
}

