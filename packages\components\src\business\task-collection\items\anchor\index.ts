import { commonTaskConfig } from '../index';
import type { TaskAnchorSetting } from './anchor';

export * from './anchor';

export const defaultConfig = (): TaskAnchorSetting => {
  return {
    ...commonTaskConfig(),
    width: 315,
    height: 96,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250414/1744632285375-task-item-bg.png',
    title: {
      x: 81,
      y: 31,
      width: 106,
      height: 20,
      fontSize: 12,
      color: '#763920',
      content: '登录活动绑定角色',
      fontWeight: true,
    },
    description: {
      x: 81,
      y: 49,
      width: 93,
      height: 20,
      fontSize: 9,
      color: '#a45433',
      content: '随机卡片*1  ',
    },
    process: {
      x: 132,
      y: 49,
      width: 79,
      height: 20,
      fontSize: 9,
      color: '#a45433',
      content: '({{process}})',
    },
    doBtn: {
      x: 199,
      y: 31,
      width: 86,
      height: 30,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250414/1744632342860-do-btn.png',
    },
    achievedBtn: {
      x: 199,
      y: 31,
      width: 86,
      height: 30,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250414/1744635093168-claimed-btn.png',
      enabled: false,
    },
    claimBtn: {
      x: 199,
      y: 31,
      width: 86,
      height: 30,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250414/1744632349795-claim-btn.png',
      enabled: false,
    },
    url: '',
  }
};