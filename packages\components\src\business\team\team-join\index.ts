import type { TeamJoinSetting } from './team-join';

export * from './team-join';

/**
 * 队伍状态-正常
 */
export const TEAM_STATUS_NORMAL = 1;
/**
 * 队伍状态-解散
 */
export const TEAM_STATUS_DISBANDED = 2;
/**
 * 队伍状态-满员
 */
export const TEAM_STATUS_FULL = 3;

export const defaultConfig = (): TeamJoinSetting => {
  return {
    x: 0,
    y: 0,
    width: 238,
    height: 93,
    teamId: 0,
    affix: 1,
    joinBtn: {
      x: 20,
      y: 20,
      width: 198,
      height: 53,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736933241371-join-e.png',
      enabled: true,
    },
    joinedBtn: {
      x: 39,
      y: 20,
      width: 159,
      height: 52,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250116/1736998595565-joined-e.png',
      enabled: true,
    },
    modal: {
      x: 0,
      y: 0,
      width: 360,
      height: 440,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736933249207-team-list.png',
      closeBtn: {
        x: 310,
        y: 8,
        width: 32,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250114/1736863567849-close.png',
      },
      list: {
        x: 52,
        y: 104,
        width: 258,
        height: 294,
      },
      item: {
        height: 46,
        gutter: 0,
        name: {
          width: 150,
          height: 45,
          fontSize: 10,
          color: '#363636',
          alignItems: 'center',
        },
        count: {
          width: 54,
          height: 45,
          fontSize: 10,
          color: '#363636',
          align: 'center',
          alignItems: 'center',
        },
      },
      createBtn: {
        x: 132,
        y: 335,
        width: 97,
        height: 32,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736933264143-go-create.png',
        enabled: true,
      },
      joinBtn: {
        x: 0,
        y: 0,
        width: 52,
        height: 20,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736933324352-join.png',
      },
      fullBtn: {
        x: 0,
        y: 0,
        width: 52,
        height: 20,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736933318581-full.png',
      },
      joinedBtn: {
        x: 0,
        y: 0,
        width: 52,
        height: 20,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736933334664-joined.png',
      },
    },
    confirmModal: {
      x: 0,
      y: 0,
      width: 360,
      height: 260,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736945454084-invitation-bg.png',
      closeBtn: {
        x: 310,
        y: 8,
        width: 32,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250114/1736863567849-close.png',
      },
      okBtn: {
        x: 190,
        y: 173,
        width: 97,
        height: 32,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736922160241-accept.png',
      },
      cancelBtn: {
        x: 73,
        y: 173,
        width: 97,
        height: 32,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736922165415-reject.png',
        enabled: true,
      },
      content: {
        x: 70,
        y: 91,
        width: 220,
        height: 64,
        fontSize: 12,
        color: '#363636',
        align: 'center',
        alignItems: 'center',
        content: '<div style="line-height: 1.8;">是否确定加入</br><span style="color: #EC5B23;">{{teamName}}</span> 的队伍？</div>',
        enabled: true,
      },
    },
  }
};
