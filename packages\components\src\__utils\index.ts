// #ifdef H5
import { showConfirmDialog } from 'vant'
// #endif
import { isIpadOS } from '@bish/utils/src/isMobile'
import { useEnv } from '@bish/hooks/src/useEnv'
import { showToast } from '@bish/ui/src/toast';
import { copyText } from './text'

const { VITE_APP_TYPE } = useEnv()

/**
 * @description 下载游戏
 * @param downloadUrl 下载地址
 */
export const downloadGame = (downloadUrl: string, guided = true) => {
  if (!downloadUrl) {
    showToast(`暂无游戏下载链接~`);
    return;
  }
  if (window && !guided) {
    window.open(downloadUrl)
    return
  }
  const ua = navigator?.userAgent.toLowerCase()
  const isIOSWechat = /micromessenger/i.test(ua) && (/(iphone|ipad|ipod|ios)/i.test(ua) || isIpadOS())
  // 下载页在iOS微信环境无法正常引导用户【长安春节裂变活动新增处理逻辑】
  if (window && !isIOSWechat) {
    window.open(downloadUrl)
  } else {
    // 微信小程序api不一样
    if (VITE_APP_TYPE === 'wx') {
      uni.showModal({
        title: '温馨提示',
        content: '请点击下方复制按钮，前往手机浏览器打开链接进行下载',
        confirmText: '复制链接',
        cancelText: '取消',
        confirmColor: 'red',
        success: (res) => {
          if (res.confirm) {
            copyText(downloadUrl)
          }
        }
      })
    } else {
      showConfirmDialog({
        title: '温馨提示',
        message: '请点击下方复制按钮，前往手机浏览器打开链接进行下载',
        confirmButtonText: '复制链接',
        cancelButtonText: '取消',
        confirmButtonColor: 'red',
      })
        .then(() => {
          copyText(downloadUrl)
        })
        .catch(() => {
          // 暂不下载
        })
    }
  }
}