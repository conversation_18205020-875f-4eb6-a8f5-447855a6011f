<!--
* @Description: 邀请流失好友
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="task-item"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 标题 -->
      <UiText v-if="setting.title?.content" v-model:setting="setting.title" />

      <!-- 描述 -->
      <UiText v-if="setting.description?.content" v-model:setting="setting.description" />
      
      <!-- 完成进度 -->
      <UiText
        v-if="setting.process"
        v-model:setting="setting.process"
        :interpolation="processInterpolation"
        :confined="false"
      />

      <!-- 去邀请 -->
      <UiImage
        :setting="setting.claimBtn"
        @click="handleClaim"
        :confined="false"
      />
      <!-- 去邀请：微信小程序环境 -->
      <button
        v-if="showShareBtn"
        :style="shareBtnStyle"
        class="task-item-wx-btn"
        open-type="share"
        @click="handleWeappShare"
      />
    </ResizableProvider>

    <!-- 邀请组队弹窗 -->
    <InviteModal
      :show="openInvite"
      :keyName="friendInvitationKey"
      :bindRole="!!setting.bindRole"
      v-model:setting="setting.inviteCard"
      @close="() => toggleInvite(false)"
    />

    <!-- 接受队伍弹窗 -->
    <InvitationModal
      :show="showInvitation"
      v-model:setting="setting.invitationCard"
      :inviter="friend"
      @close="() => toggleInvitation(false)"
      @ok="handleAccept"
    />

    <!-- 微信小程序内嵌-引导右上角分享 -->
    <ShareGuide
      :show="openWeappShareGuide"
      @update:show="(value) => toggleWeappShareGuide(value)"
    />

    <!-- 分享提示 -->
    <Popup
      v-if="setting.shareTips?.open"
      z-index="100"
      :show="openWeappShareGuide"
      :lock-scroll="false"
      :overlay="false"
      position="bottom"
      :contentStyle="shareTipsContentStyle"
      v-model:setting="setting.shareTips"
    >
      <!-- 操作按钮 -->
      <UiImage v-if="setting.shareTips?.actionBtn?.imgLink" v-model:setting="setting.shareTips.actionBtn" />
    </Popup>

    <!-- 流失好友弹窗 -->
    <LossFriendsModal
      :show="openLossFriends"
      v-model:setting="setting.lossFriendsModal"
      :friends-list="dataSource"
      @close="() => toggleLossFriends(false)"
      @handleInvite="handleInvite"
    />    
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'task-invite-loss-friends',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, computed, watch, onMounted, ref } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import type { CustomShareContent } from '@bish/store/src/modules/activityPage';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { useActivityPageConfig } from '@bish/hooks/src/business/useActivityPageConfig';
import type { ShareMessageConfigType } from '@bish/hooks/src/business/useActivityPageConfig';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { useFriendInvitation } from '@bish/hooks/src/business/useFriendInvitation';
import { useLog } from '@bish/hooks/src/useLog';
import type { StatefulComponent } from '@bish/types/src/admin';
import { userAgent, maskPhoneNumber } from '@bish/utils/src/utils';
import { pxTransform } from '@bish/utils/src/viewport';
import useRouteQuery from '@bish/hooks/src/useRouteQuery';
import { showToast } from '@bish/ui/src/toast';
import localStorage from '@bish/utils/src/storage/localStorage';
import { defaultConfig as TASK_INVITE_FRIENDS_CONFIG } from './index';
import type { TaskInviteLossFriendsSetting } from './invite-loss-friends';
import { navigateScroll } from '../../../../__utils/location';
import Resizable from '../../../../ui/resizable/index.vue';
import ResizableProvider from '../../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../../ui/image/index.vue';
import UiText from '../../../../ui/text/index.vue';
import InviteModal from '../../../../business/invite-modal.vue';
import InvitationModal from '../../../../business/invitation-modal.vue';
import ShareGuide from '../../../../common/share-guide/share-guide.vue';
import Popup from '../../../../common/popup.vue';
import LossFriendsModal from './components/loss-friends-modal.vue';
import { postLossFriendsList } from '@bish/api/src/activity';

export interface TaskCollectionProps {
  /**
   * 配置
   */
  setting: TaskInviteLossFriendsSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TaskCollectionProps>(), {
  setting: () => TASK_INVITE_FRIENDS_CONFIG(),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskInviteLossFriendsSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityPageStore = useActivityPageStore();
const userStore = useUserStore();
const activityStore = useActivityStore();
const adminStore = useAdminStore();

const { setWeappShareMessage } = useActivityPageConfig();

const [openInvite, toggleInvite] = useControllableStatus(props, emits, { fieldName: 'showInvite' });
const [openWeappShareGuide, toggleWeappShareGuide] = useControllableStatus(props, emits, { fieldName: 'showWeappShareGuide' });
const [openLossFriends, toggleLossFriends] = useControllableStatus(props, emits, { fieldName: 'showLossFriends' });
const friendInvitationKey = props.setting.cid || 'invite_friends'; // 弹窗标识
const { showInvitation, toggleInvitation, handleAccept, handleReject, friend } = useFriendInvitation(
  friendInvitationKey,
  activityStore._checkIn,
  true,
  () => {},
  props,
  emits, 
  {
    fieldName: 'showInvitation',
  }
);

const setting = useVModel(props, 'setting', emits);
const { uploadLog } = useLog();
const { query } = useRouteQuery();

const preOperationInviteFriends = query.value.pre_op === 'invite-friends';

const { isUserAgentType } = userAgent();

const friendsList = ref<any[]>([]);

const dataSource = computed(() => {
  if (adminStore.editable) {
    return [
      {
        id: 14,
        friend_info: {
          role_name: '玩家昵称1',
        },
      },
      {
        id: 14,
        friend_info: {
          role_name: '玩家昵称2',
        },
      },
      {
        id: 14,
        friend_info: {
          role_name: '玩家昵称3',
        },
      },
      {
        id: 14,
        friend_info: {
          role_name: '玩家昵称4',
        },
      },
    ];
  }
  return friendsList.value;
});

// 微信小程序环境
const isWeapp = computed(() => {
  return isUserAgentType === 'WX_MINI';
});

const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

const userTask = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
});

const process = computed(() => {
  return `${userTask.value?.progress_num || 0}/${taskConfig.value?.target_num || 0}`;
});

const processInterpolation = computed(() => {
  return {
    process: process.value,
  };
});

const showShareBtn = computed(() => {
  return isWeapp.value && activityStore.activityAccountInfo?.account_id && !activityStore.isSummit(false);
});

const shareTipsContentStyle = computed<CSSProperties>(() => {
  return {
    // left: '50%',
    // transform: 'translateX(-50%)',
  }
});

const shareBtnStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.claimBtn.x || 0),
    top: pxTransform(setting.value.claimBtn.y || 0),
    width: pxTransform(setting.value.claimBtn.width || 0),
    height: pxTransform(setting.value.claimBtn.height || 0),
  };
});

/**
 * 初始展示弹窗
 */
const initOpenModal = () => {
  if (preOperationInviteFriends) {
    toggleInvite(true);
  }
};

onMounted(() => {
  initOpenModal();
});

const uploadLogOpenLossFriendsModal = () => {
  // 事件上报：打开弹窗
  uploadLog({
    event_name: 'click',
    click_id: 150,
    click_type: 3,
  });
};

const uploadLogInvite = () => {
  // 事件上报：点击邀请
  uploadLog({
    event_name: 'click',
    click_id: 151,
    click_type: 3,
  });
};

const handleInvite = (item: any) => {
  // 事件上报：点击邀请
  uploadLogInvite();
  toggleInvite(true);
};

/**
 * 设置分享参数
 */
const sendShareMessage = (uploadLog = true) => {
  const { userData } = userStore;
  const { activityAccountInfo } = activityStore;
  let shareParams: Record<string, any> = {};
  
  if (!userData.token) {
    return;
  }
  
  // 存在有一些活动不需要绑定角色
  let invite_user = maskPhoneNumber(userData?.phone_number) || activityAccountInfo?.account_id || '';
  
  // 已经登录了，并且绑定角色
  if (activityAccountInfo?.role_info?.server_name) {
    invite_user = `${activityAccountInfo.role_info?.server_name}-${activityAccountInfo.role_info?.role_name}`;
  }
  const scene_id = localStorage.getLocalStorage('SY_LOG_DATA_scene_id') || '';
  shareParams = {
    invite_code: activityAccountInfo.invite_code,
    recall_account_id: encodeURIComponent(activityAccountInfo.account_id),
    invite_user: encodeURIComponent(invite_user),
    invite_type: 1,
    [friendInvitationKey]: 1,
  };
  if (scene_id) {
    shareParams.scene_id = scene_id;
  }
  // 随机选择一条邀请文案
  const list = setting.value.inviteCard.copyTemplates || [];
  const randomIndex = Math.floor(Math.random() * list.length);
  const current = list[randomIndex];
  // 微信小程序环境
  if (isUserAgentType === 'WX_MINI') {
    const shareContent: Partial<CustomShareContent> = {
      pathQuery: shareParams,
    }
    if (current?.text) {
      shareContent.title = current?.text;
    }
    if (current?.poster) {
      shareContent.imageUrl = current?.poster;
    }
    activityPageStore.setShareContent(shareContent);
  }
  // 设置微信小程序内嵌分享参数
  if (isUserAgentType === 'WX_WEBVIEW') {
    const shareContent: ShareMessageConfigType = {};
    if (current?.text) {
      shareContent.title = current?.text;
    }
    if (current?.poster) {
      shareContent.imgUrl = current?.poster;
    }
    setWeappShareMessage(shareParams, shareContent);
  }
  if (uploadLog) {
    uploadLogInvite();
  }
}

watch(
  () => activityStore.activityAccountInfo.invite_code,
  (newVal) => {
    // 微信小程序环境 or 微信小程序内嵌环境
    if ((isUserAgentType === 'WX_MINI' || isUserAgentType === 'WX_WEBVIEW') && newVal) {
      // 这里延迟各 300 毫毛，场景是：邀请好友模块 跟 组队模块 共存时，需要确保 邀请组队 的参数优先级>邀请好友
      // 邀请好友无需延迟 0 或者 不延迟
      sendShareMessage();
    }
  },
  {
    immediate: true,
  },
);

const handleClaim = async () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return
  }
  
  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }

  if (setting.value.notAccomplishTip) {
    showToast(setting.value.notAccomplishTip);
  }

  // 微信小程序内嵌环境：弹出右上角分享引导
  if (isUserAgentType === 'WX_WEBVIEW') {
    sendShareMessage(false);
    toggleWeappShareGuide(true);
    return;
  }

  if (setting.value.notAccomplishUrl) {
    navigateScroll(setting.value.notAccomplishUrl);
    return;
  }

  toggleLossFriends(true);
  uploadLogOpenLossFriendsModal()
  getLossFriendsList();
};

const handleWeappShare = () => {
  uploadLogInvite();

  // 设置微信页面分享参数
  sendShareMessage();
};

const getLossFriendsList = async () => {
  const res = await postLossFriendsList({
    act_id: activityStore.activityInfo.init?.id,
    act_acc_id: activityStore.activityAccountInfo.act_acc_id,
  });
  if (res.code === 0) {
    friendsList.value = res.data?.list
  }
};

</script>

<style>
.task-item {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.task-item-wx-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: transparent;
}

.task-item-wx-btn::after {
  border: none;
}
</style>
