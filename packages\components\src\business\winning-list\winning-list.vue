<template>
  <Records
    v-if="showWinners"
    v-model:setting="setting"
    :open="open"
    :columns="mergedColumns"
    :dataSource="dataSource"
    @open="() => handleShow()"
    @close="() => toggle(false)"
  >
    <!-- 入口文案 -->
    <UiText
      v-if="setting.text?.content"
      v-model:setting="setting.text"
    />

    <template #bodyCell="{ column, record }">
      <!-- 奖项 -->
      <template v-if="column.dataIndex === 'grade'">
        {{ getGradeText(record.grade) }}
      </template>
      <!-- 奖励名称 -->
      <template v-if="column.dataIndex === 'name'">
        {{ record.name }}
      </template>
      <!-- 玩家昵称 -->
      <template v-if="column.dataIndex === 'role_name'">
        {{ record.role_name }}
      </template>
      <!-- 中奖时间 -->
      <template v-if="column.dataIndex === 'open_at'">
        {{ record.open_at }}
      </template>
    </template>
  </Records>
</template>

<script lang="ts">
export default {
  name: 'winning-list',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import type { StatefulComponent } from '@bish/types/src/admin';
import { postLotteryPrizeWinners } from '@bish/api/src/lottery';
import type { LotteryPrizeWinnersData } from '@bish/api/src/lottery';
import Records from '../records/records.vue';
import UiText from '../../ui/text/index.vue';
import { defaultConfig } from './index';
import type { WinningListSetting } from './winning-list';

export interface WinningListProps {
  setting: WinningListSetting
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<WinningListProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: WinningListSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const winners = ref<LotteryPrizeWinnersData>([]);

const activityStore = useActivityStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);
const [open, toggle] = useControllableStatus(props, emits, { fieldName: 'showWinningList' });

const dataSource = computed(() => {
  if (adminStore.editable) {
    return [
      {
        role_name: '玩家昵称',
        grade: 1,
        name: '100钻石',
        open_at: '2024.09.09 16:01:33',
      },
      {
        role_name: '玩家昵称',
        grade: 2,
        name: '幸运礼包',
        open_at: '2024.09.09 16:01:33',
      },
      {
        role_name: '玩家昵称',
        grade: 3,
        name: '神秘道具',
        open_at: '2024.09.09 16:01:33',
      },
      {
        role_name: '玩家昵称',
        grade: 0,
        name: '神秘道具',
        open_at: '2024.09.09 16:01:33',
      },
    ];
  }
  return winners.value;
});

const showWinners = computed(() => {
  return adminStore.editable || winners.value.length > 0;
});

const mergedColumns = computed(() => {
  return props.setting.columns.map(column => {
    return {
      ...column,
      width: column.dataIndex === 'created_at' ? undefined : column.width,
    };
  });
});

watch(
  () => activityStore.activityInfo,
  () => {
    fetchWinningList();
  },
);

const handleShow = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }
  toggle(true);
};

const fetchWinningList = async () => {
  try {
    const res = await postLotteryPrizeWinners({
      act_id: activityStore.activityInfo?.init?.id,
      component_config_id: setting.value.lotteryId,
    });
    if (res.code === 0) {
      winners.value = res.data;
    }
  } catch (error) {
    console.warn('获取中奖名单失败', error);
  }
};

const gradeMap: Record<number, string> = {
  1: '一等奖',
  2: '二等奖',
  3: '三等奖',
};

const getGradeText = (grade: number) => {
  return gradeMap[grade] || '常规';
};
</script>

<style>
.winning-list {}
</style> 