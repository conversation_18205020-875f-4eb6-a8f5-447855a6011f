<template>
  <div class="picker-toolbar">
    <div
      class="picker-toolbar-cancel"
      @click="handleCancel"
    >
      取消
    </div>
    <div class="picker-toolbar-title">
      <slot></slot>
    </div>
    <div
      class="picker-toolbar-confirm"
      @click="handleConfirm"
    >
      确认
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineEmits } from 'vue';

const emits = defineEmits<{
  (event: 'cancel'): void;
  (event: 'confirm'): void;
}>();

const handleCancel = () => {
  emits('cancel');
};

const handleConfirm = () => {
  emits('confirm');
};
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
  name: 'common-picker-tollbar',
}
</script>

<style>
.picker-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 46PX;
}

.picker-toolbar-cancel,
.picker-toolbar-confirm {
  padding: 0 16PX;
  font-size: 14PX;
  background-color: transparent;
  border: none;
}

.picker-toolbar-cancel {
  color: #969799;
}

.picker-toolbar-confirm {
  color: #1989fa;
}

.picker-toolbar-title {
  flex: 1;
  font-size: 16PX;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>