<!--
* @Description: 集卡-刮刮乐
-->
<template>
  <div
    class="collect-cards-scratch"
    @touchmove.prevent
  >
    <Popup
      z-index="99"
      :show="show"
      :lock-scroll="false"
      v-model:setting="setting"
      :stop-propagation="false"
      @close="handleClose"
      @closed="handleClosed"
    >
      <ResizableProvider>
        <!-- 标题 -->
        <UiImage
          v-if="setting.title.enabled"
          v-model:setting="setting.title"
          :confined="false"
        />
        <!-- 卡片容器 -->
        <div
          :class="{
            'collect-cards-scratch-box': true,
            'collect-cards-scratch-box__flipped': flipped,
          }"
        >
          <!-- 正面 -->
          <div class="collect-cards-scratch-box-front" :style="boxStyle">
            <ResizableProvider>
              <!-- 卡片盒子 -->
              <UiImage
                v-if="setting.box.enabled"
                v-model:setting="setting.box"
                :confined="false"
              />
              <!-- 卡片 -->
              <Resizable
                v-model:x="setting.card.x"
                v-model:y="setting.card.y"
                v-model:width="setting.card.width"
                v-model:height="setting.card.height"
                class="collect-cards-scratch-card"
                :class="{ 'collect-cards-scratch-card__empty': !data.icon }"
                :style="{
                  backgroundImage: `url(${data.icon})`,
                }"
              />
              <!-- 翻卡刮奖 -->
              <UiImage
                v-if="setting.scratch.enabled"
                v-model:setting="setting.scratch"
                :confined="false"
                @click="handleFlip"
              />
            </ResizableProvider>
          </div>
          <!-- 背面 -->
          <div class="collect-cards-scratch-box-back" :style="boxStyle">
            <ResizableProvider>
              <!-- 卡片盒子 -->
              <UiImage
                v-if="setting.box.enabled"
                v-model:setting="setting.box"
                :confined="false"
              />
              <!-- 恭喜获得-背景 -->
              <Resizable
                v-model:x="setting.congrats.x"
                v-model:y="setting.congrats.y"
                v-model:width="setting.congrats.width"
                v-model:height="setting.congrats.height"
                class="collect-cards-scratch-card"
                :style="{
                  backgroundImage: `url(${setting.congrats.bgImage})`,
                }"
              >
                <CongratulationsContent
                  v-model:setting="setting.congrats"
                  :prize="drawData!"
                />
              </Resizable>
              <!-- 恭喜获得蒙层 -->
              <UiImage
                v-if="setting.congratsMask?.enabled && showCongratsMask"
                v-model:setting="setting.congratsMask"
                :confined="false"
                :class="{
                  'collect-cards-scratch-canvas__out': isFading,
                  'collect-cards-scratch-ticket__out': !showTicket,
                }"
              />
              <!-- 刮刮乐 -->
              <div
                v-if="!adminStore.editable"
                class="collect-cards-scratch-ticket"
                :class="{ 'collect-cards-scratch-ticket__out': !showTicket }"
                :style="ticketStyle"
              >
                <canvas
                  ref="canvasRef" 
                  class="collect-cards-scratch-canvas"
                  :class="{ 'collect-cards-scratch-canvas__out': isFading }"
                />
              </div>
            </ResizableProvider>
          </div>
        </div>
  
        <!-- 卡片名称 -->
        <UiText
          v-if="setting.name.enabled"
          v-model:setting="setting.name"
          :confined="false"
        >
          {{ data.name || '卡片名称' }}
        </UiText>
      </ResizableProvider>
    </Popup>
  </div>
</template>

<script lang="ts">
export default {
  name: 'collect-cards-scratch',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, ref, watch, nextTick, onUnmounted } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import { pxTransform } from '@bish/utils/src/viewport';
import { sleep } from '@bish/utils/src/utils';
import { useBoxLottery } from '@bish/hooks/src/business/useBoxLottery';
import useAdminStore from '@bish/store/src/modules/admin';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import { showToast } from '@bish/ui/src/toast';
import { useLog } from '@bish/hooks/src/useLog';
import Popup from '../../../../common/popup.vue';
import type { CollectCardsScratchSetting } from './collect-cards-scratch';
import Resizable from '../../../../ui/resizable/index.vue';
import ResizableProvider from '../../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../../ui/image/index.vue';
import UiText from '../../../../ui/text/index.vue';
import CongratulationsContent from '../../../../common/congratulations-modal/congratulations-content/congratulations-content.vue';

export interface CollectCardsScratchData {
  /**
   * 卡皮名称
   */
  name: string
  /**
   * 卡片图片地址
   */
  icon: string
  /**
   * 用户集卡记录id
   */
  card_log_id: number
}

export interface CollectCardsScratchProps {
  /**
   * 配置
   */
  setting: CollectCardsScratchSetting
  /**
   * 是否显示
   */
  show: boolean
  /**
   * 是否自动翻转
   */
  autoFlip?: boolean
  /**
   * 抽奖id
   */
  lotteryId: number
  /**
   * 是否需要绑定角色
   */
  needBindRole: number
  /**
   * 卡片数据
   */
  data: CollectCardsScratchData
}

const props = withDefaults(defineProps<CollectCardsScratchProps>(), {
  show: false,
  autoFlip: false,
  lotteryId: 0,
  needBindRole: 0,
  data: () => ({
    name: '',
    icon: '',
    card_log_id: 0,
  }),
});

const emits = defineEmits<{
  (event: 'update:setting', value: CollectCardsScratchSetting): void;
  (event: 'close'): void;
  (event: 'scratched'): void;
}>();

const flipped = ref(false);

const setting = useVModel(props, 'setting', emits);

const adminStore = useAdminStore();
const userStore = useUserStore();
const activityStore = useActivityStore();

const { uploadLog } = useLog();

const ticketStyle = computed<CSSProperties>(() => {
  return {
    top: pxTransform(setting.value.box.y!),
    left: pxTransform(setting.value.box.x!),
    width: pxTransform(setting.value.box.width),
    height: pxTransform(setting.value.box.height),
  };
});

const canvasRef = ref<HTMLCanvasElement>();
const ctx = ref<CanvasRenderingContext2D>();
let isDrawing = false;
let lastPoint = { x: 0, y: 0 };

// 定义刮开阈值常量（百分比）
const SCRATCH_THRESHOLD = 2;

const isFading = ref(false);
const showTicket = ref(true);
const hasTriggeredDraw = ref(false);

const {
  fetchDraw,
  fetchDrawEnd,
  drawData,
  lotteryConfig,
  lotterySchedule,
  process,
  resetDrawData,
} = useBoxLottery(props.lotteryId, props.needBindRole);

const boxStyle = computed<CSSProperties>(() => {
  return {
    height: pxTransform(setting.value.box.height),
  };
});

const showCongratsMask = computed(() => {
  return !adminStore.editable;
});

const scratchBg = computed(() => {
  return setting.value.scratchBg || 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250418/1744955837568-stratch-bg.png';
});

// 添加一个引用来存储定时器ID
let fadeOutTimer: number | null = null;

const initCanvas = () => {
  if (adminStore.editable) return false;
  if (!canvasRef.value) return false;
  
  // 先移除可能存在的事件监听器
  removeEventListeners();
  
  const canvas = canvasRef.value;
  const rect = canvas.getBoundingClientRect();
  
  // 适配高清屏
  canvas.width = rect.width * devicePixelRatio;
  canvas.height = rect.height * devicePixelRatio;
  canvas.style.width = `${rect.width}px`;
  canvas.style.height = `${rect.height}px`;
  
  ctx.value = canvas.getContext('2d')!;
  ctx.value.scale(devicePixelRatio, devicePixelRatio);
  
  // 使用背景图替代纯色背景
  const img = new Image();
  img.onload = () => {
    // 绘制背景图
    ctx.value!.drawImage(img, 0, 0, rect.width, rect.height);
    
    // 绘制文字
    ctx.value!.fillStyle = '#FFFFFF';
    ctx.value!.font = `${rect.height * 0.05}px Arial`;
    ctx.value!.textAlign = 'center';
    ctx.value!.textBaseline = 'middle';
    ctx.value!.fillText('刮开查看奖励', rect.width / 2, rect.height / 2);
    
    // 设置刮擦模式
    ctx.value!.globalCompositeOperation = 'destination-out';
  };
  img.crossOrigin = '';
  img.src = scratchBg.value;
  
  // 添加事件监听器
  canvas.addEventListener('mousedown', startDrawing, { passive: false });
  canvas.addEventListener('mousemove', draw, { passive: false });
  canvas.addEventListener('mouseup', endDrawing);
  canvas.addEventListener('touchstart', startDrawing, { passive: false });
  canvas.addEventListener('touchmove', draw, { passive: false });
  canvas.addEventListener('touchend', endDrawing);

  return true;
};

const startDrawing = (e: MouseEvent | TouchEvent) => {
  isDrawing = true;
  const { clientX, clientY } = e instanceof TouchEvent ? e.touches[0] : e;
  const rect = canvasRef.value!.getBoundingClientRect();
  lastPoint = {
    x: clientX - rect.left,
    y: clientY - rect.top
  };
};

const draw = (e: MouseEvent | TouchEvent) => {
  if (!lotterySchedule.value) {
    showToast('抽奖信息异常，请稍后再试~');
    return;
  };

  if (!isDrawing || !ctx.value) return;
  
  const { clientX, clientY } = e instanceof TouchEvent ? e.touches[0] : e;
  const rect = canvasRef.value!.getBoundingClientRect();
  const currentPoint = {
    x: clientX - rect.left,
    y: clientY - rect.top
  };

  ctx.value.beginPath();
  ctx.value.moveTo(lastPoint.x, lastPoint.y);
  ctx.value.lineTo(currentPoint.x, currentPoint.y);
  ctx.value.strokeStyle = 'rgba(0,0,0,1)';
  ctx.value.lineWidth = 20;
  ctx.value.lineCap = 'round';
  ctx.value.stroke();

  lastPoint = currentPoint;

  // 检查刮开面积
  checkScratchPercentage();
};

// 检查刮开面积的百分比
const checkScratchPercentage = async () => {
  if (!ctx.value || !canvasRef.value) return;
  
  if (hasTriggeredDraw.value) return;
  
  const imageData = ctx.value.getImageData(
    0,
    0,
    canvasRef.value.width,
    canvasRef.value.height,
  );
  const pixels = imageData.data;
  let transparentPixels = 0;
  
  // 每4个值表示一个像素点(r,g,b,a)
  for (let i = 3; i < pixels.length; i += 4) {
    // alpha 值为0表示透明
    if (pixels[i] === 0) {
      transparentPixels++;
    }
  }
  
  // 计算透明部分占总像素的百分比
  const totalPixels = (canvasRef.value.width * canvasRef.value.height);
  const percentage = (transparentPixels / totalPixels) * 100;
  
  // 如果超过阈值，触发刮开事件
  if (percentage > SCRATCH_THRESHOLD && !hasTriggeredDraw.value) {
    hasTriggeredDraw.value = true;
    
    try {
      const res = await fetchDraw({
        ...lotterySchedule.value!,
        card_log_id: props.data.card_log_id,
      });
      if (!res) {
        // 移除事件监听
        removeEventListeners();
        handleClose();
        return;
      }
    } catch (error) {
      console.warn('抽奖失败，请稍后再试~', error);
      hasTriggeredDraw.value = false;
    }
  }
};

// 移除事件监听
const removeEventListeners = () => {
  if (!canvasRef.value) return;
  
  const canvas = canvasRef.value;
  
  // 确保移除所有事件监听器
  canvas.removeEventListener('mousedown', startDrawing, { capture: true });
  canvas.removeEventListener('mousemove', draw, { capture: true });
  canvas.removeEventListener('mouseup', endDrawing, { capture: true });
  canvas.removeEventListener('touchstart', startDrawing, { capture: true });
  canvas.removeEventListener('touchmove', draw, { capture: true });
  canvas.removeEventListener('touchend', endDrawing, { capture: true });
  
  // 重置绘图状态
  isDrawing = false;
};

const endDrawing = () => {
  if (!hasTriggeredDraw.value) return;

  isDrawing = false;
  // 触发刮开事件
  emits('scratched');
  // 开始淡出动画
  isFading.value = true;
  // 移除事件监听
  removeEventListeners();

  // 清除可能存在的旧定时器
  if (fadeOutTimer !== null) {
    clearTimeout(fadeOutTimer);
  }

  // 等待淡出动画结束
  fadeOutTimer = window.setTimeout(() => {  
    showTicket.value = false;
    fadeOutTimer = null;
  }, 1000);

  fetchDrawEnd();
};

watch(
  () => props.show,
  async (newVal) => {
    if (newVal) {
      // 等待下一帧并初始化 canvas
      await nextTick();
      const initialized = initCanvas();
      
      // 只有在 canvas 初始化成功且需要自动翻转时才执行
      if (initialized && props.autoFlip && userStore.userData.token) {
        await sleep(200);
        handleFlip();
      }
    }
  },
);

const handleClose = () => {
  emits('close');
};

const handleFlip = () => {
  // 数据上报：点击翻卡
  uploadLog({
    event_name: 'click',
    click_id: 144,
    click_type: 3,
  });
  // 登录态拦截
  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }
  flipped.value = true;
};

/**
 * 重置刮刮乐状态 
 */
const resetState = () => {
  flipped.value = false;
  showTicket.value = true;
  isFading.value = false;
  hasTriggeredDraw.value = false;
  resetDrawData();
  
  // 清除可能存在的定时器
  if (fadeOutTimer !== null) {
    clearTimeout(fadeOutTimer);
    fadeOutTimer = null;
  }
  
  // 确保移除事件监听器
  removeEventListeners();
};

const handleClosed = () => {
  resetState();
  removeEventListeners(); // 确保在关闭时移除事件监听器
};

// 在组件卸载时清理资源
onUnmounted(() => {
  // 清除定时器
  if (fadeOutTimer !== null) {
    clearTimeout(fadeOutTimer);
    fadeOutTimer = null;
  }
  
  // 移除事件监听器
  removeEventListeners();
});
</script>

<style>
.collect-cards-scratch {}

.collect-cards-scratch-card {
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.collect-cards-scratch-card__empty {
  background-color: #f4f4f4;
}

.collect-cards-scratch-ticket {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.collect-cards-scratch-ticket__out {
  display: none;
}

.collect-cards-scratch-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  touch-action: none; /* 禁用触摸滚动 */
  opacity: 1;
  transition: opacity 1s ease-out;
}

.collect-cards-scratch-canvas__out {
  opacity: 0;
}

.collect-cards-scratch-box {
  transform-style: preserve-3d;
  transition: transform 0.6s;
}

.collect-cards-scratch-box__flipped {
  transform: rotateY(180deg);
}

.collect-cards-scratch-box-front,
.collect-cards-scratch-box-back {
  backface-visibility: hidden;
}

.collect-cards-scratch-box-back {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  transform: rotateY(180deg);
}

.collect-cards-scratch-congrats {
  width: 100%;
  height: 100%;
  background-color: salmon;
}
</style>