import { ref, watchEffect } from 'vue'
import { getWxShare } from '@bish/api/src/team-gift/index'
import useActivityStore from '@bish/store/src/modules/activity'
import useActivityPageStore from '@bish/store/src/modules/activityPage'
import { additionalLinkParameters } from '@bish/utils/src/utils'

const sourceModMap: Record<string, string> = {
  107: 'cahx_web',
  11: 'sszg_web',
  110: 'pxxl_web',
  112: 'yyjl_web',
}

export function useWxShare() {
  const wx = (window as any)?.wx
  const activityStore = useActivityStore()
  const activityPageStore = useActivityPageStore()
  const shareUrl = ref('')

  const getWechatShareSign = async (title: string, desc: string, imgUrl: string, shareLink?: string) => {
    const params = {
      url: window.location.href.split('#')[0],
      source_mod: sourceModMap[activityPageStore.activityPageConfig?.project_id] || 'cahx_web',
    }
    const { code, data } = await getWxShare(params)
    if (!code) {
      wx?.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: data.appId, // 必填，公众号的唯一标识
        timestamp: data.timestamp, // 必填，生成签名的时间戳
        nonceStr: data.nonceStr, // 必填，生成签名的随机串
        signature: data.signature, // 必填，签名
        jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData', 'hideMenuItems'], // 必填，需要使用的JS接口列表
      })
      handleShareData(title, desc, imgUrl, shareLink)
    }
  }

  /**
   *
   * @param title 分享标题
   * @param desc 分享描述
   * @param imgUrl 分享封面
   * @param shareLink 分享链接 注意：可选参数，不传入时默认使用shareUrl.value
   */
  const handleShareData = (title: string, desc: string, imgUrl: string, shareLink?: string) => {
    wx.ready(() => {
      const shareData = {
        title: title,
        desc: desc,
        link: shareLink ? shareLink : shareUrl.value, // 该链接域名或路径必须与当前页面对应的公众号 JS 安全域名一致
        imgUrl: imgUrl,
        success: function (success: any) {
          // 分享成功可以做相应的数据处理
          console.log('设置微信分享参数成功', success)
        },
        fail: function (fail: any) {
          console.log('设置微信分享参数失败', fail)
        },
      }
      wx.updateTimelineShareData(shareData)
      wx.updateAppMessageShareData(shareData)
      wx.hideMenuItems({
        menuList: ['menuItem:copyUrl'],
      })
    })
    wx.error(function (res: any) {
      // config信息验证失败会执行error函数，如签名过期导致验证失败，
      // 具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，
      // 对于SPA可以在这里更新签名。
      // alert('分享失败', res)
      console.log(res)
      //alert('分享失败')
    })
  }

  // watchEffect(() => {
  //   const activityAccountInfo = activityStore.activityAccountInfo
  //   const componentWithUserInfo = activityStore.componentWithUserInfo
  //   const params: Record<string, any> = {}
  //   if (activityAccountInfo) {
  //     const invite_user = encodeURIComponent(`${activityAccountInfo.role_info?.server_name}-${activityAccountInfo.role_info?.role_name}`)
  //     params.invite_code = activityAccountInfo.invite_code || ''
  //     params.recall_account_id = encodeURIComponent(activityAccountInfo.account_id).includes('undefined')
  //       ? ''
  //       : encodeURIComponent(activityAccountInfo.account_id)
  //     params.invite_user = invite_user.includes('undefined') ? '' : invite_user
  //     params.invite_type = 1
  //   }
  //   if (componentWithUserInfo) {
  //     // 这里直接取第一个队伍的id，是有问题的，如果存在多个组队配置就 g 了
  //     const teamId = componentWithUserInfo.component_with_user_info?.team[0]?.team_id
  //     if (teamId) {
  //       params.team_id = teamId
  //     }
  //   }
  //   shareUrl.value = additionalLinkParameters(params)
  // })

  return {
    getWechatShareSign,
  }
}
