import { defaultConfig as downloadGameDefaultConfig } from '../../../business/download-game.vue';
import type { GachaSetting } from './gacha';

export * from './gacha';

export const defaultConfig = (): GachaSetting => {
  const downloadGameConfig = downloadGameDefaultConfig();
  return {
    x: 0,
    y: 0,
    width: 346,
    height: 297,
    items: {
      x: 0,
      y: 0,
      width: 346,
      height: 225,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241106/1730885137760-%E6%B5%B7%E6%8A%A5.png',
    },
    bookBtn: {
      x: 78,
      y: 242,
      width: 191,
      height: 55,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241106/1730885141753-BUTTON%E7%AB%8B%E5%8D%B3%E9%A2%84%E7%BA%A6.png',
    },
    gachaBtn: {
      x: 78,
      y: 242,
      width: 191,
      height: 55,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241106/1730885143793-BUTTON%E7%AB%8B%E5%8D%B3%E6%8A%BD%E5%8D%A1.png',
    },
    gachaModal: {
      x: 0,
      y: 0,
      width: 375,
      height: 364,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730944699167-%E7%BB%84%2033.png',
      closeBtn: {
        x: 327,
        y: 5,
        width: 27,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730944693773-%E5%85%B3%E9%97%AD%E6%8C%89%E9%92%AE%20%E6%8B%B7%E8%B4%9D%203.png',
      },
      tip: {
        x: 51,
        y: 84,
        width: 281,
        height: 61,
        fontSize: 15,
        color: '#674B7C',
        align: 'center',
        alignItems: 'center',
        // content: '请前往《闪烁之光》游戏内</br>在登录页面点击右侧【预约】完成预抽卡',
        content: '',
        fontWeight: true,
      },
      poster: {
        x: 65,
        y: 153,
        width: 246,
        height: 119,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241119/1732019067289-%E5%BC%95%E5%AF%BC%E6%B5%B7%E6%8A%A5.jpg',
      },
      visitGame: {
        x: 49,
        y: 285,
        width: 137,
        height: 46,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730944706364-BUTTON%E5%89%8D%E5%BE%80%E6%B8%B8%E6%88%8F.png',
        tip: '请前往《闪烁之光》登录参与预约抽卡~',
      },
      downloadGame: {
        ...downloadGameConfig,
        x: 190,
        y: 285,
        width: 137,
        height: 46,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241107/1730945404384-BUTTON%E4%B8%8B%E8%BD%BD%E6%B8%B8%E6%88%8F.png',
      },
    },
    wxSubscribeTemp: [],
    wxSubscribeGuide: {
      x: 0,
      y: 0,
      width: 375, // 这里的宽度没啥用，在用户端会被覆盖成 100vw
      height: 204, // 这里的高度没啥用，在用户端会被覆盖成 100vh
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732069833551-bg.png',
      closeBtn: {
        x: 327,
        y: 0,
        width: 27,
        height: 27,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732069799956-close.png',
      },
      okBtn: {
        x: 119,
        y: 127,
        width: 137,
        height: 46,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241120/1732069830798-btn.png',
      },
    },
  }
};