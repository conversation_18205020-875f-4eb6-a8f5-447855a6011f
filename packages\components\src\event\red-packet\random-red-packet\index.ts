import type { RandomRedPacketSetting } from './random-red-packet';

export * from './random-red-packet';

export const defaultConfig = (): RandomRedPacketSetting => {
  return {
    x: 0,
    y: 0,
    width: 375,
    height: 289,
    taskId: 0,
    bindRole: 1,
    bgImage: {
      x: 12,
      y: 7,
      width: 353,
      height: 251,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250507/1746601386072-red-packet-bg.png',
      enabled: true,
    },
    redPacket: {
      x: 78,
      y: 10,
      width: 222,
      height: 250,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250507/1746618270090-red-packet.png',
    },
    openBtn: {
      x: 141,
      y: 47,
      width: 94,
      height: 86,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250507/1746618275243-red-packet-btn.png',
    },
    redPacketOpened: {
      x: 38,
      y: 0,
      width: 294,
      height: 289,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250507/1746601391831-red-packet-opened.png',
    },
    amount: {
      x: 0,
      y: 35,
      width: 375,
      height: 88,
      fontSize: 58,
      color: '#FD3B07',
      alignItems: 'center',
      align: 'center',
      fontWeight: true,
    },
    hitBtn: {
      x: 147,
      y: 129,
      width: 83,
      height: 83,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250512/1747033324225-crit-btn.png',
      enabled: true,
    },
    taskIdHit: 0,
    hitTips: '充值一波有机会额外获得3-188红包暴击机会',
  }
};