import usePopupStore from '@bish/store/src/modules/popup'
import { useUserAgent, UserAgent } from '../useUserAgent'
import { useEnv } from '../useEnv'
import { parseQueryString, additionalLinkEncodeParameters } from '@bish/utils/src/utils'
import localStorage from '@bish/utils/src/storage/localStorage'

export function useAccountBindWx() {
  const queryParams = parseQueryString(window.location.href)
  const popupStore = usePopupStore()
  const { isUserAgentType } = useUserAgent()
  const { VITE_WECHAT_URL } = useEnv()

  /**
   * 引导用户绑定微信
   */
  function handleBindGuide() {
    const wxParams = localStorage.getLocalStorage('wx_params')
    if (isUserAgentType === UserAgent.WECHATWEB) {
      if (wxParams && wxParams.unionid && wxParams.openid) {
        popupStore.setShowAccountBindWx(true)
      } else {
        const redirectLink = getRedirectWxAuthUrl()
        window.location.href = redirectLink
      }
    } else {
      popupStore.setShowWxEmpowerGuide(true)
    }
  }

  /**
   * 获取微信授权地址
   */
  function getRedirectWxAuthUrl() {
    const redirectUrl = additionalLinkEncodeParameters({ ...queryParams }, window.location.href.split('?')[0])
    // 通过Base64编码得到redirect_info，注意：redirect_url的参数统一使用&拼接，不能有?
    const redirectInfo = btoa(`redirect_url=${redirectUrl.replace(/\?/g, '&')}`)
    // 生成重定向地址
    const redirectLink = `${VITE_WECHAT_URL}/wechat/webOauth?state=SY_NORMAL&request_mode=Normal&redirect_info=${redirectInfo}`
    return redirectLink
  }

  return {
    handleBindGuide,
    getRedirectWxAuthUrl,
  }
}
