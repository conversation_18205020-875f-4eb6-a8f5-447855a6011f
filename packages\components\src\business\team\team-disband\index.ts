import type { TeamDisbandSetting } from './team-disband';

export * from './team-disband';

export const defaultConfig = (): TeamDisbandSetting => {
  return {
    x: 0,
    y: 0,
    width: 65,
    height: 267,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736928363379-disband-btn.png',
    teamId: 0,
    affix: 1,
    modal: {
      x: 0,
      y: 0,
      width: 360,
      height: 260,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736929485887-disband-dialog-bg.png',
      closeBtn: {
        x: 310,
        y: 8,
        width: 32,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250114/1736863567849-close.png',
      },
      okBtn: {
        x: 132,
        y: 161,
        width: 98,
        height: 32,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736929494049-disband-dialog-btn.png',
      },
      content: {
        x: 70,
        y: 91,
        width: 220,
        height: 64,
        fontSize: 12,
        color: '#363636',
        align: 'center',
        alignItems: 'center',
        content: '<div style="line-height: 1.8;">已创建</br><span style="color: #EC5B23;">{{teamName}}</span> 的队伍</div>',
        enabled: true,
      },
    },
    confirmModal: {
      x: 0,
      y: 0,
      width: 360,
      height: 260,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736928357097-bg.png',
      closeBtn: {
        x: 310,
        y: 8,
        width: 32,
        height: 30,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250114/1736863567849-close.png',
      },
      okBtn: {
        x: 190,
        y: 173,
        width: 97,
        height: 32,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736922160241-accept.png',
      },
      cancelBtn: {
        x: 73,
        y: 173,
        width: 97,
        height: 32,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250115/1736922165415-reject.png',
        enabled: true,
      },
      content: {
        x: 70,
        y: 91,
        width: 220,
        height: 64,
        fontSize: 12,
        color: '#363636',
        align: 'center',
        alignItems: 'center',
        content: '<div style="line-height: 1.8;">申请解散</br><span style="color: #EC5B23;">{{teamName}}</span> 的队伍</div>',
        enabled: true,
      },
    },
  };
};