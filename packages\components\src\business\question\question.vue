<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    @click="handleOpen"
  >
    <!-- 入口 -->
    <div
      class="question"
      :style="entryStyle"
    />

    <!-- 弹窗 -->
    <Popup
      z-index="99"
      :show="open"
      :lock-scroll="false"
      v-model:setting="setting.modal"
      @close="handleClose"
    >
      <!-- 标题 -->
      <ResizableProvider>
        <Resizable
          class="question-content"
          v-model:x="setting.modal.content.x"
          v-model:y="setting.modal.content.y"
          v-model:width="setting.modal.content.width"
          v-model:height="setting.modal.content.height"
        >
          <div v-html="setting.modal.instruction" />
        </Resizable>
      </ResizableProvider>
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'question',
}
</script>

<script lang="ts" setup>
import { computed } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import useAdminStore from '@bish/store/src/modules/admin';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { pxTransform } from '@bish/utils/src/viewport';
import Resizable from '../../ui/resizable/index.vue';
import ResizableProvider from '../../ui/resizable/resizable-provider.vue';
import Popup from '../../common/popup.vue';
import type { QuestionSetting } from './question';
import { defaultConfig } from './index';

export interface QuestionProps {
  setting: QuestionSetting
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<QuestionProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: QuestionSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const setting = useVModel(props, 'setting', emits);
const [open, toggle] = useControllableStatus(props, emits, { fieldName: 'showQuestion' });

const adminStore = useAdminStore();

const entryStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.x!),
    top: pxTransform(setting.value.y!),
    width: pxTransform(setting.value.width),
    height: pxTransform(setting.value.height),
    backgroundImage: `url(${setting.value.bgImage})`,
  };
});

const handleOpen = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }
  toggle(true);
};

const handleClose = () => {
  toggle(false);
};
</script>

<style>
.question {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.question-content {
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  text-align: justify;
  word-break: break-all;
}
</style> 