import { commonTaskConfig } from '../index';
import type { TaskRedPacketSetting } from './red-packet';

export * from './red-packet';

export const defaultConfig = (): TaskRedPacketSetting => {
  return {
    ...commonTaskConfig(),
    process: {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      fontSize: 0,
      color: '#684975',
      content: '{{process}}',
    },
    width: 375,
    height: 224,
    taskId: 0,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241202/1733124456027-1-bg.png',
    activeBgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241202/1733124458894-1-bg-e.png',
    goBtn: {
      width: 0,
      height: 0,
    },
    claimBtn: {
      x: 118,
      y: 156,
      width: 140,
      height: 35,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241202/1733124604373-btn.png',
    },
    achievedBtn: {
      x: 118,
      y: 156,
      width: 140,
      height: 35,
      imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20241202/1733124606574-btn-e.png',
    },
    receivedSuccessTip: '领取成功，请前往红包查看',
    receivedRepeatTip: '提示前往钱包查询',
    notAccomplishTip: '任务未完成，请冒险者完成任务后再来领取哟',
  }
};