import type { CommonSetting } from '@bish/types/src'
import type { ImageSetting } from '../../../ui/image/index.vue'
import type { PopupSetting } from '../../../common/popup.vue'
import type { TextSetting } from '../../../ui/text/index.vue'

export interface TeamJoinSetting extends CommonSetting {
  /**
   * 队伍id
   */
  teamId: number
  /**
   * 是否固定在页面，1是 0否，默认 1，设置后将使用 fixed 布局
   */
  affix: number
  /**
   * 加入按钮
   */
  joinBtn: ImageSetting
  /**
   * 已加入按钮
   */
  joinedBtn: ImageSetting
  /**
   * 弹窗基础配置
   */
  modal: PopupSetting & {
    /**
     * 列表配置
     */
    list: CommonSetting
    /**
     * 列表项配置
     */
    item: {
      /**
       * 高度
       */
      height: number
      /**
       * 间距
       */
      gutter: number
      /**
       * 队伍名
       */
      name: TextSetting
      /**
       * 数量
       */
      count: TextSetting
    }
    /**
     * 创建队伍按钮
     */
    createBtn: ImageSetting
    /**
     * 加入队伍按钮
     */
    joinBtn: ImageSetting
    /**
     * 已满员按钮
     */
    fullBtn: ImageSetting
    /**
     * 已加入按钮
     */
    joinedBtn: ImageSetting
  }
  /**
   * 确认弹窗配置
   */
  confirmModal: PopupSetting & {
    /**
     * 取消按钮
     */
    cancelBtn: ImageSetting
    /**
     * 提示文案
     */
    content: TextSetting
  }
}