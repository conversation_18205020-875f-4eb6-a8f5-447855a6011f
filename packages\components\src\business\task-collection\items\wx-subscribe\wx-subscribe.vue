<!--
* @Description: 每日分享任务
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="task-item"
    :style="{
      backgroundImage: `url(${setting.bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 标题 -->
      <UiText v-if="setting.title?.content" v-model:setting="setting.title" />

      <!-- 描述 -->
      <UiText v-if="setting.description?.content" v-model:setting="setting.description" />
      
      <!-- 完成进度 -->
      <UiText
        v-if="setting.process"
        v-model:setting="setting.process"
        :interpolation="processInterpolation"
        :confined="false"
      />

      <!-- 去订阅 -->
      <UiImage
        v-if="!userTask || userTask?.status === 1"
        :setting="setting.claimBtn"
        @click="handleClaim"
        :confined="false"
      />
      <!-- 已完成 -->
      <UiImage
        v-else
        :setting="setting.achievedBtn"
        :confined="false"
      />
    </ResizableProvider>

    <!-- 微信小程序订阅提示弹窗 -->
    <WxSubscribeGuide
      v-if="setting.wxSubscribeGuide"
      :show="showOpenSubscribeMessage"
      v-model:setting="setting.wxSubscribeGuide"
      @close="handleCloseSubscribeMessage"
      @ok="handleOpenSetting"
    />

    <!-- 总是保持以上选择，不再询问 -->
    <WxSubscribeCheck v-model:show="showGuideSubscription" />
    
    <!-- 微信小程序订阅弹窗 -->
    <Popup
      z-index="99"
      :show="openWxSubscribe"
      v-model:setting="setting.wxSubscribe"
      @close="() => toggleWxSubscribe(false)"
      @ok="handleClaim"
    />
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'task-daily-wx-subscribe',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, computed, watch, onMounted } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useActivityStore from '@bish/store/src/modules/activity';
import type { StatefulComponent } from '@bish/types/src/admin';
import { sleep } from '@bish/utils/src/utils';
import useWxSubscribeMessage from '@bish/hooks/src/business/useWxSubscribeMessage';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import useRouteQuery from '@bish/hooks/src/useRouteQuery';
import { defaultConfig } from './index';
import type { TaskWxSubscribeSetting } from './wx-subscribe';
import Resizable from '../../../../ui/resizable/index.vue';
import ResizableProvider from '../../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../../ui/image/index.vue';
import UiText from '../../../../ui/text/index.vue';
import WxSubscribeGuide from '../../../wx-subscribe-guide/wx-subscribe-guide.vue';
import WxSubscribeCheck from '../../../../common/wx-subscribe-check/wx-subscribe-check.vue';
import Popup from '../../../../common/popup.vue';

export interface TaskCollectionProps {
  /**
   * 配置
   */
  setting: TaskWxSubscribeSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TaskCollectionProps>(), {
  setting: () => defaultConfig(),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskWxSubscribeSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityPageStore = useActivityPageStore();
const activityStore = useActivityStore();

const [openWxSubscribe, toggleWxSubscribe] = useControllableStatus(props, emits, { fieldName: 'showWxSubscribe' });

const setting = useVModel(props, 'setting', emits);
const { query } = useRouteQuery();

const preOperationWxSubscribe = query.value.pre_op === 'wx-subscribe';

const submitClock = async () => {
  await updateSubscriptionCount(
    {
      act_id: activityPageStore.activityPageConfig.activity_id!,
      act_acc_id: activityStore.activityAccountInfo.act_acc_id,
    },
    setting.value.wxSubscribeTemp,
  );
  toggleWxSubscribe(false);
  await sleep(2000);
  activityStore.getActivityAccountInfo();
};

const {
  showGuideSubscription,
  showOpenSubscribeMessage,
  setClock,
  guidSubscribeMessageAuthAfter,
  // subscribed,
  fetchRemindStatus,
  updateSubscriptionCount,
  resetSubscribeState,
  toggleOpenSubscribeMessage,
} = useWxSubscribeMessage(
  submitClock,
  [props, emits, { fieldName: 'showOpenSubscribeMessage' }],
);

const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

const userTask = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
});

const process = computed(() => {
  return `${userTask.value?.progress_num || 0}/${taskConfig.value?.target_num || 0}`;
});

const processInterpolation = computed(() => {
  return {
    process: process.value,
  };
});

watch(
  () => activityStore.activityAccountInfo,
  (newVal) => {
    if (newVal.act_acc_id) {
      fetchMiniRemindStatus();
    } else {
      resetSubscribeState();
    }
  },
);

/**
 * 初始展示弹窗
 */
 const initOpenModal = () => {
  if (preOperationWxSubscribe) {
    toggleWxSubscribe(true);
  }
};

onMounted(() => {
  initOpenModal();
});

const fetchMiniRemindStatus = async () => {
  const { activityAccountInfo } = activityStore;
  const templateList = setting.value.wxSubscribeTemp || [];
  const remind_template_id = templateList.map(item => item.remindTemplateId).join(',');
  fetchRemindStatus({
    act_id: activityPageStore.activityPageConfig.activity_id!,
    act_acc_id: activityAccountInfo.act_acc_id,
    remind_template_id,
  });
};

const handleClaim = async () => {
  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfig.value) {
    showToast('活动已结束~');
    return;
  }
  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  setClock(setting.value.wxSubscribeTemp);
  // 没有订阅次数，并且开启提醒，才调起微信订阅消息
  // 防止用户一直订阅消息
  // if (!subscribed) {
  // } else {
  //   showToast('已订阅，无需重复操作');
  // }
};

const handleCloseSubscribeMessage = () => {
  toggleOpenSubscribeMessage(false);
};

const handleOpenSetting = () => {
  handleCloseSubscribeMessage();
  uni.openSetting({
    success() {
      guidSubscribeMessageAuthAfter(setting.value.wxSubscribeTemp);
    },
  });
};
</script>

<style>
.task-item {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
