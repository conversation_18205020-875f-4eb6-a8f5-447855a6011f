<!--
* @Description: 每日登录游戏任务
-->
<template>
  <TaskItem v-model:setting="setting" @do="handleClaim">
    <!-- 去完成 -->
    <template #doBtn="{ show }">
      <template v-if="show">
        <UiImage
          v-if="setting.actionType !== 'download'"
          :setting="setting.doBtn"
          @click="handleClaim"
          :confined="false"
        />
        <!-- 下载游戏按钮 -->
        <DownloadGame
          v-if="setting.downloadGame"
          v-model:setting="setting.downloadGame"
          :exposureType="1"
        />
      </template>
    </template>

    <!-- 引导弹窗 -->
    <Popup
      v-if="setting.guideModal"
      z-index="99"
      :show="open"
      :lock-scroll="false"
      v-model:setting="setting.guideModal"
      @close="handleClose"
    >
      <!-- 提示文案 -->
      <UiText
        v-if="setting.guideModal.tip?.content"
        v-model:setting="setting.guideModal.tip"
        class="book-confirmed-content"
      />
      <!-- 海报展示 -->
      <UiImage v-if="setting.guideModal.poster?.imgLink" v-model:setting="setting.guideModal.poster" />

      <!-- 前往游戏按钮 -->
      <UiImage
        v-if="setting.guideModal.visitGame?.imgLink"
        v-model:setting="setting.guideModal.visitGame"
        @click="handleVisitGame"
      />
      <!-- 下载游戏按钮 -->
      <DownloadGame
        v-if="setting.guideModal.downloadGame?.imgLink"
        v-model:setting="setting.guideModal.downloadGame"
      />
    </Popup>
  </TaskItem>
</template>

<script lang="ts">
export default {
  name: 'task-login-game',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { useLog } from '@bish/hooks/src/useLog';
import { defaultConfig as TASK_LOGIN_GAME_CONFIG } from './index';
import type { TaskLoginGameSetting } from './login-game';
import TaskItem from '../item.vue';
import UiImage from '../../../../ui/image/index.vue';
import UiText from '../../../../ui/text/index.vue';
import Popup from '../../../../common/popup.vue';
import DownloadGame from '../../../../business/download-game.vue';

export interface TaskLoginGameProps {
  /**
   * 配置
   */
  setting: TaskLoginGameSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TaskLoginGameProps>(), {
  setting: () => TASK_LOGIN_GAME_CONFIG(),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskLoginGameSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityStore = useActivityStore();

const setting = useVModel(props, 'setting', emits);

const [open, toggle] = useControllableStatus(props, emits, { fieldName: 'showGuide' });

const { uploadLog } = useLog();

const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

// const userTask = computed(() => {
//   const { component_with_user_info } = activityStore.componentWithUserInfo;
//   return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
// });

// const completed = computed(() => {
//   return userTask.value ? userTask.value?.progress_num === userTask.value?.target_num : false;
// });

const handleClaim = async () => {
  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfig.value) {
    showToast('活动已结束~');
    return;
  }
  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  // if (completed.value) {
  //   showToast('任务已完成');
  //   return;
  // }
  switch (setting.value.actionType) {
    case 'toast':
      showToast(setting.value.tip);
      break;
    case 'modal':
      toggle(true);
      break;
    default:
      break;
  }
};

const handleVisitGame = () => {
  // 事件上报--点击进入游戏人数
  uploadLog({
    event_name: 'click',
    click_id: 7,
    click_type: 3,
  });
  showToast(setting.value.guideModal?.visitGame?.tip || '请前往游戏登录参与预约抽卡~');
};

const handleClose = () => {
  toggle(false);
};
</script>

<style>
.task-item {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
