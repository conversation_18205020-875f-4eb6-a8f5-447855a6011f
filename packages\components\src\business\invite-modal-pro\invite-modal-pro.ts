import type { InviteModalPosterSetting } from './invite-modal-poster/invite-modal-poster';
import type { InviteModalLinkSetting } from './invite-modal-link/invite-modal-link';

export type CombineSetting = {
  link: InviteModalLinkSetting
  poster: InviteModalPosterSetting
}

export interface InviteModalProSetting {
  /**
   * 邀请方式，1 复制链接 2 分享海报
  */
  actionMode: number
  /**
   * 指定复制地址
   */
  customUrl?: string
  /**
    * 分享模式：复制链接
    */
  link?: InviteModalLinkSetting
  /**
    * 分享模式：分享海报
    */
  poster?: InviteModalPosterSetting
  /**
    * 分享模式：复制链接+分享海报
    */
  combine?: CombineSetting
}