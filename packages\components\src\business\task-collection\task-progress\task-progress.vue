<!--
* @Description: 任务进度条
-->
<template>
  <Milestone
    v-model:setting="vSetting"
    :percent="percent"
    :marks="vSetting.marks"
    class="task-progress"
  >
    <template #mark="{ item, isActive }">
      <div class="task-progress-mark">
        <Resizable
          v-model:x="vSetting.mark.item.x"
          v-model:y="vSetting.mark.item.y"
          v-model:width="vSetting.mark.item.width"
          v-model:height="vSetting.mark.item.height"
          :confined="false"
          :baseline="false"
          class="task-progress-mark-item"
        >
          <img
            :src="(item as MarksArrItem).item"
            style="width: 100%; height: 100%;"
          />
        </Resizable>
        <!-- 节点内容 -->
        <Resizable
          v-model:x="vSetting.mark.content.x"
          v-model:y="vSetting.mark.content.y"
          v-model:width="vSetting.mark.content.width"
          v-model:height="vSetting.mark.content.height"
          :confined="false"
          class="task-progress-mark-content"
        >
          <ResizableProvider>
            <!-- 节点名称 -->
            <UiText
              v-if="vSetting.mark.name.enabled"
              v-model:setting="vSetting.mark.name"
              class="task-progress-mark-name"
            >
              {{ (item as MarksArrItem).name }}
            </UiText>
          </ResizableProvider>
        </Resizable>
      </div>
    </template>
  </Milestone>
</template>

<script lang="ts">
import type { MarksArrItem } from '../../../common/milestone/index.vue';
import type { TaskProgressSetting } from './task-progress';

export interface TaskProgressProps {
  /**
   * 配置
   */
  setting: TaskProgressSetting
}

export default {
  name: 'task-progress',
};
</script>

<script lang="ts" setup>
import { withDefaults, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import type { ComponentWithUserInfoTask } from '@bish/api/src/activity';
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import UiText from '../../../ui/text/index.vue';
import Milestone from '../../../common/milestone/index.vue';
import { defaultConfig } from './index';

const props = withDefaults(defineProps<TaskProgressProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskProgressSetting): void;
}>();

const activityStore = useActivityStore();
const adminStore = useAdminStore();

const vSetting = useVModel(props, 'setting', emits);

// 关联的任务列表
const relatedTaskList = computed(() => { 
  const ids = vSetting.value.marks.map(item => item.taskId);
  const taskList: ComponentWithUserInfoTask = [];
  ids.forEach((item) => {
    const task = activityStore.componentWithUserInfo.component_with_user_info?.task?.find((task) => task.id === item);
    if (task) {
      taskList.push(task);
    }
  });
  return taskList;
});

const percent = computed(() => {
  const { marks = [] } = props.setting;
  const newMarks = [...marks];
  // 区间数
  const sectionNum = 1 / (marks.length * 2);
  // 完成数
  let num = relatedTaskList.value.reduce((acc, item) => acc + (item.status !== 1 ? 1 : 0), 0);

  // 后台端模拟数据
  if (adminStore.editable) {
    return 50;
  }
  
  const minPoint = 1;
  const maxPoint = relatedTaskList.value.length || 1;

  // 超过最大里程阀值，直接 100
  if (num >= maxPoint) {
    return 100;
  }
  // 进度处在 0 到 第一节点值
  if (num <= minPoint) {
    return +((num / minPoint) * sectionNum * 100).toFixed(2);
  }
  for (let i = 0; i < newMarks.length; i++) {
    const currentPoint = i + 1 || 0;
    const nextPoint = i + 2 || 0;

    if (num > currentPoint && num <= nextPoint) {
      // 当前节点前面的区间数，是为 1、3、5、7 的等差数列
      const frontSectionNum = sectionNum * (2 * (i + 1) - 1);
      // sectionNum * 2，表示占两段的区间
      return +((((num - currentPoint) / (nextPoint - currentPoint)) * sectionNum * 2 + frontSectionNum) * 100).toFixed(2);
    }
  }
  return 0;
})
</script>

<style lang="less" scoped>
.task-progress {
  &-mark {
    &-content {}

    &-point {}

    &-name {}
  }
}
</style>
