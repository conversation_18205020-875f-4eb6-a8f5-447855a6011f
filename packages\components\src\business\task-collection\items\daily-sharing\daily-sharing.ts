import type { TaskCollectionCommon } from '../index'
import type { ImageSetting } from '../../../../ui/image/index.vue'
import type { ShareGuideSetting } from '../../../../business/share-guide';

export type ActionType = 'toast' | 'modal'

/**
 * 任务-每日任务配置
 */
export type TaskDailySharingSetting = TaskCollectionCommon & {
  /**
   * 去完成按钮
   */
  claimBtn: ImageSetting
  /**
   * 是否要求登录，，默认 1
   */
  needLogin: number
  /**
   * 分享引导，WEB端使用，微信小程序直接进行分享
   */
  shareGuide: ShareGuideSetting
}
