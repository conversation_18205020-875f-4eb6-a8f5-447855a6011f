import type { CommonBackgroundSetting } from '@bish/types/src'
import type { TextSetting } from '../../../ui/text/index.vue';
import type { BoxLotterySetting as BasicBoxLotterySetting } from '../../box-lottery/index.vue';

export interface BoxLotterySetting extends CommonBackgroundSetting  {
  /**
   * 抽奖id
   */
  lotteryId: number
  /**
   * 九宫格抽奖配置
   */
  boxLottery: BasicBoxLotterySetting & {
    /**
     * 宫格数
     */
    boxGrid: number
  }
  /**
   * 是否需要绑定角色
   */
  needBindRole?: number
  /**
   * 完成进度
   */
  process: TextSetting
}