import type { TaskCollectionCommon } from '../index';
import type { ImageSetting } from '../../../../ui/image/index.vue';
import type { InvitationModalSetting } from '../../../invitation-modal.vue';
import type { PopupSetting } from '../../../../common/popup.vue';
import type { CommonSetting } from '@bish/types/src';
import type { InviteModalSetting } from '../../../invite-modal.vue';


export type TableStyle = 'intricate' | 'simple'

export type lossFriendsModalSetting = PopupSetting & {
  /**
   * 复制链接按钮
   */
  copyLinkBtn?: ImageSetting
  /**
   * 表格
   */
  table: CommonSetting & {
    /**
     * 样式
     */
    style: TableStyle
    /**
     * 圆角，默认 0
     */
    radius: number
    /**
     * 边框配置
     */
    border: {
      /**
       * 宽度
       */
      width: number
      /**
       * 颜色
       */
      color: string
    }
    /**
     * 表头
     */
    head: {
      /**
       * 字号
       */
      fontSize?: number
      /**
       * 是否加粗
       */
      fontWeight?: boolean
      /**
       * 颜色
       */
      color: string
      /**
       * 背景
       */
      background?: string
      /**
       * 是否显示头部，可选 0否 1是，默认 1
       */
      show?: number
    }
    /**
     * 表格主体
     */
    body: {
      /**
       * 字号
       */
      fontSize?: number
      /**
       * 是否加粗
       */
      fontWeight?: boolean
      /**
       * 颜色
       */
      color: string
      /**
       * 背景
       */
      background?: string
    }
  }
}

/**
 * 任务-邀请好友
 */
export type TaskInviteLossFriendsSetting = TaskCollectionCommon & {
  /**
     * 去邀请按钮
     */
  claimBtn: ImageSetting
  /**
   * 邀请好友弹窗
   */
  inviteCard: InviteModalSetting
  /**
   * 接受好友邀请弹窗
   */
  invitationCard: InvitationModalSetting
  /**
   * 任务未完成提示
   */
  notAccomplishTip?: string
  /**
   * 任务未完成跳转
   */
  notAccomplishUrl?: string
  /**
   * 分享提示
   */
  shareTips: PopupSetting & {
    /**
     * 是否开启，0不开启 1开启，不开启则不展示
     */
    open: number
    /**
     * 操作按钮
     */
    actionBtn: ImageSetting
  }
  lossFriendsModal: lossFriendsModalSetting
}