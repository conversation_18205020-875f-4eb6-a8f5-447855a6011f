<!--
* @Description: 基础组队-加入队伍
-->
<template>
  <Resizable
    class="team-join"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
  >
    <ResizableProvider>
      <!-- 加入队伍 -->
      <UiImage
        v-if="setting.joinBtn.enabled && !currenTeam?.team_id"
        v-model:setting="setting.joinBtn"
        :class="{ 'team-join-btn__disabled': !teamConfig }"
        @click="handleOpenList"
      />

      <!-- 已组队队伍 -->
      <UiImage
        v-if="setting.joinedBtn.enabled && currenTeam?.team_id"
        v-model:setting="setting.joinedBtn"
      />
    </ResizableProvider>

    <!-- 弹窗 -->
    <Popup
      z-index="99"
      :show="openList"
      v-model:setting="setting.modal"
      @close="handleClose"
    >
      <!-- 列表 -->
      <Resizable
        v-model:x="setting.modal.list.x"
        v-model:y="setting.modal.list.y"
        v-model:width="setting.modal.list.width"
        v-model:height="setting.modal.list.height"
        class="team-join-list"
        @scroll="handleScroll"
      >
        <div
          v-for="item in mergedList"
          :key="item.id"
          class="team-join-list-item"
        >
          <div class="team-join-list-item-name">
            <UiText v-model:setting="setting.modal.item.name" :movable="false">
              <div class="team-join-list-item-name-content">
                <Avatar
                  v-if="item.team_list[0]?.career"
                  :width="setting.modal.item.name.height * 0.8"
                  :height="setting.modal.item.name.height * 0.8"
                  :career="item.team_list[0].career"
                  :sex="item.team_list[0].sex"
                />
                {{ item.team_name }}
              </div>
            </UiText>
          </div>
          <div class="team-join-list-item-count">
            <UiText v-model:setting="setting.modal.item.count" :movable="false">
              {{ `${item.current_joined_number}人` }}
            </UiText>
          </div>
          <div class="team-join-list-item-action">
            <!-- 不是队员 -->
            <template v-if="item.is_join === 1">
              <!-- 已满员 -->
              <UiImage
                v-if="item.team_status === TEAM_STATUS_FULL"
                v-model:setting="setting.modal.fullBtn"
                :movable="false"
              />
              <UiImage
                v-else-if="!currenTeam?.team_id"
                v-model:setting="setting.modal.joinBtn"
                :movable="false"
                @click="() => handleJoinTeam(item)"
              />
            </template>
            <template v-else>
              <!-- 已加入 -->
              <UiImage
                v-model:setting="setting.modal.joinedBtn"
                :movable="false"
              />
            </template>
          </div>
        </div>
        <!-- 空 -->
        <img v-if="!mergedList.length" :src="img_empty" class="team-join-list-empty" />
      </Resizable>

      <!-- 创建队伍按钮 -->
      <UiImage
        v-if="showCreateBtn"
        v-model:setting="setting.modal.createBtn"
        @click.stop="handleCreateTeam"
      />
    </Popup>

    <!-- 确认加入队伍弹窗 -->
    <Popup
      z-index="99"
      :show="openConfirm"
      v-model:setting="setting.confirmModal"
      @close="handleCloseConfirm"
      @ok="handleConfirm"
    >
      <!-- 文本内容 -->
      <UiText
        v-if="setting.confirmModal.content.enabled"
        v-model:setting="setting.confirmModal.content"
        :interpolation="interpolation"
        class="team-disband-content"
      />

      <!-- 取消按钮 -->
      <UiImage
        v-if="setting.confirmModal.cancelBtn.enabled"
        v-model:setting="setting.confirmModal.cancelBtn"
        @click.stop="handleCloseConfirm"
      />
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'team-join',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import usePopupStore from '@bish/store/src/modules/popup';
import useAdminStore from '@bish/store/src/modules/admin';
import type { StatefulComponent } from '@bish/types/src/admin';
import { showToast } from '@bish/ui/src/toast';
import { useLog } from '@bish/hooks/src/useLog';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { postTeamList, joinTeam } from '@bish/api/src/team-gift';
import type { TeamListData } from '@bish/api/src/team-gift';
import { usePagination, type PaginationService } from '@bish/hooks/src/usePagination';
import UiImage from '../../../ui/image/index.vue';
import UiText from '../../../ui/text/index.vue';
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import Popup from '../../../common/popup.vue';
import Avatar from '../../../common/avatar/index.vue'
import { defaultConfig, TEAM_STATUS_FULL } from './index';
import type { TeamJoinSetting } from './team-join';
import img_empty from './images/empty.png';

export interface TeamJoinProps {
  /**
   * 配置
   */
  setting: TeamJoinSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const mockTeamList: TeamListData = [
  {
    id: 1,
    team_name: '队伍1',
    team_status: 1,
    current_joined_number: 88,
    team_count_number: 100,
    is_join: 2,
    team_list: [],
  },
  {
    id: 2,
    team_name: '队伍2',
    team_status: 2,
    current_joined_number: 88,
    team_count_number: 100,
    is_join: 1,
    team_list: [],
  },
  {
    id: 3,
    team_name: '队伍3',
    team_status: 3,
    current_joined_number: 88,
    team_count_number: 100,
    is_join: 1,
    team_list: [],
  },
];

const props = withDefaults(defineProps<TeamJoinProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: TeamJoinSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const current = ref<TeamListData[0] | null>(null);
const joining = ref(false);

const setting = useVModel(props, 'setting', emits);

const activityStore = useActivityStore();
const popupStore = usePopupStore();
const adminStore = useAdminStore();

const [openList, toggleList] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showTeamListModal' });
const [openConfirm, toggleConfirm] = useControllableStatus(props as StatefulComponent, emits, { fieldName: 'showTeamJoinConfirmModal' });

const { uploadLog } = useLog();

const emptyData = () => ({ list: [], count: 0 });

const fetchTeamList: PaginationService<TeamListData[0]> = async (paginationParams) => {
  try {
    const { activityAccountInfo } = activityStore;

    if (!activityAccountInfo.activity_id) {
      return Promise.resolve(emptyData());
    }
    const res = await postTeamList({
      ...paginationParams,
      act_id: activityAccountInfo.activity_id,
      act_acc_id: activityAccountInfo.act_acc_id,
      team_config_id: setting.value.teamId,
    });
    if (res.code === 0) {
      return Promise.resolve(res.data);
    }
    return Promise.resolve(emptyData());
  } catch (error) {
    console.warn('获取队伍列表失败', error);
    return Promise.reject(error);
  }
};

const { listState, firstLoad, loadMore, refreshList } = usePagination<TeamListData[0]>(fetchTeamList, {
  afterFirstLoad: () => {
    // firstLoading.value = false;
  },
  afterRefresh: () => {
    // uni.stopPullDownRefresh();
  },
});

const mergedList = computed(() => {
  return adminStore.editable ? mockTeamList : listState.list;
});

// 队伍信息配置信息
const teamConfig = computed(() => {
  const { team } = activityStore.activityInfo.config || {};
  return team?.find(item => item.id === setting.value.teamId);
});

// 与配置对应的队伍信息
const currenTeam = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.team?.find(item => item.id === setting.value.teamId);
});

const interpolation = computed(() => {
  return {
    teamName: `${current.value?.team_name || '未知的队伍'}`,
  };
});

const showCreateBtn = computed(() => {
  if (adminStore.editable) {
    return props.setting.modal.createBtn.enabled;
  }
  return props.setting.modal.createBtn.enabled && !listState.list.length
});

watch(
  () => popupStore.showTeamList,
  (newVal) => {
    if (newVal) {
      firstLoad();
    }
    toggleList(newVal);
  },
);

const handleOpenList = () => {
  // 事件上报:创建队伍
  // uploadLog({
  //   event_name: 'click',
  //   click_id: 13,
  //   click_type: 3,
  // });
  // 找不到队伍配置（队伍下架了）
  if (!teamConfig.value) {
    showToast('活动已结束~');
    return;
  }
  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }

  popupStore.setShowTeamList(true);
};

const handleClose = () => {
  // 同步数据
  if (openList && !popupStore.showTeamList) {
    toggleList(false);
  }
  popupStore.setShowTeamList(false);
};

const handleJoinTeam = (team: TeamListData[0]) => {
  current.value = team;
  toggleConfirm(true);
};

const handleCloseConfirm = () => {
  toggleConfirm(false);
};

const handleConfirm = async () => {
  const { activityAccountInfo } = activityStore;
  if (activityAccountInfo?.role_info?.role_id) {
    if (joining.value) {
      showToast('请勿重复操作~');
      return;
    }
    joining.value = true;

    try {
      const { code } = await joinTeam({
        act_id: activityAccountInfo.activity_id,
        act_acc_id: activityAccountInfo.act_acc_id,
        team_id: current.value?.id!,
        inviter: '0',
        server_id: activityAccountInfo.role_info.zone_id,
        server_name: activityAccountInfo.role_info.server_name,
        role_id: +activityAccountInfo.role_info.role_id,
        role_name: activityAccountInfo.role_info.role_name,
        sex: activityAccountInfo.role_info.sex,
        career: +activityAccountInfo.role_info.career,
      });
      // 重新请求活动相关数据
      activityStore.getComponentWithUserInfo();
      if (!code) {
        showToast('加入队伍成功');
      }
    } catch (error) {
      console.log('加入组队失败', error);
    } finally {
      joining.value = false;
      handleCloseConfirm();
      handleClose();
    }
  }
};

const handleCreateTeam = () => {
  handleClose();
  popupStore.setShowTeamCreate(true);
};

const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  const { scrollTop, clientHeight, scrollHeight } = target;

  // 判断是否滚动到底部
  if (scrollTop + clientHeight >= scrollHeight - 100) {
    // 触发加载更多数据的逻辑
    loadMore();
  }
};

</script>

<style>
.team-join {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.team-join-affix {
  position: fixed;
  left: auto !important;
  z-index: 98;
}

.team-join-list {
  overflow-y: auto;
  overflow-x: hidden;
}

.team-join-list-empty {
  width: 40%;
  height: auto;
  display: block;
  margin: 25% auto 0;
}

.team-join-list-item {
  display: flex;
  align-items: center;
  border-bottom: 1px dashed #363636;
}

.team-join-list-item-name {}

.team-join-list-item-name-content {
  display: flex;
  align-items: center;
}

.team-join-list-item-name-content > .avatar {
  flex-shrink: 0;
}

.team-join-list-item-count {}

.team-join-list-item-action {
  flex: 1;
  text-align: center;
}

.team-join-btn__disabled {
  filter: grayscale(100%);
}
</style>