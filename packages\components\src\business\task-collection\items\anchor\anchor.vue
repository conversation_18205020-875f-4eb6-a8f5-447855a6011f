<!--
* @Description: 任务-跳转链接
-->
<template>
  <TaskItem v-model:setting="setting" v-model:status="status" @do="handleClaim" />
</template>

<script lang="ts">
export default {
  name: 'task-anchor',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits } from 'vue';
import { useVModel } from '@vueuse/core';
import type { StatefulComponent } from '@bish/types/src/admin';
import { navigateScroll } from '../../../../__utils/location';
import { defaultConfig } from './index';
import type { TaskAnchorSetting } from './anchor';
import TaskItem from '../item.vue';

export interface TaskAnchorProps {
  /**
   * 配置
   */
  setting: TaskAnchorSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TaskAnchorProps>(), {
  setting: () => defaultConfig(),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskAnchorSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const setting = useVModel(props, 'setting', emits);
const status = useVModel(props, 'status', emits);

const handleClaim = async () => {
  if (setting.value.url) {
    navigateScroll(setting.value.url);
  }
};
</script>

<style>
</style>
