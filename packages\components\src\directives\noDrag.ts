// src/directives/noDrag.ts
import { watchEffect } from 'vue';
import type { DirectiveBinding, VNode } from 'vue';

interface NoDragOptions {
  observeElement?: (el: Element, vnode: VNode, disabled: boolean) => void;
  unobserveElement: (vnode: VNode) => void;
  updateDraggable?: (el: Element, vnode: VNode, disabled: boolean) => void;
}

export default {
  mounted(el: Element, binding: DirectiveBinding, vnode: VNode) {
    const options: NoDragOptions = {
      observeElement(el, vnode, disabled) {
        if (disabled) {
          return; // 如果 disabled 为 true，则不执行任何操作
        }

        const observer = new MutationObserver(mutations => {
          mutations.forEach(mutation => {
            const addedNodes = mutation.addedNodes;
            addedNodes.forEach(node => {
              if (node instanceof Element) {
                if (node.tagName.toLowerCase() === 'img') {
                  (node as HTMLImageElement).draggable = false;
                }
                // 如果添加的是元素节点，递归处理
                options.observeElement?.(node, vnode, disabled);
              }
            });
          });
        });

        observer.observe(el, { childList: true, subtree: true });

        // 使用 watchEffect 监听相关状态的变化
        watchEffect(() => {
          options.updateDraggable?.(el, vnode, disabled);
        });
      },
      unobserveElement(vnode) {
        const observer = new MutationObserver(() => {});
        observer.disconnect();

        // 清除 watchEffect
        // 注意：这里需要根据实际情况来处理 watchEffect 的清除逻辑
      },
      updateDraggable(el: Element, vnode: VNode, disabled: boolean) {
        if (disabled) {
          return; // 如果 disabled 为 true，则不执行任何操作
        }

        const imgs = el.querySelectorAll('img');
        imgs.forEach(img => {
          img.draggable = false;
        });
      },
    };
    options.observeElement?.(el, vnode, binding.value?.disabled || false);
  },
  unmounted(el: Element, binding: DirectiveBinding, vnode: VNode) {
    const options: NoDragOptions = {
      unobserveElement: (vnode) => {
        const observer = new MutationObserver(() => {});
        observer.disconnect();
      },
    };

    options.unobserveElement(vnode);
  },
};