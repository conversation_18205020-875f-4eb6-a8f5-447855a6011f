import type { TeamMembersSetting } from './team-members';
import { defaultConfig as teammateDefaultConfig } from '../../teammate/index.vue';

export * from './team-members';

export const defaultConfig = (): TeamMembersSetting => {
  const teammateConfig = teammateDefaultConfig();
  return {
    teamId: 0,
    ...teammateConfig,
    x: 0,
    y: 0,
    width: 357,
    height: 112,
    item: {
      x: 0,
      y: 0,
      width: 80,
      height: 80,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250419/1745063900105-teammate-position.png',
      defaultAvatar: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-8-6/1722926730553_.png',
      name: {
        x: 6,
        y: 84,
        width: 69,
        height: 28,
        fontSize: 10,
        color: '#215D39',
        align: 'center',
      },
      showServerName: true,
    },
    items: [], // 重置为 []
  };
};