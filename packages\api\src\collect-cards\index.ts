import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'
const { VITE_ACTIVITY_URL } = useEnv()

type CollectionRecordParams = {
  act_id: number
  act_acc_id: string
  type: 1 | 2
}

/**
 * @description 获取赠送收取记录
 * @param {number} params.act_id 活动ID
 * @param {string} params.act_acc_id 活动系统加密的账号id，登录状态需要
 * @param {number} params.type 记录类型 1: 赠送 2: 收取
 * @returns
 */
export function postCollectionRecord(params: CollectionRecordParams) {
  const { act_id, act_acc_id, type } = params
  return http.post<CollectionRecordParams>(`${VITE_ACTIVITY_URL}/collect_draw/share_list`, {
    act_id: `${act_id}`,
    act_acc_id: act_acc_id,
    type: `${type}`,
  })
}

/**
 * @description 获取获奖名单
 * @param {number} params.act_id 活动ID
 * @returns
 */
export function postGetWinnerList (params: { act_id: number }) {
  const { act_id } = params 
  return http.post<any>(`${VITE_ACTIVITY_URL}/collect_draw/winner_list`, {
    act_id: `${act_id}`,
  })
}

export type CollectDrawShareParams = {
  /**
   * 活动id
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 卡片id
   */
  item_id: number
  /**
   * 类型 1为分享 2为索求
   */
  type: 1 | 2
}

export type CollectDrawShareData = {
  /**
   * 分享记录id
   */
  id: number
}

/**
 * 发起赠送/索要
 * @param params CollectDrawShareParams
 * @returns 
 */
export function postCollectDrawShare(params: CollectDrawShareParams) {
  return http.post<CollectDrawShareData>(`${VITE_ACTIVITY_URL}/collect_draw/share_card`, params)
}

export type CollectDrawTurnDetailParams = {
  /**
   * 活动id
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 卡片id
   */
  item_id: number
  /**
   * 操作类型 3为领取 4为赠送
   */
  operate_type: 3 | 4
  /**
   * 分享记录ID
   */
  share_log_id: number
}

export type CollectDrawTurnDetailData = {
  /**
   * 卡片id
   */
  item_id: number
  /**
   * 卡片名称
   */
  item_name: string
  /**
   * 卡片icon 
   */
  item_icon: string
  /**
   * 是否已领取 1为否 2为是
   */
  is_draw: number
  /**
   * 是否已有 1为否 2为是
   */
  is_had: number
  /**
   * 拥有卡片数量
   */
  item_num: number
}

/**
 * 卡流转详情
 * @param params CollectDrawTurnDetailParams
 * @returns 
 */
export function postCollectDrawTurnDetail(params: CollectDrawTurnDetailParams) {
  return http.post<CollectDrawTurnDetailData>(`${VITE_ACTIVITY_URL}/collect_draw/card_turn_detail`, params)
}

export type CollectDrawOperateParams = {
  /**
   * 活动id
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 分享记录id
   */
  share_log_id: number
  /**
   * 卡片id
   */
  item_id: number
  /**
   * 操作类型 3为领取 4为赠送
   */
  operate_type: 3 | 4
}

/**
 * 赠送/接受卡片操作
 * @param params CollectDrawOperateParams
 * @returns 
 */
export function postCollectDrawOperate(params: CollectDrawOperateParams) {
  return http.post(`${VITE_ACTIVITY_URL}/collect_draw/operate`, params)
}
