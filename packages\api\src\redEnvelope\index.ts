import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'
const { VITE_ACTIVITY_URL } = useEnv()

export type ExchangeRedeemCodeParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
  /**
   * 角色id 格式：104689_rhtest_2
   */
  role_id: string
  /**
   * 红包口令码
   */
  red_code: string
}

/**
 * 口令红包兑换
 * @param params ExchangeRedeemCodeParams
 * @returns
 */
export function exchangeRedeemCode(params: ExchangeRedeemCodeParams) {
  return http.post(`${VITE_ACTIVITY_URL}/red_packet/exchange_redeem_code`, params)
}

export type WithdrawRedBalanceParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
}

/**
 * @description: 红包余额提现
 * @param {object} WithdrawRedBalanceParams
 * @return {*}
 */
export function withdrawRedBalance(params: WithdrawRedBalanceParams) {
  return http.post(`${VITE_ACTIVITY_URL}/red_packet/withdraw_red_balance`, params)
}

export type WithdrawRedPacketParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
  /**
   * 提现金额
   */
  amount: number
  /**
   * 人机校验
   */
  rand_str?: string
  /**
   * 人机校验ticket
   */
  ticket?: string
}

/**
 * @description: 钱包余额提现
 * @param {object} WithdrawRedPacketParams
 * @return {*}
 */
export function withdrawRedPacket(params: WithdrawRedPacketParams) {
  return http.post(`${VITE_ACTIVITY_URL}/red_packet/withdraw`, params)
}

/**
 * @description: 邀请红包任务 - 接受邀请接口
 * @param {object} data
 * @return {*}
 */
export function postRedEnvelopeTaskInvite(data: object, errToast = false) {
  return http.post(`${VITE_ACTIVITY_URL}/red_packet/accept_invite`, data, { errToast })
}


export type SetPromoteEarningsParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，数据来源于 获取活动账号信息
   */
  act_acc_id: string
  /**
   * 是否暴击 1否 2是
   */
  is_promote: number
}

/**
 * @description: 设置提升收益
 * @param {object} SetPromoteEarningsParams
 * @return {*}
 */
export function setPromoteEarnings(params: SetPromoteEarningsParams) {
  return http.post(`${VITE_ACTIVITY_URL}/red_packet/set_promote_earnings`, params)
}