<!--
* @Description: 商城充值任务
-->
<template>
  <TaskItem v-model:setting="setting" @do="handleClaim">
    <Popup
      v-if="setting.weappEmbedSetting?.modalSetting"
      :show="openWeappEmbed"
      v-model:setting="setting.weappEmbedSetting.modalSetting"
      z-index="101"
      :resizable="false"
      @close="() => toggleWeappEmbed(false)"
    >
    </Popup>
  </TaskItem>
</template>

<script lang="ts">
export default {
  name: 'task-mall-recharge',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import useUserStore from '@bish/store/src/modules/user';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { userAgent, additionalLinkParameters } from '@bish/utils/src/utils';
import { useShop } from '@bish/hooks/src/useShop';
import { defaultConfig as TASK_MALL_RECHARGE_CONFIG } from './index';
import type { TaskMallRechargeSetting } from './mall-recharge';
import TaskItem from '../item.vue';
import Popup from '../../../../common/popup.vue';

export interface TaskMallRechargeProps {
  /**
   * 配置
   */
  setting: TaskMallRechargeSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TaskMallRechargeProps>(), {
  setting: () => TASK_MALL_RECHARGE_CONFIG(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskMallRechargeSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityPageStore = useActivityPageStore();
const activityStore = useActivityStore();
const userStore = useUserStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const [openWeappEmbed, toggleWeappEmbed] = useControllableStatus(props, emits, { fieldName: 'showWeappEmbed' });

const { shopUrl } = useShop();

const { isUserAgentType } = userAgent();

/**
 * 跳转商城
 */
const handleGoShop = () => {
  const params: Record<string, any> = {}
  
  if (userStore.userData.token) {
    params.token = userStore.userData.token;
  }
  const newShopUrl = additionalLinkParameters(
    params,
    shopUrl.value,
  );
  window.open(newShopUrl, !adminStore.editable ? '_self' : '_blank');
};

/**
 * 跳转到小程序内嵌前往充值页面
 */
const navigateWeappDownload = () => {
  const { init } = activityStore.activityInfo;
  const { themeColor } = activityPageStore.mergedPageConfig;
  
  let cfg = '';
  if (setting.value?.weappEmbedSetting) {
    cfg = JSON.stringify({
      ...setting.value?.weappEmbedSetting,
      theme: themeColor, // 附加主题色配置
    });
  }
  
  // TODO: 把小程序前往充值页面提到环境变量
  const weappBishMallRechargeUrl = '/subpkg/bish/mall-recharge';
  const url = additionalLinkParameters(
    {
      cfg: encodeURIComponent(cfg),
      project_id: init?.project_id,
    },
    weappBishMallRechargeUrl,
  );

  wx?.miniProgram.navigateTo({
    url,
  });
};

const handleClaim = async () => {
  // 微信小程序内嵌环境：跳转指定的登录页面
  if (isUserAgentType === 'WX_WEBVIEW') {
    navigateWeappDownload();
    return;
  }
  handleGoShop();
};
</script>

<style>
.task-item {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style> 