import { defaultConfig as recordsDefaultConfig } from '../records/records.vue';
import type { WinningListSetting } from './winning-list';

export * from './winning-list';

export const defaultConfig = (): WinningListSetting => {
  const recordsDefault = recordsDefaultConfig();
  return {
    ...recordsDefault,
    x: 0,
    y: 0,
    width: 50,
    height: 50,
    bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250312/1741761861002-winner-e.png',
    text: {
      x: 0,
      y: 15,
      width: 50,
      height: 20,
      fontSize: 14,
      color: '#1887D3',
      align: 'center',
      alignItems: 'center',
      content: '',
      textDecoration: 'underline',
    },
    modalSetting: {
      x: 0,
      y: 0,
      width: 373,
      height: 409,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250312/1741761173566-winner-bg.png',
      okBtn: {
        width: 0,
        height: 0,
      },
      table: {
        x: 42,
        y: 149,
        width: 297,
        height: 214,
        style: 'simple',
        radius: 0,
        border: {
          width: 1,
          color: '#D7B29E',
        },
        head: { 
          color: '#72450D',
          show: 0,
        },
        body: {
          color: '#B04430',
        },
      },
      closeBtn: {
        x: 336,
        y: -2,
        width: 27,
        height: 25,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250312/1741751471690-x.png',
      },
    },
    columns: [
      {
        title: '奖项',
        dataIndex: 'grade',
      },
      {
        title: '奖励名称',
        dataIndex: 'name',
      },
      {
        title: '玩家昵称',
        dataIndex: 'role_name',
      },
      {
        title: '中奖时间',
        dataIndex: 'open_at',
      },
    ],
  }
}; 