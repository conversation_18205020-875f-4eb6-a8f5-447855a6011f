<template>
  <div class="picker">
    <picker-tollbar
      v-if="showToolbar"
      @cancel="cancel"
      @confirm="confirm"
    >
      {{ title }}
    </picker-tollbar>
      
    <!-- #ifdef WEB -->
    <template v-if="!isWeapp">
      <van-picker
        v-bind="props"
        :model-value="defaultValues"
        @change="(option: PickerChangeEvent) => { changeHandler(option.columnIndex, option.selectedOptions[option.columnIndex]) }"
        :show-toolbar="false"
      />
    </template>
    <!-- #endif -->
    <!-- 微信小程序 -->
    <template v-if="isWeapp">
      <picker-view
        :style="pickerViewStyles"
        :indicator-style="`height:${innerOptionHeight}PX`"
        :value="delayDefaultIndexes"
        :immediate-change="true"
        mask-class="picker__mask"
        @change="handleTileChange"
        @pickstart="handlePickStart"
        @pickend="handlePickEnd"
      >
        <picker-view-column v-for="(column, columnIndex) in (columnsList as PickerOption[])" :key="columnIndex">
          <view
            v-for="(item, index) in column"
            :key="item[columnFieldNames.value] ? item[columnFieldNames.value] : index"
            class="picker-roller-item-tarotile"
            :style="{ lineHeight: pxCheck(innerOptionHeight) }"
          >
            {{ item[columnFieldNames.text] }}
          </view>
        </picker-view-column>
      </picker-view>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, toRefs } from 'vue';
import type { CSSProperties } from 'vue';
import { useEnv } from '@bish/hooks/src/useEnv';
import { pickerProps, pickerEmits } from './Picker';
import type { PickerChangeEvent, PickerOption } from './Picker';
import { componentName, usePicker } from './use-picker';
import { pxCheck } from '../../__utils/pxCheck';
import PickerTollbar from './PickerTollbar.vue';

const props = defineProps(pickerProps);

const emit = defineEmits(pickerEmits);

const { VITE_APP_TYPE } = useEnv();

const isWeapp = VITE_APP_TYPE === 'wx';

const innerVisibleOptionNum = computed(() => {
  return Number(props.visibleOptionNum);
});

const innerOptionHeight = computed(() => {
  return Number(props.optionHeight);
});

const {
  changeHandler,
  confirm,
  defaultValues,
  defaultIndexes,
  delayDefaultIndexes,
  columnsList,
  columnFieldNames,
  classes,
  cancel,
  confirmHandler,
} = usePicker(props, emit);

function componentWeapp() {
  const state = reactive({
    show: false,
    picking: false,
  });

  // 选中项的位置
  const pickerViewStyles = computed(() => {
    const styles: CSSProperties = {};
    styles.height = `${innerVisibleOptionNum.value * innerOptionHeight.value}PX`;
    styles['--line-height'] = `${innerOptionHeight.value}PX`;
    return styles;
  });

  // 平铺展示时，滚动选择
  const handleTileChange = (event: any) => {
    const indexes = event.detail.value;
    const prevIndexes = defaultIndexes.value;

    let changeIndex = 0
    // 判断变化的是第几个
    for (let i = 0; i < indexes.length; i++) {
      if (prevIndexes[i] !== indexes[i]) {
        changeIndex = i;
        break;
      }
    }

    // 选择的是哪个 option
    changeHandler(changeIndex, columnsList.value[changeIndex][indexes[changeIndex]]);
  };

  // 确定
  const confirmHandler = () => {
    if (state.picking) {
      setTimeout(() => {
        confirm();
      }, 0);
    }
    else {
      confirm();
    }
  };

  // 开始滚动
  const handlePickStart = () => {
    state.picking = true;
  };

  // 开始滚动
  const handlePickEnd = () => {
    state.picking = false;
  };

  return {
    ...toRefs(state),
    pickerViewStyles,
    handleTileChange,
    confirmHandler,
    handlePickStart,
    handlePickEnd,
  };
}

const {
  confirmHandler: confirmHandlerMp,
  handleTileChange,
  handlePickStart,
  handlePickEnd,
  pickerViewStyles,
} = componentWeapp()
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
  name: 'common-picker',
}
</script>

<style>
.picker {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  user-select: none;
}

.picker {
  position: relative;
  background: #fff;
  border-radius: 5PX;
}

.picker__bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 46PX;
}

.picker__left {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50PX;
  height: 100%;
  padding: 0 15PX;
  font-size: 14PX;
  color: #808080;
  cursor: pointer;
}

.picker__right {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50PX;
  height: 100%;
  padding: 0 15PX;
  font-size: 14PX;
  color: #1989fa;
  cursor: pointer;
}

.picker__column {
  position: relative;
  display: flex;
}

.picker__column::before {
  position: absolute;
  top: 50%;
  width: 100%;
  height: 44PX;
  content: '';
  border: 1PX solid #eae7e7;
  border-right: 0;
  border-left: 0;
  transform: scale(0.9);
  transform: translateY(-50%);
}

.picker__columnitem {
  flex-grow: 1;
  width: 0;
  height: 100%;
  cursor: grab;
  user-select: none;
}

.picker__title {
  flex: 1;
  font-size: 16PX;
  font-weight: normal;
  color: #1a1a1a;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.picker__wrapper {
  display: block;
}

.picker-roller-item-tarotile {
  display: block;
  width: 100%;
  overflow: hidden;
  font-size: 14PX;
  color: #1a1a1a;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>