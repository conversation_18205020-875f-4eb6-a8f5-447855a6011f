<template>
  <Resizable
    v-if="showPackage"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    @click="handleOpen"
  >
    <!-- 入口 -->
    <div
      class="floating-package"
      :class="{ 'floating-package-affix': setting.affix && !adminStore.editable }"
      :style="entryStyle"
    />

    <!-- 弹窗 -->
    <Popup
      z-index="99"
      :show="open"
      :lock-scroll="false"
      v-model:setting="setting.modal"
      @close="handleClose"
      @ok="handleReceive"
    >
      <ResizableProvider>
        <!-- 提示 -->
        <UiText
          v-if="setting.modal.tip.enabled"
          v-model:setting="setting.modal.tip"
        />
        <!-- 道具图片 -->
        <UiImage
          v-if="setting.modal.item.enabled"
          v-model:setting="setting.modal.item"
        />
  
        <!-- 区服-角色选择器 -->
        <ServerRolePicker
          v-model:setting="setting.modal.serverRolePicker"
          v-model:open="showServerRolePicker"
          v-model="serverRole"
          placeholder="请选择领取角色"
          :initGlobalRoleInfo="false"
          @click="handleShowServerRolePicker"
          @confirm="handleServerRoleConfirm"
        >
          <template #default="{ selectedServer, selectedRole }">
            {{ `领取角色：${selectedServer?.text || ''}-${selectedRole?.text || ''}` }}
          </template>
        </ServerRolePicker>
      </ResizableProvider>
    </Popup>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'floating-package',
}
</script>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import type { CSSProperties } from 'vue';
import { useToggle, useVModel } from '@vueuse/core';
import { showToast, showLoadingToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import type { StatefulComponent } from '@bish/types/src/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import { pxTransform } from '@bish/utils/src/viewport';
import { useLog } from '@bish/hooks/src/useLog';
import { postActivityTaskGetAwardTask } from '@bish/api/src/activity';
import type { ActivityInfoConfigTask } from '@bish/api/src/activity';
import type { PickerBaseEvent } from '@/common/picker/Picker';
import { sleep } from '@bish/utils/src/utils';
import Resizable from '../../ui/resizable/index.vue';
import ResizableProvider from '../../ui/resizable/resizable-provider.vue';
import UiImage from '../../ui/image/index.vue';
import UiText from '../../ui/text/index.vue';
import Popup from '../../common/popup.vue';
import ServerRolePicker from '../server-role-picker.vue';
import type { ServerRolePickerValue } from '../server-role-picker.vue';
import type { FloatingPackageSetting } from './floating-package';
import { defaultConfig } from './index';

export interface FloatingPackageProps {
  setting: FloatingPackageSetting
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<FloatingPackageProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: FloatingPackageSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const serverRole = ref<ServerRolePickerValue>([]);
const receiving = ref(false);

const setting = useVModel(props, 'setting', emits);
const [open, toggle] = useControllableStatus(props, emits, { fieldName: 'showFloatingPackage' });
const [showServerRolePicker, toggleShowServerRolePicker] = useToggle();

const activityStore = useActivityStore();
const adminStore = useAdminStore();

const { uploadLog } = useLog();

let lastSelectedOptions: PickerBaseEvent['selectedOptions'] = [];

const entryStyle = computed<CSSProperties>(() => {
  return {
    left: pxTransform(setting.value.x!),
    top: pxTransform(setting.value.y!),
    width: pxTransform(setting.value.width),
    height: pxTransform(setting.value.height),
    backgroundImage: `url(${setting.value.bgImage})`,
  };
});

const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

const userTask = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
});

const showPackage = computed(() => {
  return adminStore.editable || userTask.value?.status !== 3;
});

const handleOpen = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }
  // 数据上报：礼包点击数
  uploadLog({
    event_name: 'click',
    click_id: 4003,
    click_type: 3,
  });
  const pass = activityStore._checkIn(false);
  // 校验登录
  if (!pass) {
    return;
  }
  toggle(true);
};

const handleClose = () => {
  toggle(false);
};

const handleReceive = async () => {
  if (adminStore.editable) {
    return;
  }
  // 数据上报
  uploadLog({
    event_name: 'click',
    click_id: 9,
    click_type: 3,
  });
  if (!taskConfig.value) {
    showToast('活动已结束~');
    return;
  }
  // 登录态拦截
  const pass = activityStore._checkIn(false);
  if (!pass) {
    return;
  }
  // 未选择领取角色
  if (!serverRole.value.length) {
    toggleShowServerRolePicker(true);
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  if (!userTask.value) {
    return;
  }
  // 领取用户账号状态判断
  if (
    setting.value.accountStatus !== 0
    && setting.value.accountStatus !== activityStore.activityAccountInfo.status
  ) {
    if (setting.value.accountStatus === 2) {
      showToast('你不是回流用户哦！');
    } else {
      showToast('该账号暂不符合参与条件~');
    }
    return;
  }
  // 判断任务是否是已完成状态 & 任务配置了商品id
  // 没有商品id的任务，有可能是为抽奖服务的，这个是不需要领取道具奖励
  if (userTask.value.status === 2 && !hasPrize(taskConfig.value)) {
    return;
  }
  if (receiving.value) {
    return;
  }
  try {
    receiving.value = true;
    const roleOption = lastSelectedOptions[1];
    // 先获取最新的用户与组件的数据，因为可以存在任务已经完成，但是用户没有进行刷新操作
    await activityStore.getComponentWithUserInfo();
    await sleep(100);
    const res = await postActivityTaskGetAwardTask(
      {
        act_id: activityStore.activityInfo.init?.id,
        act_acc_id: activityStore.activityAccountInfo.act_acc_id,
        // 这里处理一下月度礼包，本月如果有领取的话，那么到下个月 taskLogId 是不会进行重置成 0 的
        // 只有状态会重置成 1 进行中（未完成）
        // 所以当状态是 1 的时候，taskLogId 传 0
        task_log_id: userTask.value.status === 1 ? 0 : userTask.value.task_log_id, // 这里跟上边的 未完成任务冲突 了，对于月度活动建议使用 customReceive 复写
        select_type: (taskConfig.value.reward_type === 3 || taskConfig.value.reward_type === 4) ? 2 : 1,
        role_name: roleOption.role_name,
        role_id: `${serverRole.value[1]}_${serverRole.value[0]}`,
      },
    );
    if (res.code === 0) {
      setTimeout(() => {
        handleClose();
        showToast('领取成功，快去游戏中查收！');
        // 重新获取用户与组件的数据
        activityStore.getComponentWithUserInfo();
      }, 500);
    }
  } catch (error) {
    console.warn('领取任务奖励失败', error);
  } finally {
    receiving.value = false;
  }
};

const handleShowServerRolePicker = () => {
  // 数据上报：点击领取角色
  uploadLog({
    event_name: 'click',
    click_id: 4004,
    click_type: 3,
  });
  const pass = activityStore._checkIn(false);
  if (!pass) {
    return;
  }
  toggleShowServerRolePicker(true);
};

const handleServerRoleConfirm = (value: ServerRolePickerValue, e: PickerBaseEvent) => {
  lastSelectedOptions = e.selectedOptions;
};

/**
 * 是否有奖品的
 */
const hasPrize = (tConfig?: ActivityInfoConfigTask[0]) => {
  if (!tConfig) {
    return false;
  }
  const rewardType = tConfig.reward_type;
  // 虚拟道具
  if (rewardType === 1) {
    // 没有配置道具id，不需要领取任务奖励
    return !!tConfig.item_id
  }
  return true;
};
</script>

<style>
.floating-package {
  background-repeat: no-repeat;
  background-size: 100% 100%;

  transform-origin: 50% 100%;
  animation: swing 3s infinite;
  animation-iteration-count: infinite;
}

.floating-package-affix {
  position: fixed;
  left: auto !important;
  z-index: 98;
}

@keyframes swing {
  0%, 100% {
    transform: rotate(0deg);
  }
  5% {
    transform: rotate(-1.5deg); /* 摇晃的距离可以根据需要调整 */
  }
  10% {
    transform: rotate(-1.5deg); /* 摇晃的距离可以根据需要调整 */
  }
  15% {
    transform: rotate(1.5deg); /* 摇晃的距离可以根据需要调整 */
  }
  20% {
    transform: rotate(-1.5deg); /* 摇晃的距离可以根据需要调整 */
  }
  25% {
    transform: rotate(0deg); /* 摇晃的距离可以根据需要调整 */
  }
}
</style> 
