<!--
* @Description: 集卡-卡组列表
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="collect-cards-group"
  >
    <ResizableProvider>
      <Swiperc
        :loop="receiveLog.length > 3"
        effect="coverflow"
        :centeredSlides="true"
        :modules="modules"
        slidesPerView="auto"
        :initialSlide="0"
        :loopAdditionalSlides="2"
        :loopFillGroupWithBlank="true"
        :watchSlidesProgress="true"
        :coverflowEffect="{
          rotate: 0,
          stretch: 20,
          depth: 160,
          modifier: 2,
          slideShadows: false,
        }"
        :enabled="!adminStore.editable"
        @swiper="handleSwiperInit"
        @activeIndexChange="handleActiveIndexChange"
        class="mySwiper"
      >
        <SwipercSlide v-for="(item, index) in receiveLog" :key="item.id" :style="slideStyle">
          <CollectCardsItem
            :setting="setting.item"
            :active="current === index"
            :data="card"
            :locked="item.id === -1"
            :scratchable="item.is_receive_gift === 1"
            :scratched="item.is_receive_gift === 2"
            :isGift="item.receive_type === 2"
            @scratch="() => handleScratch(item)"
          />
        </SwipercSlide>
      </Swiperc>

      <!-- 索引 -->
      <UiText
        v-if="setting.indicator.enabled && showPagination"
        :setting="setting.indicator"
        :interpolation="interpolation"
        :confined="false"
        :style="controlStyle"
      />

      <!-- 上一页 -->
      <UiImage
        v-if="showPagination"
        :setting="setting.prev"
        :confined="false"
        :style="controlStyle"
        @click="handlePrev"
      />

      <!-- 下一页 -->
      <UiImage
        v-if="showPagination"
        :setting="setting.next"
        :confined="false"
        :style="controlStyle"
        @click="handleNext"
      />
    </ResizableProvider>

    <!-- <button @click="swiperInst?.slideNext()">Slide to the next slide</button> -->
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'collect-cards',
}
</script>

<script lang="ts" setup>
import { computed, ref, watchEffect, nextTick, watch } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import { Swiper } from 'swiper'
import { Swiper as Swiperc, SwiperSlide as SwipercSlide } from 'swiper/vue';
// Import Swiper styles
import 'swiper/css';
import { Navigation, EffectCoverflow } from 'swiper/modules';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { pxTransform } from '@bish/utils/src/viewport';
import { useLog } from '@bish/hooks/src/useLog';
import type {
  ActivityInfoConfigCollectDrawDetail,
  ComponentWithUserInfoCollectDrawCardList,
  ComponentWithUserInfoCollectDrawCardListReceiveLog,
} from '@bish/api/src/activity';
import Resizable from '../../../../ui/resizable/index.vue';
import ResizableProvider from '../../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../../ui/image/index.vue';
import UiText from '../../../../ui/text/index.vue';
import type { CollectCardsGroupSetting } from './collect-cards-group';
import { defaultConfig } from './index';
import CollectCardsItem from '../collect-cards-item/collect-cards-item.vue';

export type ScratchEventData = {
  /**
   * 卡片id
   */
  id: number
  /**
   * 卡片名称
   */
  name: string
  /**
   * 卡片图片地址
   */
  icon: string
  /**
   * 领取记录id
   */
  card_log_id: number
}

export interface CollectCardsGroupProps {
  setting: CollectCardsGroupSetting
  /**
   * 当前激活卡片
   */
  current: number
  /**
   * 当前卡片信息
   */
  card: ActivityInfoConfigCollectDrawDetail[number]
  /**
   * 当前选中用户卡片信息
   */
  userCardInfoCurrent?: ComponentWithUserInfoCollectDrawCardList[number]
}

const modules = [Navigation, EffectCoverflow];

const props = withDefaults(defineProps<CollectCardsGroupProps>(), {
  setting: () => defaultConfig(),
  list: () => [],
  card: () => ({
    id: 0,
    name: '',
    icon: '',
    is_limit_card: 0,
  }),
  userCardInfoCurrent: () => ({
    id: 0,
    name: '',
    icon: '',
    is_limit_card: 0,
    receive_log: [],
  }),
});

const emits = defineEmits<{
  (event: 'update:setting', value: CollectCardsGroupSetting): void;
  (event: 'update:current', value: number): void;
  (event: 'scratch', data: ScratchEventData): void;
}>();

const swiperInst = ref<Swiper | null>(null);

const setting = useVModel(props, 'setting', emits);

const activityStore = useActivityStore();
const adminStore = useAdminStore();

const { uploadLog } = useLog();

const interpolation = computed(() => {
  return {
    current: props.current + 1,
    total: props.userCardInfoCurrent?.receive_log.length || 0,
  };
});

const slideStyle = computed<CSSProperties>(() => {
  return {
    width: pxTransform(setting.value.item.width),
    height: pxTransform(setting.value.item.height * 1.1), // 这里 * 1.1 是为了溢出的内容能够显示完整
  };
});

const controlStyle = computed<CSSProperties>(() => {
  return {
    zIndex: 10,
  };
});

const receiveLog = computed(() => {
  // 如果用户卡片信息为空，则返回一个空数组
  const fakeLog = { id: -1 } as ComponentWithUserInfoCollectDrawCardListReceiveLog[number];
  return props.userCardInfoCurrent?.receive_log.length > 0
    ? props.userCardInfoCurrent?.receive_log
    : [fakeLog];
});

const showPagination = computed(() => {
  return adminStore.editable || receiveLog.value.length > 1;
});

// 当 receiveLog 变化时，我们需要等待 DOM 更新后再更新 Swiper
watch(
  () => receiveLog.value,
  () => {
    nextTick(() => {
      if (swiperInst.value) {
        swiperInst.value.update();
      }
    });
  }
);

// 当 userCardInfoCurrent 变化时，我们需要等待 DOM 更新后再更新 Swiper
watch(
  () => props.userCardInfoCurrent?.id,
  () => {
    nextTick(() => {
      if (swiperInst.value) {
        const firstUnclaimedIndex = receiveLog.value.findIndex(item => item.is_receive_gift === 1);
        const targetIndex = firstUnclaimedIndex !== -1 ? firstUnclaimedIndex : 0;
        
        swiperInst.value.slideToLoop(targetIndex, 0);
        emits('update:current', targetIndex);
      }
    });
  }
);

const handleSwiperInit = (swiper: Swiper) => {
  swiperInst.value = swiper;
  
  // 确保 Swiper 初始化后更新到正确位置
  nextTick(() => {
    swiper.update(); // 更新 Swiper 以确保正确计算尺寸
    swiper.slideToLoop(0, 0); // 滑动到第一个循环位置，无动画
  });
};

const handleScratch = (log: ComponentWithUserInfoCollectDrawCardListReceiveLog[number]) => {
  // 登录态拦截
  const pass = activityStore._checkIn();
  if (!pass) {
    return;
  }
  emits('scratch', {
    card_log_id: log.id,  
    id: props.card.id,
    name: props.card.name,
    icon: props.card.icon,
  });
};

const handleActiveIndexChange = (swiper: Swiper) => {
  emits('update:current', swiper.realIndex);
};

const handlePrev = () => {
  swiperInst.value?.slidePrev();
};

const handleNext = () => {
  swiperInst.value?.slideNext();
};
</script>

<style>
.collect-cards-group {
  /* fixbug: 修复在 ios 下，子项只有两项时，子项元素点击事件失效的问题 */
  .swiper-wrapper {
    width: 0;
  }

  .swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .swiper-slide-active {}
}
</style> 
