<!--
* @Description: 基础任务组件
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="task-item"
    :style="{
      backgroundImage: `url(${bgImage})`,
    }"
  >
    <ResizableProvider>
      <!-- 标题 -->
      <UiText v-if="setting.title?.content" v-model:setting="setting.title" />

      <!-- 描述 -->
      <UiText v-if="setting.description?.content" v-model:setting="setting.description" />
      
      <!-- 完成进度 -->
      <UiText
        v-if="setting.process"
        v-model:setting="setting.process"
        :interpolation="processInterpolation"
        :confined="false"
      />

      <!-- 去完成 -->
      <slot name="doBtn" :show="showDoBtn">
        <template v-if="!slots.doBtn && showDoBtn">
          <UiImage
            :setting="setting.doBtn"
            @click="handleDo"
            :confined="false"
          />
        </template>
      </slot>
      <!-- 领取按钮 -->
      <slot name="claimBtn">
        <template v-if="!slots.claimBtn && showClaimBtn">
          <UiImage
            v-if="setting.claimBtn?.enabled"
            :setting="setting.claimBtn"
            @click="handleClaim"
            :confined="false"
          />
        </template>
      </slot>
      <!-- 已完成 -->
      <slot name="achievedBtn">
        <template v-if="!slots.achievedBtn && showAchievedBtn">
          <UiImage
            :setting="setting.achievedBtn"
            :confined="false"
          />
        </template>
      </slot>

      <!-- 自定义内容 -->
      <slot />

      <!-- TODO：新增恭喜获得 -->

      <!-- 刮卡 -->
      <CollectCardsScratch
        v-if="setting.scratch"
        v-model:setting="setting.scratch"
        :show="showScratch"
        :lottery-id="setting.lotteryId"
        :need-bind-role="setting.bindRole"
        :data="scratchData"
        @close="toggleScratch(false)"
        @scratched="handleScratched"
      />
    </ResizableProvider>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'task-item',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, ref, computed, watch, useSlots } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityStore from '@bish/store/src/modules/activity';
import useUserStore from '@bish/store/src/modules/user';
import { postActivityTaskGetAwardTask, postActivityTaskUploadTask } from '@bish/api/src/activity';
import type { ActivityTaskGetAwardTaskData } from '@bish/api/src/activity';
import type { ActivityInfoConfigTask } from '@bish/api/src/activity';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import type { StatefulComponent } from '@bish/types/src/admin';
import type { TaskCollectionCommon } from './index';
import Resizable from '../../../ui/resizable/index.vue';
import ResizableProvider from '../../../ui/resizable/resizable-provider.vue';
import UiImage from '../../../ui/image/index.vue';
import UiText from '../../../ui/text/index.vue';
import CollectCardsScratch from '../../collect-cards/components/collect-cards-scratch/collect-cards-scratch.vue';

export interface TaskCollectionProps {
  /**
   * 配置
   */
  setting: TaskCollectionCommon
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<TaskCollectionProps>(), {
  setting: () => ({} as TaskCollectionCommon),
  task: undefined,
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskCollectionCommon): void;
  (event: 'do'): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const slots = useSlots();

const award = ref<ActivityTaskGetAwardTaskData>();

const activityStore = useActivityStore();
const userStore = useUserStore();

const setting = useVModel(props, 'setting', emits);

const [showScratch, toggleScratch] = useControllableStatus(props, emits, { fieldName: 'showScratch' });

const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

const userTask = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
});

const completed = computed(() => {
  return userTask.value ? userTask.value?.progress_num === userTask.value?.target_num : false;
});

const process = computed(() => {
  return userTask.value?.is_reach_max_draw !== 2
    ? `${userTask.value?.progress_num || 0}/${taskConfig.value?.target_num || 0}`
    : `已达次数上限`;
});

const processInterpolation = computed(() => {
  return {
    process: process.value,
  };
});

const scratchData = computed(() => {
  return {
    name: award.value?.card_name || '',
    icon: award.value?.card_icon || '',
    card_log_id: award.value?.card_log_id || 0,
  };
});

/**
 * 是否显示去完成按钮
 */
const showDoBtn = computed(() => {
  const enabled = setting.value.doBtn?.enabled;
  // 用户未登录，展示领取按钮
  if (!userTask.value && enabled) {
    return true;
  }
  // 配置了进行按钮，并且状态进行中，展示进行按钮
  if (userTask.value?.status === 1 && enabled) {
    return true;
  }
  // 任务已完成，但是未领取，但是没有配置领取按钮，并且没有配置已完成按钮
  if (
    userTask.value?.status === 2 &&
    !setting.value.claimBtn?.enabled &&
    !setting.value.achievedBtn?.enabled
  ) {
    return true;
  }
  // 任务已领取奖励，但是没有配置已领取按钮
  if (userTask.value?.status === 3 && !setting.value.achievedBtn?.enabled) {
    return true;
  }
  return false;
});

/**
 * 是否显示领取按钮
 */
const showClaimBtn = computed(() => {
  // 用户未登录，不展示
  if (!userTask.value) {
    return false;
  }
  // 任务已完成，但是未领取，展示领取按钮
  if (setting.value.claimBtn?.enabled && userTask.value?.status === 2) {
    return true;
  }
  return false;
});

/**
 * 是否显示已完成按钮
 */
const showAchievedBtn = computed(() => {
  // 用户未登录，不展示
  if (!userTask.value) {
    return false;
  }
  // 任务已完成，未领取，但是没有配置领取按钮，并且配置了已完成按钮，展示已完成按钮
  if (
    userTask.value?.status === 2 &&
    !setting.value.claimBtn?.enabled &&
    setting.value.achievedBtn?.enabled
  ) {
    return true;
  }
  if ((userTask.value?.status === 3 || userTask.value?.is_reach_max_draw === 2) && setting.value.achievedBtn?.enabled) {
    return true;
  }
  return false; 
});

const bgImage = computed(() => {
  const activeBgImage = setting.value.activeBgImage;
  // 用户未登录，有配置完成背景图，取完成背景图
  if (!userTask.value && activeBgImage) {
    return activeBgImage;
  }
  if (userTask?.value?.status === 2 && activeBgImage) {
    return activeBgImage;
  }
  // 没有配置完成背景图，取默认背景图
  return userTask?.value?.status !== 3 ? setting.value.bgImage : activeBgImage || setting.value.bgImage;
});

const setLimitLogin = () => {
  if (!setting.value.limitLogin) {
    return;
  }
  if (!userTask.value) {
    return;
  }
  // 用户任务状态为非进行时
  if (userTask.value.status !== 1 && !userStore.limitLogin) {
    userStore.setLimitLogin(1);
  }
};

watch(
  () => userTask?.value,
  () => {
    setLimitLogin();
  },
  {
    immediate: true,
  },
);

const handleDo = async () => {
// 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfig.value) {
    showToast('活动已结束~');
    return;
  }
  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  if (!userTask.value) {
    return;
  }
  const { notAccomplishTip, isFakeTask } = setting.value;

  if (userTask?.value?.status === 1) {
    if (notAccomplishTip) {
      showToast(notAccomplishTip);
    }
    // 伪任务自动完成
    if (isFakeTask === 1) {
      try {
        await postActivityTaskUploadTask({
          act_id: activityStore.activityInfo.init?.id,
          act_acc_id: activityStore.activityAccountInfo.act_acc_id,
          task_code: taskConfig.value.task_code,
          task_id: userTask?.value?.id,
        });
      } catch (error) {
        console.warn('上报任务完成失败', error);
      } finally {
        setTimeout(() => {
          activityStore.getComponentWithUserInfo();
        }, 1000);
      }
    }
  }
  emits('do');
}

const selectTypeMap: Record<number, number> = {
  3: 2,
  4: 2,
  5: 1,
  6: 3,
};

const handleClaim = async () => {
  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfig.value) {
    showToast('活动已结束~');
    return;
  }
  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  if (!userTask.value) {
    return;
  }
  const { receivedRepeatTip, receivedSuccessTip, notAccomplishTip } = setting.value;
  if (userTask?.value?.status === 1) {
    if (notAccomplishTip) {
      showToast(notAccomplishTip);
    }
  }
  if (userTask?.value?.status === 3) {
    if (receivedRepeatTip) {
      showToast(receivedRepeatTip);
    }
    return;
  }
  try {
    // 判断任务是否是已完成状态 & 任务配置了商品id
    // 没有商品id的任务，有可能是为抽奖服务的，这个是不需要领取道具奖励
    if (userTask.value.status === 2 && !hasPrize(taskConfig.value)) {
      return;
    }
    const res = await postActivityTaskGetAwardTask(
      {
        act_id: activityStore.activityInfo.init?.id,
        act_acc_id: activityStore.activityAccountInfo.act_acc_id,
        // 这里处理一下月度礼包，本月如果有领取的话，那么到下个月 taskLogId 是不会进行重置成 0 的
        // 只有状态会重置成 1 进行中（未完成）
        // 所以当状态是 1 的时候，taskLogId 传 0
        task_log_id: userTask.value.status === 1 ? 0 : userTask.value.task_log_id, // 这里跟上边的 未完成任务冲突 了，对于月度活动建议使用 customReceive 复写
        select_type: selectTypeMap[taskConfig.value.reward_type] || 1,
      },
    );
    if (res.code === 0) {
      setTimeout(() => {
        award.value = res.data;
        if (setting.value.congratsMode === 1 || setting.value.congratsMode === 2) {
          showToast(receivedSuccessTip || '领取成功~');
        } else if (setting.value.congratsMode === 3) {
          toggleScratch(true);
        }
        // 重新获取用户与组件的数据
        activityStore.getComponentWithUserInfo();
      }, 500);
    }
  } catch (error) {
    console.warn('领取任务奖励失败', error);
  }
};

/**
 * 是否有奖品的
 */
const hasPrize = (tConfig?: ActivityInfoConfigTask[0]) => {
  if (!tConfig) {
    return false;
  }
  const rewardType = tConfig.reward_type;
  // 虚拟道具
  if (rewardType === 1) {
    // 没有配置道具id，不需要领取任务奖励
    return !!tConfig.item_id
  }
  return true;
};

const handleScratched = () => {
  // TODO: 请求刮奖接口
  console.log('刮奖成功');
};
</script>

<style>
.task-item {
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.task-item-qrCode-content {
  position: relative;
}

.task-item-qrCode-mask {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.8);
}
</style>
