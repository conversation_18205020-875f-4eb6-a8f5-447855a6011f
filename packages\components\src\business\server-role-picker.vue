<!--
* @Description: 区服角色选择器
-->
<template>
  <div class="server-role">
    <UiText
      v-model:setting="setting.selector"
      class="server-role-selector"
    >
      <div
        v-if="!adminStore.editable && !modelValue?.[0]"
        @click="handleSelect"
      >
        {{ placeholder }}
      </div>
      <div
        v-else
        class="server-role-selector-content"
        @click="handleSelect"
      >
        <slot
          :selectedServer="selectedServer"
          :selectedRole="selectedRole"
        >
          <div class="server-role-selector-item">
            {{ `${selectedServer?.text || ''}-${selectedRole?.text || ''}` }}
          </div>
        </slot>
        <slot name="switch">
          <span class="server-role-switch" :style="switchStyle">
            更换
          </span>
        </slot>
      </div>
    </UiText>

    <!-- 区服-角色选择 -->
    <!-- #ifdef WEB -->
    <template v-if="!isWeapp">
      <Teleport to="body">
        <Popup
          :show="open"
          position="bottom"
          @close="handleClose"
        >
          <Picker
            v-model="interValue"
            :columns="columns"
            @confirm="handleConfirm"
            @cancel="handleClose"
          />
        </Popup>
      </Teleport>
    </template>
    <!-- #endif -->
    <template v-if="isWeapp">
      <Popup
        :show="open"
        position="bottom"
        @close="handleClose"
      >
        <Picker
          v-model="interValue"
          :columns="columns"
          @confirm="handleConfirm"
          @cancel="handleClose"
        />
      </Popup>
    </template>
  </div>
</template>

<script lang="ts">
import type { TextSetting } from '../ui/text/index.vue';

export interface ServerRolePickerSetting {
  /**
   * 选择器配置
   */
  selector: TextSetting & {
    /**
     * 更换按钮颜色
     */
    switchColor: string
  }
  /**
   * 是否限制区服，默认 false
   */
  limitServer: boolean
  /**
   * 是否限制是受邀者，勾选是，限制换绑时需要同时满足账号是受邀者
   */
  invitee: boolean
}

export type ServerRolePickerValue = (string | number)[]

export interface ServerRolePickerProps {
  /**
   * 配置
   */
  setting: ServerRolePickerSetting
  /**
   * 值
   */
  modelValue: ServerRolePickerValue
  /**
   * 是否显示
   */
  open: boolean
  /**
   * 占位
   */
  placeholder?: string
  /**
   * 是否从活动账号数据初始默认值，默认 true
   */
  initGlobalRoleInfo?: boolean
}

export const defaultConfig = (): ServerRolePickerSetting => {
  return {
    selector: {
      x: 36,
      y: 82,
      width: 300,
      height: 30,
      fontSize: 14,
      color: '#8F4707',
      align: 'center',
      alignItems: 'center',
      fontWeight: true,
      switchColor: '#8F4707',
    },
    limitServer: false,
    invitee: false,
  };
};

export default {
  name: 'server-role-picker',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, ref, computed, watch } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import Popup from '@bish/ui/src/popup/Popup.vue';
import useUserStore from '@bish/store/src/modules/user';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { pxTransform } from '@bish/utils/src/viewport';
import type { AllRoleInfo } from '@bish/api/src/user';
import { setServerRole, getServerRole } from '@bish/utils/src/storage/modules/server-role-picker';
import { userAgent } from '@bish/utils/src/utils';
import UiText from '../ui/text/index.vue';
import Picker from '../common/picker/Picker.vue';
import type { PickerBaseEvent, PickerOption } from '../common/picker/Picker';

const props = withDefaults(defineProps<ServerRolePickerProps>(), {
  setting: defaultConfig,
  modelValue: () => ([] as ServerRolePickerValue),
  open: false,
  placeholder: '请选择区服-角色',
  initGlobalRoleInfo: true,
});

const emits = defineEmits<{
  (event: 'update:setting', value: ServerRolePickerSetting): void;
  (event: 'update:modelValue', value: ServerRolePickerValue): void;
  (event: 'confirm', value: ServerRolePickerValue, e: PickerBaseEvent): void;
  (event: 'update:open', value: boolean): void;
  (event: 'click'): void;
}>();

const interValue = ref<ServerRolePickerValue>([]);

const userStore = useUserStore();
const activityStore = useActivityStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const { isUserAgentType } = userAgent();
const isWeapp = isUserAgentType === 'WX_MINI'; // 微信小程序环境

const columns = computed(() => {
  const { limitServer, invitee } = props.setting;
  let roleInfo: AllRoleInfo = activityStore.allRoleInfo;

  if (limitServer && !invitee) {
    roleInfo = activityStore.limitServerRole;
  }
  if (limitServer && invitee) {
    if (activityStore.activityAccountInfo.invited_status === 1) {
      roleInfo = activityStore.limitServerRole;
    }
  }
  return _normalizeServerRoleColumns(roleInfo);
});

const selectedServer = computed(() => {
  return columns.value.find(item => item.value === props.modelValue[0]);
});

const selectedRole = computed(() => {
  return selectedServer.value?.children?.find(item => item.value === +props.modelValue[1]);
});

const switchStyle = computed<CSSProperties>(() => {
  const { setting: { selector } } = props;
  return {
    marginLeft: pxTransform(9),
    color: selector.switchColor,
  };
});

watch(
  () => props.modelValue,
  (newVal) => {
    interValue.value = newVal || [];
  },
  { immediate: true },
);

watch(
  () => activityStore.activityAccountInfo,
  () => {
    initialServerRoleValue();
  }
);

// 监听用户退出登录
watch(
  () => userStore.userData.token,
  (newVal, oldVal) => {
    if (!newVal && oldVal) {
      emits('update:modelValue', []);
    }
  }
)

const _normalizeServerRoleColumns = (original: AllRoleInfo) => {
  const ret: PickerOption[] = [];
  if (!original || !original.length) {
    return ret;
  }
  original.forEach(item => {
    const { role_list = [] } = item;
    const server: PickerOption = {
      text: item.server_name,
      value: item.server_id,
      ...item,
      children: [],
    };
    if (role_list && role_list.length) {
      role_list.forEach(roleItem => {
        const role = {
          text: roleItem.role_name,
          value: roleItem.role_id,
          ...roleItem,
        };
        server.children!.push(role);
      })
    }
    ret.push(server);
  })
  return ret;
};

/**
 * 初始数据
 */
const initialServerRoleValue = () => {
  let value: ServerRolePickerValue = [];

  // 没有绑定角色，可能出现在退出登录操作后
  if (!activityStore.bindRoleInfo?.server_name) {
    return value;
  }

  const storageValue = getServerRole<ServerRolePickerValue>();

  if (storageValue) {
    value = storageValue;
  }
  if (props.initGlobalRoleInfo && activityStore.bindRoleInfo?.role_id) {
    const { platform, zone_id } = activityStore.bindRoleInfo;
    const server_id = `${platform}_${zone_id}`;
    value = [server_id, +activityStore.bindRoleInfo.role_id];
  }
  emits('update:modelValue', value);
};

const handleSelect = () => {
  emits('click');
};

const handleClose = () => {
  emits('update:open', false);
};

const handleConfirm = (e: PickerBaseEvent) => {
  const [oldServer, oldRole] = props.modelValue;
  const [newServer, newRole] = e.selectedValues;

  // 选择未改变
  if (oldServer === newServer && `${oldRole}` === `${newRole}`) {
    handleClose();
    return;
  }
  if (!e.selectedValues.length) {
    showToast('请选择区服-角色');
    return;
  }
  const newData = [...e.selectedValues];
  // 结果缓存在本地
  setServerRole(newData);

  handleClose();
  emits('update:modelValue', newData);
  emits('confirm', newData, e);
}
</script>

<style>
.server-role {}

.server-role-selector {
  /* display: flex;
  align-items: center; */
}

.server-role-selector-content {
  display: flex;
  align-items: center;
}

.server-role-switch {
  font-size: 0.95em;
  font-weight: 400;
  text-decoration: underline;
}
</style>
