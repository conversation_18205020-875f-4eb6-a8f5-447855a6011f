<!--
* @Description: 创建/加入队伍任务
-->
<template>
  <TaskItem v-model:setting="setting">
    <!-- 去完成 -->
    <template #doBtn>
      <template v-if="showDoBtn">
        <UiImage
          :setting="setting.doBtn"
          :class="{ 'task-team-join-btn__disabled': !teamConfig }"
          @click="handleDo"
          :confined="false"
        />
      </template>
    </template>
    <!-- 已组队按钮 -->
    <UiImage
      v-if="setting.formedBtn?.enabled && showFormedBtn"
      :setting="setting.formedBtn"
      :confined="false"
    />

    <!-- 队伍人数，同 已组队按钮 一起展示 -->
    <UiText
      v-if="setting.num?.enabled && (currenTeam?.team_id || adminStore.editable)"
      v-model:setting="setting.num"
      :interpolation="numInterpolation"
      :confined="false"
    />
  </TaskItem>
</template>

<script lang="ts">
export default {
  name: 'task-team-join',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { withDefaults, defineProps, defineEmits, computed } from 'vue';
import { useVModel } from '@vueuse/core';
import usePopupStore from '@bish/store/src/modules/popup';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { showToast } from '@bish/ui/src/toast';
import { defaultConfig } from './index';
import type { TaskTeamJoinSetting } from './team-join';
import TaskItem from '../item.vue';
import UiImage from '../../../../ui/image/index.vue';
import UiText from '../../../../ui/text/index.vue';

export interface TeamJoinProps {
  /**
   * 配置
   */
  setting: TaskTeamJoinSetting
}

const props = withDefaults(defineProps<TeamJoinProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: TaskTeamJoinSetting): void;
}>();

const setting = useVModel(props, 'setting', emits);

const activityStore = useActivityStore();
const popupStore = usePopupStore();
const adminStore = useAdminStore();

const taskConfig = computed(() => {
  const { task } = activityStore.activityInfo?.config || {};
  return task?.find(item => item.id === setting.value.taskId);
});

const userTask = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.task?.find(taskItem => taskItem.id === setting.value.taskId);
});

// 队伍信息配置信息
const teamConfig = computed(() => {
  const { team } = activityStore.activityInfo.config || {};
  return team?.find(item => item.id === setting.value.teamId);
});

// 与配置对应的队伍信息
const currenTeam = computed(() => {
  const { component_with_user_info } = activityStore.componentWithUserInfo;
  return component_with_user_info?.team?.find(item => item.id === setting.value.teamId);
});

const showDoBtn = computed(() => {
  // 用户未登录，展示领取按钮
  if (!userTask.value) {
    return true;
  }
  // 存在队伍id
  if (currenTeam.value?.team_id) {
    return false;
  }
  // 配置了进行按钮，并且状态进行中，展示进行按钮
  if (userTask.value?.status === 1) {
    return true;
  }
  return false;
});

const showFormedBtn = computed(() => {
  if (!setting.value.formedBtn?.enabled) {
    return false;
  }
  // 用户未登录，不展示
  if (!userTask.value) {
    return false;
  }
  // 任务已完成，但是没有配置领取按钮，展示已组队按钮
  if (!setting.value.claimBtn?.enabled && userTask.value?.status === 2) {
    return true;
  }
  // 任务已完成，但是没有配置已领取按钮，展示已组队按钮
  if (!setting.value.achievedBtn?.enabled && userTask.value?.status === 3) {
    return true;
  }
  // 存在队伍id
  if ((userTask.value?.status !== 2 && userTask.value?.status !== 3) && currenTeam.value?.team_id) {
    return true;
  }
  return false;
});

const numInterpolation = computed(() => {
  return {
    num: currenTeam.value?.members.length || 0,
  };
});

const handleDo = async () => {
  // 配置找不到
  // 或任务结束了，后端接口是不会返回的
  if (!taskConfig.value || !teamConfig.value) {
    showToast('活动已结束~');
    return;
  }
  
  const pass = activityStore._checkIn(!!setting.value.bindRole);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  popupStore.setShowTeamList(true);
};
</script>

<style>
.task-team-join-btn__disabled {
  filter: grayscale(100%);
}
</style>
