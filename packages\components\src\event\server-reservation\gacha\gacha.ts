import type { CommonSetting } from '@bish/types/src';
import type { TextSetting } from '../../../ui/text/index.vue';
import type { ImageSetting } from '../../../ui/image/index.vue';
import type { PopupSetting } from '../../../common/popup.vue';
import type { DownloadGameSetting } from '../../../business/download-game.vue';
import type { WxSubscribeTemplateList } from '../../../business/we-subscribe-modal';
import type { WxSubscribeGuideSetting } from '../../../business/wx-subscribe-guide';

export type GachaModalSetting = PopupSetting & {
  /**
   * 提示配置
   */
  tip: TextSetting
  /**
   * 海报展示
   */
  poster: ImageSetting
  /**
   * 前往游戏按钮
   */
  visitGame: ImageSetting & {
    /**
     * 点击提示
     */
    tip: string
  }
  /**
   * 下载游戏按钮
   */
  downloadGame: DownloadGameSetting
}

export interface GachaSetting extends CommonSetting {
  /**
   * 道具展示
   */
  items: ImageSetting
  /**
   * 预约按钮
   */
  bookBtn: ImageSetting
  /**
   * 抽卡按钮
   */
  gachaBtn: ImageSetting
  /**
   * 抽卡弹窗
   */
  gachaModal: GachaModalSetting
  /**
   * 是否在微信小程序环境中运行，1是 0否
   */
  // weapp: number
  /**
   * 微信消息订阅模板
   */
  wxSubscribeTemp: WxSubscribeTemplateList
  /**
   * 微信小程序订阅提示弹窗
   */
  wxSubscribeGuide: WxSubscribeGuideSetting
}