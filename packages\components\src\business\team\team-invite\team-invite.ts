import type { CommonSetting } from '@bish/types/src';
import type { ImageSetting } from '../../../ui/image/index.vue'
import type { InviteModalProSetting } from '../../invite-modal-pro/invite-modal-pro';
import type { InvitationModalSetting } from '../../invitation-modal.vue';
import type { ShareGuideSetting } from '../../share-guide';
import type { PopupSetting } from '../../../common/popup.vue'
import type { TextSetting } from '../../../ui/text/index.vue'

export interface TeamInviteSetting extends CommonSetting {
  /**
   * 队伍id
   */
  teamId: number
  /**
   * 邀请按钮
   */
  inviteBtn: ImageSetting
  /**
   * 邀请好友组队弹窗
   */
  inviteCard: InviteModalProSetting
  /**
   * 接受好友邀请弹窗
   */
  invitationCard: InvitationModalSetting
  /**
   * 已满员
   */
  fullBtn: ImageSetting
  /**
   * 分享引导，WEB端使用，微信小程序直接进行分享
   */
  shareGuide?: ShareGuideSetting
  /**
   * 入队失败窗配置
   */
  joinFailedModal: PopupSetting & {
    /**
     * 提示文案
     */
    content: TextSetting
  }
}