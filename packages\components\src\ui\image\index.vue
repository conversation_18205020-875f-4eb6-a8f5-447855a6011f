<template>
  <Resizable
    v-bind="attrs"
    v-model:x="vSetting.x"
    v-model:y="vSetting.y"
    v-model:width="vSetting.width"
    v-model:height="vSetting.height"
    :id="vSetting.cid"
    :style="{ fontSize: 0 }"
    :position="vSetting.position"
    @click="(e) => emits('click', e)"
    aspect-ratio-enabled
  >
    <Link
      class="ui-image"
      :class="{ 'ui-image-affix': vSetting.affix && !adminStore.editable }"
      :href="vSetting.href"
      :target="vSetting.target"
      :isLimit="isLimit"
      @click="handleClick"
    >
      <img
        v-if="vSetting.imgLink"
        :src="vSetting.imgLink"
        :alt="vSetting.imgAlt"
        :style="imgStyle"
        :show-menu-by-longpress="providedShowMenuByLongpress"
      />
      <div v-else class="ui-image-empty" :style="imgStyle">
        <IconEmpty />
      </div>

      <div v-if="adminStore.editable" class="ui-image-quick" @click.stop="handleQuickEdit">
        <div class="ui-image-quick-content">
          <IconPicture />
        </div>
      </div>
    </Link>
  </Resizable>
</template>

<script lang="ts" setup>
import { computed, useAttrs } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import { pxTransform } from '@bish/utils/src/viewport';
import useAdminStore from '@bish/store/src/modules/admin';
import { useLog } from '@bish/hooks/src/useLog';
import { useUserAgent } from '@bish/hooks/src/useUserAgent';
import { QUICK_EVENT, quickEvent } from '../../__utils/quick-event';
import Resizable from '../resizable/index.vue';
import type { ResizableProps } from '../resizable/index.vue';
import Link from '../link/index.vue';
import type { LinkProps } from '../link/index.vue';
import IconEmpty from './icons/icon-empty.vue';
import IconPicture from './icons/icon-picture.vue';

export type ImageSetting = ResizableProps & LinkProps & {
  /**
   * 图片地址
   */
  imgLink?: string
  /**
   * 图片描述
   */
  imgAlt?: string
  /**
   * 是否固定在页面，默认 false，设置后将使用 fixed 布局
   */
  affix?: boolean
  /**
   * 事件上报点击id
   */
  click_id?: number
  /**
   * 事件上报点击类型
   */
  click_type?: number
  /**
   * 环境限制，默认不限制，值为需要限制的环境
   */
  browser_limit?: string[]
  /**
   * 提示文案
   */
  tip?: string
  /**
   * 长按图片显示发送给朋友，微信小程序属性，默认 0
   */
  showMenuByLongpress?: number
}

export interface ImageProps {
  /**
   * 配置
   */
  setting: ImageSetting
  /**
   * 长按图片显示发送给朋友，微信小程序属性，默认 false
   * 注意该属性准备废弃，请走 setting.showMenuByLongpress
   */
  showMenuByLongpress?: boolean

  [key: string]: any
}

const props = withDefaults(defineProps<ImageProps>(), {
  setting: () => ({} as ImageSetting),
  showMenuByLongpress: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: ImageSetting): void;
  (event: 'quickImg', value: ImageSetting, fieldName: string): void;
  (event: 'click', value: MouseEvent): void;
}>();

const attrs = useAttrs();
const vSetting = useVModel(props, 'setting', emits);

const adminStore = useAdminStore();

const { uploadLog } = useLog();
const { isUserAgentType } = useUserAgent();

const imgStyle = computed(() => {
  return {
    width: `${pxTransform(props.setting.width as number)}`,
    height: `${pxTransform(props.setting.height as number)}`,
  }
});

const isLimit = computed(() => {
  const limit = props.setting?.browser_limit || []
  return limit.includes(isUserAgentType)
})

const providedShowMenuByLongpress = computed(() => {
  return props.showMenuByLongpress ? 1 : vSetting.value.showMenuByLongpress || 0;
});

const handleQuickEdit = () => {
  const event = quickEvent(
    QUICK_EVENT.IMG,
    {
      callback(setting) {
        vSetting.value.imgLink = setting.imgLink;
      },
      value: {
        imgLink: vSetting.value.imgLink,
      }
    },
  );
  document.dispatchEvent(event);
};

const handleClick = () => {
  // 点击事件上报
  if (props.setting.click_id && !adminStore.editable) {
    let clickType = 3
    if (!props.setting.click_type) {
    // 4: 链接 3: 按钮
      clickType = props.setting.href ? 4 : 3;
    } else {
      clickType = props.setting.click_type
    }
    uploadLog({
      click_id: props.setting.click_id,
      click_type: clickType,
      event_name: 'click',
    });
  }
  // 跳转事件环境限制
  const limit = props.setting?.browser_limit || []
  if (limit.length > 0) {
    if (isLimit.value) {
      if (props.setting.tip) {
        showToast(props.setting.tip);
      }
    }
    return
  }
  if (props.setting.tip) {
    showToast(props.setting.tip);
  }
}; 
</script>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ui-image',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
});
</script>

<style>
.ui-image {}

.ui-image img {
  user-drag: none;
  user-select: none;
}

.ui-image-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('./icons/transparent.png');
}

.ui-image-empty > .svg-icon {
  width: auto;
  height: 50%;
}

.ui-image:hover .ui-image-quick {
  display: block;
}

.ui-image-quick {
  display: none;
  padding: 6px;
  position: absolute;
  left: -6px;
  top: -34px;
}

.ui-image-quick-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  cursor: pointer;
}

.ui-image-quick-content > .svg-icon {
  width: 60%;
  height: auto;
}

.ui-image-affix {
  position: fixed;
  z-index: 98;
}
</style>
