<!--
* @Description: 预约抽卡，目前好像只有闪烁之光用到这个组件
-->
<template>
  <Resizable
    class="gacha"
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
  >
    <!-- 道具展示 -->
    <UiImage v-if="setting.items?.imgLink" v-model:setting="setting.items" />

    <!-- 立即预约按钮 -->
    <template v-if="showBookBtn">
      <UiImage
        v-if="setting.bookBtn?.imgLink"
        v-model:setting="setting.bookBtn"
        @click="handleBook"
      />
    </template>
    <!-- 立即抽卡按钮 -->
    <template v-else>
      <UiImage
        v-if="setting.gachaBtn?.imgLink"
        v-model:setting="setting.gachaBtn"
        @click="handleDraw"
      />
    </template>

    <!-- 抽卡弹窗 -->
    <Popup
      z-index="99"
      :show="open"
      :lock-scroll="false"
      v-model:setting="setting.gachaModal"
      @close="handleClose"
    >
      <!-- 提示文案 -->
      <UiText
        v-if="setting.gachaModal.tip?.content"
        v-model:setting="setting.gachaModal.tip"
        class="book-confirmed-content"
      />
      <!-- 海报展示 -->
      <UiImage v-if="setting.gachaModal.poster?.imgLink" v-model:setting="setting.gachaModal.poster" />

      <!-- 前往游戏按钮 -->
      <UiImage
        v-if="setting.gachaModal.visitGame?.imgLink"
        v-model:setting="setting.gachaModal.visitGame"
        @click="handleVisitGame"
      />
      <!-- 下载游戏按钮 -->
      <DownloadGame
        v-if="setting.gachaModal.downloadGame?.imgLink"
        v-model:setting="setting.gachaModal.downloadGame"
        :exposureType="1"
      />
    </Popup>
    
    <!-- 微信小程序订阅提示弹窗 -->
    <WxSubscribeGuide
      v-if="setting.wxSubscribeGuide"
      :show="showOpenSubscribeMessage"
      v-model:setting="setting.wxSubscribeGuide"
      @close="handleCloseSubscribeMessage"
      @ok="handleOpenSetting"
    />

    <!-- 总是保持以上选择，不再询问 -->
    <WxSubscribeCheck v-model:show="showGuideSubscription" />
  </Resizable>
</template>

<script lang="ts">
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'gacha',
  options: {
    styleIsolation: 'shared',
    virtualHost: true,
  },
}
</script>

<script lang="ts" setup>
import { computed, watch, onMounted } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useActivityStore from '@bish/store/src/modules/activity';
import useUserStore from '@bish/store/src/modules/user';
import useAdminStore from '@bish/store/src/modules/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import type { StatefulComponent } from '@bish/types/src/admin';
import { useLog } from '@bish/hooks/src/useLog';
import useWxSubscribeMessage from '@bish/hooks/src/business/useWxSubscribeMessage';
import { sleep } from '@bish/utils/src/utils';
import UiText from '../../../ui/text/index.vue';
import UiImage from '../../../ui/image/index.vue';
import Resizable from '../../..//ui/resizable/index.vue';
import Popup from '../../../common/popup.vue';
import WxSubscribeGuide from '../../../business/wx-subscribe-guide/wx-subscribe-guide.vue';
import WxSubscribeCheck from '../../../common/wx-subscribe-check/wx-subscribe-check.vue';
import DownloadGame from '../../../business/download-game.vue';
import { defaultConfig } from './index';
import type { GachaSetting } from './gacha';

export interface GachaProps {
  /**
   * 配置
   */
  setting: GachaSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

const props = withDefaults(defineProps<GachaProps>(), {
  setting: () => defaultConfig(),
});

const emits = defineEmits<{
  (event: 'update:setting', value: GachaSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const activityPageStore = useActivityPageStore();
const activityStore = useActivityStore();
const userStore = useUserStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);
const [open, toggle] = useControllableStatus(props, emits, { fieldName: 'showGacha' });

const { uploadLog } = useLog();

const submitClock = async () => {
  await updateSubscriptionCount(
    {
      act_id: activityPageStore.activityPageConfig.activity_id!,
      act_acc_id: activityStore.activityAccountInfo.act_acc_id,
    },
    setting.value.wxSubscribeTemp,
  );
  await sleep(2000);
  activityStore.getComponentWithUserInfo();
};

const {
  showGuideSubscription,
  showOpenSubscribeMessage,
  setClock,
  guidSubscribeMessageAuthAfter,
  subscribed,
  fetchRemindStatus,
  updateSubscriptionCount,
  resetSubscribeState,
  toggleOpenSubscribeMessage,
} = useWxSubscribeMessage(
  submitClock,
  [props, emits, { fieldName: 'showOpenSubscribeMessage' }],
);

const showBookBtn = computed(() => {
  return !activityStore.activityAccountInfo.act_acc_id  || !subscribed.value;
});

watch(
  () => activityStore.activityAccountInfo,
  (newVal) => {
    if (newVal.act_acc_id) {
      fetchMiniRemindStatus();
    } else {
      resetSubscribeState();
    }
  },
);

onMounted(() => {
  userStore.acceptInviteScheduler.add(() => {
    toggle(true);
  });
});

const fetchMiniRemindStatus = async () => {
  const { activityAccountInfo } = activityStore;
  const templateList = setting.value.wxSubscribeTemp || [];
  const remind_template_id = templateList.map(item => item.remindTemplateId).join(',');
  fetchRemindStatus({
    act_id: activityPageStore.activityPageConfig.activity_id!,
    act_acc_id: activityAccountInfo.act_acc_id,
    remind_template_id,
  });
};

/**
 * 立即预约
 */
const handleBook = () => {
  // 只能通过状态切换显示弹窗
  if (adminStore.editable) {
    return;
  }

  // 事件上报--预约人数
  uploadLog({
    event_name: 'click',
    click_id: 17,
    click_type: 3,
  });
  const pass = activityStore._checkIn(false);
  if (!pass) {
    return;
  }
  if (activityStore.isSummit()) {
    return;
  }
  // 没有订阅次数，并且开启提醒，才调起微信订阅消息
  // 防止用户一直订阅消息
  if (!subscribed.value) {
    setClock(setting.value.wxSubscribeTemp);
  } else {
    showToast('已订阅，无需重复操作');
  }
};

/**
 * 立即抽卡
 */
const handleDraw = () => {
  // 事件上报--抽卡
  uploadLog({
    event_name: 'click',
    click_id: 48,
    click_type: 3,
  });
  toggle(true);
};

const handleVisitGame = () => {
  // 事件上报--点击进入游戏人数
  uploadLog({
    event_name: 'click',
    click_id: 7,
    click_type: 3,
  });
  showToast(setting.value.gachaModal.visitGame?.tip || '请前往游戏登录参与预约抽卡~');
};

const handleClose = () => {
  toggle(false);
};

const handleCloseSubscribeMessage = () => {
  toggleOpenSubscribeMessage(false);
};

const handleOpenSetting = () => {
  handleCloseSubscribeMessage();
  uni.openSetting({
    success() {
      guidSubscribeMessageAuthAfter(setting.value.wxSubscribeTemp);
    },
  });
};
</script>
