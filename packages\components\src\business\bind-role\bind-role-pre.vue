<template>
  <ServerRolePicker
    v-if="show"
    v-model="serverRole"
    :open="openBindRoleModal"
    :role="activityStore.bindRoleInfo"
    :server-options="serverOptions"
    :role-options="roleOptions"
    @confirm="handleConfirm"
    @update:open="handleOpenChange"
  />
</template>

<script lang="ts">
import type { AllRoleInfoRoleList } from '@bish/api/src/user';
import type { ServerRoleSetting, ServerRoleValue, ServerRoleOptions } from './server-role-picker';
import { defaultConfig as serverRoleDefaultConfig } from './server-role-picker.vue';


export interface BindRolePreSetting extends ServerRoleSetting {}

export interface BindRolePreProps {
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): BindRolePreSetting => {
  const serverRoleDefault = serverRoleDefaultConfig();
  return {
    ...serverRoleDefault,
    modalSetting: {
      ...serverRoleDefault.modalSetting,
      cancelBtn: {
        x: 0,
        y: 0,
        width: 141,
        height: 39,
        imgLink: '',
      },
      closeBtn: {
        x: 320,
        y: -37,
        width: 35,
        height: 35,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719403274633_.png',
      },
      okBtn: {
        x: 119,
        y: 260,
        width: 141,
        height: 39,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-6-26/1719396520604_.png',
      },
      form: {
        ...serverRoleDefault.modalSetting.form,
        label: {
          show: true,
          color: '#1A2454',
        },
      },
    },
  }
};

export default {
  name: 'bind-role-pre',
}
</script>

<script lang="ts" setup>
import { ref, computed, watch, watchEffect } from 'vue';
import useActivityPageStore from '@bish/store/src/modules/activityPage';
import useActivityStore from '@bish/store/src/modules/activity';
import useUserStore from '@bish/store/src/modules/user';
import useReservationStore from '@bish/store/src/modules/reservation';
import usePopupStore from '@bish/store/src/modules/popup';
import useAdminStore from '@bish/store/src/modules/admin';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import type { StatefulComponent } from '@bish/types/src/admin';
import ServerRolePicker from './server-role-picker.vue';


const props = withDefaults(defineProps<BindRolePreProps>(), {});

const emits = defineEmits<{
  (event: 'update:status', value: StatefulComponent['status']): void;
}>();

const serverRole = ref<ServerRoleValue>({
  server: [],
  role: [],
});

const activityPageStore = useActivityPageStore();
const activityStore = useActivityStore();
const userStore = useUserStore();
const reservationStore = useReservationStore();
const popupStore = usePopupStore();
const adminStore = useAdminStore();

const [openBindRoleModal, toggleOpenBindRoleModal] = useControllableStatus(props, emits, { fieldName: 'showBindRoleModal' });

// 区服选项
const serverOptions = computed<ServerRoleOptions>(() => {
    return activityStore.bindRoleInfo.server_name
      ? [
          {
            text: activityStore.bindRoleInfo.server_name,
            value: `${activityStore.bindRoleInfo.platform}_${activityStore.bindRoleInfo.zone_id}`,
          },
        ]
      : []
  })

// 角色选项
const roleOptions = computed<ServerRoleOptions>(
  () =>
    reservationStore.preRoleList.map((item: any) => ({
      text: item.role_name,
      value: item.role_id,
    }))
)

// 是否显示绑定角色
const show = computed(() => {
  // 这里后台组件配置特殊逻辑
  if (adminStore.editable) {
    return true;
  }
  // 未登录
  if (!userStore.isLogined) {
    return false;
  }
  // 开服时间的判断基于已获取到活动初始化数据
  if (!activityStore.activityInfo.init) {
    return false;
  }
  // 配置了开服时间、未开服、未绑角
  if (!activityStore.getServerOpenState() && !activityStore.activityAccountInfo?.role_info) {
    return false;
  }
  return true;
})

watch(
  () => popupStore.showServerRole,
  (newVal) => {
    toggleOpenBindRoleModal(newVal);
  },
);

watch(
  () => activityStore.bindRoleInfo,
  (newVal) => {
    if (newVal) {
      serverRole.value.server = newVal.server_name ? [`${newVal.platform}_${newVal.zone_id}`] : [];
      // 这里玩家角色列表返回的 role_id 是 number 类型，获取活动账号信息返回的 role_info 中的 role_id 又是 string 类型
      // 所以在这统一一下类型
      serverRole.value.role = newVal.role_id ? [+newVal.role_id] : [];
    }
  },
  { immediate: true },
);

watchEffect(() => {
  if (activityStore.activityAccountInfo.act_acc_id) {
    // 用户已登录状态下才获取
    if (activityStore.activityInfo.config?.preCreateRole?.zone_id) {
      reservationStore.fetchPreRoleList();
    }
  }
});

const handleOpenChange = (value: boolean) => {
  if (!value) {
    // 同步数据
    if (openBindRoleModal && !popupStore.showServerRole) {
      toggleOpenBindRoleModal(false);
    }
  }
  popupStore.setShowServerRole(value);
};

const handleConfirm = () => {
  // 因为预约创角只能够创建一个角色，也就是说其实不能进行换绑操作，没有意义
  popupStore.setShowServerRole(false);
};

</script>

<style lang="less">
.bind-role-pre {}
</style>
