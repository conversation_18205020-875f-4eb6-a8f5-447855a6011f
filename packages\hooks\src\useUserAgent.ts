
export enum UserAgent {
  MOBILE = 'H5',
  PC = 'PC',
  WECHATWEB = 'WECHATWEB',
  WX_MINI = 'WX_MINI',
  WX_WEBVIEW = 'WX_WEBVIEW',
}

/**
 * @description: 判断设备
 * @return {*}
 */
export function useUserAgent() {
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator?.userAgent.toLowerCase())
  const isWechatWeb = /^((?!wxwork).)*micromessenger.*$/i.test(navigator?.userAgent.toLowerCase()) // 是否是微信浏览器(但不能是企微)

  // 判断是否在微信小程序环境中
  const isWechatMiniProgram = typeof wx !== 'undefined' && !!wx.getSystemInfoSync && !window
  // 判断是否在微信小程序 webview 环境
  const isWechatWebview = isWechatWeb && navigator?.userAgent.toLowerCase().indexOf('miniprogram') > -1

  let isUserAgentType: UserAgent
  if (isWechatWebview) {
    isUserAgentType = UserAgent.WX_WEBVIEW
  } else if (isWechatMiniProgram) {
    isUserAgentType = UserAgent.WX_MINI
  } else if (isWechatWeb) {
    isUserAgentType = UserAgent.WECHATWEB
  } else if (isMobile) {
    isUserAgentType = UserAgent.MOBILE
  } else {
    isUserAgentType = UserAgent.PC
  }

  return {
    isUserAgentType,
  }
}
