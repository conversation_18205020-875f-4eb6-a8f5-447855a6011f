import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'
import type { ComponentWithUserInfoMembers } from '../activity'

const { VITE_ACTIVITY_URL, VITE_WECHAT_URL } = useEnv()

/**
 * @description: 抽奖
 * @param {object} data
 * @return {*}
 */
export function drawPrize(data: object) {
  return http.post(`${VITE_ACTIVITY_URL}/lottery/prize/draw`, data)
}

export type CreateTeamParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 	队伍配置id
   */
  team_config_id: number
  /**
   * 区服id
   */
  server_id?: string
  /**
   * 	区服名称
   */
  server_name?: string
  /**
   * 角色id
   */
  role_id?: number
  /**
   * 角色等级
   */
  role_level?: number
  /**
   * 角色名称
   */
  role_name?: string
  /**
   * 队名
   */
  team_name?: string
  /**
   * 队伍宣言
   */
  team_slogan?: string
  /**
   * 赛段 根据角色等级进行枚举 1为68-69 2为85-89
   */
  stage?: number
  /**
   * 性别，0是女，1是男
   */
  sex?: number
  /**
   * 门派
   */
  career?: number
}

/**
 * @description: 创建队伍
 * @param {object} data
 * @return {*}
 */
export function createTeam(data: CreateTeamParams) {
  return http.post(`${VITE_ACTIVITY_URL}/team/create`, data)
}

export type JoinTeamParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 	队伍配置id
   */
  team_id: number
  /**
   * 邀请人账号account_id
   */
  inviter: string
  /**
   * 区服id
   */
  server_id?: string
  /**
   * 	区服名称
   */
  server_name?: string
  /**
   * 角色id
   */
  role_id?: number
  /**
   * 角色等级
   */
  role_level?: number
  /**
   * 角色名称
   */
  role_name?: string
  /**
   * 性别，0是女，1是男
   */
  sex?: number
  /**
   * 门派
   */
  career?: number
}

/**
 * @description: 加入队伍
 * @param {object} data
 * @param {boolean} errToast 是否显示错误提示，默认 true
 * @return {*}
 */
export function joinTeam(data: JoinTeamParams, errToast = true) {
  return http.post(`${VITE_ACTIVITY_URL}/team/join`, data, { errToast })
}

/**
 * @description: 微信分享
 * @param {object} data
 * @return {*}
 */
export function getWxShare(data: object) {
  return http.post(`${VITE_WECHAT_URL}/wechat/share`, data)
}

export type TeamInviterParams = {
  /**
   * 活动id
   */
  act_id: number
  /**
   * 邀请码
   */
  invite_code: string
}

export type TeamInviterData = {
  /**
   * 邀请人账号
   */
  account_id: string
  /**
   * 邀请人角色名
   */
  role_name: string
}

/**
 * 邀请码查询邀请信息
 * @param params GameTeamListParams
 * @returns 
 */
export function postTeamInviter(params: TeamInviterParams) {
  return http.post<TeamInviterData>(`${VITE_ACTIVITY_URL}/team/get_inviter`, params)
}

export type TeamDisbandParams = {
  /**
   * 活动id
   */
  act_id: number
  /**
   * 组队配置ID
   */
  team_config_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 队伍ID
   */
  team_id: number
}

/**
 * 解散常规队伍
 * @param params TeamDisbandParams
 * @returns 
 */
export function postTeamDisband(params: TeamDisbandParams) {
  return http.post(`${VITE_ACTIVITY_URL}/team/disband`, params)
}

export type TeamListParams = {
  /**
   * 活动id
   */
  act_id: number
  /**
   * 活动用户id
   */
  act_acc_id: string
  /**
   * 组队配置ID
   */
  team_config_id: number
  /**
   * 页数
   */
  page: number
  /**
   * 每页数量
   */
  page_rows: number
}

export type TeamListData = {
  /**
   * 队伍id
   */
  id: number
  /**
   * 队伍名
   */
  team_name: string
  /**
   * 队伍状态 1正常 2解散 3满员
   */
  team_status: number
  /**
   * 当前队员人数
   */
  current_joined_number: number
  /**
   * 队员人数总数
   */
  team_count_number: number
  /**
   * 是否是是队员，1为否 2为是
   */
  is_join: number
  /**
   * 队伍信息存放
   */
  team_list: ComponentWithUserInfoMembers
}[]

/**
 * 常规队伍列表
 * @param params TeamListParams
 * @returns 
 */
export function postTeamList(params: TeamListParams) {
  return http.post<{ list: TeamListData, count: number }>(`${VITE_ACTIVITY_URL}/team/team_list`, params)
}
