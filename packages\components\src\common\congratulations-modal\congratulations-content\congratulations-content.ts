import type { CommonSetting } from '@bish/types/src';
import type { TextSetting } from '../../../ui/text/index.vue';
import type { ImageSetting } from '../../../ui/image/index.vue';

export interface CongratulationsContentSetting {
  /**
   * 奖品图片
   */
  prizeImg: CommonSetting & {
    /**
     * 是否显示，默认 true
     */
    show?: boolean
  }
  /**
   * 奖品名称
   */
  prizeName: TextSetting
  /**
   * 提示
   */
  tip: TextSetting
  /**
   * 游戏道具提示
   */
  gameItemTip: string
  /**
   * 红包提示
   */
  redPacketTip: string
  /**
   * 其他提示
   */
  otherTip: string
  /**
   * 默认按钮
   */
  okBtn?: ImageSetting
  /**
   * 填写地址按钮
   */
  addressBtn?: ImageSetting
  /**
   * 立即提现按钮
   */
  withdrawBtn?: ImageSetting
}