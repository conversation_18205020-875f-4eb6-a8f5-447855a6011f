import { onMounted, onUnmounted } from "vue"
import type { TextOwnerSetting } from "../ui/text/index.vue"

export type QuickEventDetail<T = Object> = {
  /**
   * 回调函数
   */
  callback: (setting: T) => void
  /**
   * 当前值
   */
  value: T

  [key: string]: any
}

export type ImgQuickSetting = {
  /**
   * 图片地址
   */
  imgLink?: string
}

export const enum QUICK_EVENT {
  'IMG' = 'quick-img',
  'TEXT' = 'quick-text',
}

export interface QuickEventMap {
  [QUICK_EVENT.IMG]: ImgQuickSetting;
  [QUICK_EVENT.TEXT]: TextOwnerSetting;
}

export function quickEvent<K extends keyof QuickEventMap>(eventName: K, detail: QuickEventDetail<QuickEventMap[K]>) {
  return new CustomEvent(eventName, {
    detail,
  });
}

export function useQuickEventListener<K extends keyof QuickEventMap>(eventName: K, callback: (event: CustomEvent<QuickEventDetail<QuickEventMap[K]>>) => void) {
  onMounted(() => {
    document.addEventListener(eventName as string, callback as EventListenerOrEventListenerObject)
  })
  onUnmounted(() => {
    document.removeEventListener(eventName as string, callback as EventListenerOrEventListenerObject)
  })
}

