import type { CommonSetting } from '@bish/types/src';
import type { PopupSetting } from '../../common/popup.vue';
import type { TextSetting } from '../../ui/text/index.vue';
import type { ImageSetting } from '../../ui/image/index.vue';
import type { ResizableProps } from '../../ui/resizable/index.vue';
 
export type LogType = 'gift' | 'receive'

export interface GiftCollectionRecordSetting extends CommonSetting {
  tips: TextSetting
  bgImage: string
  modal: LogModalSetting
  content: CommonSetting & {
    text: TextSetting
  }
} 

type LogModalSetting = {
  modalSetting: PopupSetting // 弹窗基础配置
  logType: Array<LogType> // 记录类型[赠送, 收取]
  gift?: { // 赠送记录的tab配置
    position: ResizableProps
    bg: string // 默认背景
    activeBg: string // 选中时背景
  }
  receive?: LogModalSetting['gift'] // 收取记录的tab的配置
}