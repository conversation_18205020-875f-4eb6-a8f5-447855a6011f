import { http } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'

const { VITE_ACTIVITY_URL } = useEnv()

export type LotteryPrizeDrawParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，登录状态需要
   */
  act_acc_id: string
  /**
   * 抽奖组件id(红包id)，数据来源于 用户与组件产生的信息 接口的 lottery 数组中的id
   */
  component_config_id: string
  /**
   * 抽奖类型： 1普通抽奖、2刮卡抽奖
   */
  lottery_type?: number
  /**
   * 用户集卡记录id
   */
  card_log_id?: number
}

export type LotteryPrizeDrawData = {
  /**
   * 奖品id
   */
  id: number
  /**
   * 奖品名称
   */
  name: string
  /**
   * 奖品icon
   */
  icon: string
  /**
   * 奖品类型 1: 实物 2: 游戏道具 3: 序列号 4:积分 5:红包 6:测试资格 7:谢谢参与
   */
  type: number
  /**
   * 奖品序列号, 如果是其他类型默认为空字符串
   */
  serial: string
}

/**
 * @description 点击抽奖
 * @param {number} params.act_id 活动ID
 * @param {string} params.act_acc_id 活动系统加密的账号id，登录状态需要
 * @param {number} params.component_config_id 抽奖组件id(红包id)，数据来源于 用户与组件产生的信息 接口的 lottery 数组中的id
 * @returns
 */
export function postLotteryPrizeDraw(params: LotteryPrizeDrawParams) {
  const { act_id, act_acc_id, component_config_id, lottery_type, card_log_id } = params
  return http.post<LotteryPrizeDrawData>(`${VITE_ACTIVITY_URL}/lottery/prize/draw`, {
    act_id: `${act_id}`,
    act_acc_id: act_acc_id,
    component_config_id: `${component_config_id}`,
    lottery_type: `${lottery_type}`,
    card_log_id: `${card_log_id}`,
  })
}


export type LotterySlideParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 轮播数量
   */
  page_size: number
}

export type LotterySlideData = {
  /**
   * 中奖时间
   */
  created_at: string
  /**
   * 奖励名
   */
  item_name: string
  /**
   * 中奖人信息
   */
  account: {
    /**
     * 无
     */
    wx_openid: string
    /**
     * 手机号码
     */
    phone_number: string
    /**
     * 头像
     */
    portrait: string
    /**
     * 无
     */
    client_extra: string
  }
  /**
   * 账号ID
   */
  account_id: string
}[]

/**
 * @description 获取中奖轮播列表
 * @param {number} params.act_id 活动ID
 * @param {string} params.act_acc_id 活动系统加密的账号id，登录状态需要
 * @param {number} params.component_config_id 抽奖组件id(红包id)，数据来源于 用户与组件产生的信息 接口的 lottery 数组中的id
 * @returns
 */
export function postLotterySlide(params: LotterySlideParams) {
  return http.post<LotterySlideData>(`${VITE_ACTIVITY_URL}/lottery/slide`, params)
}

export type LotteryRankParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 活动系统加密的账号id，登录状态需要
   */
  act_acc_id?: string
  /**
   * 抽奖配置ID
   */
  component_config_id: number
  /**
   * 页数
   */
  page: number
  /**
   * 页数
   */
  per_page: number
}

export type LotteryRankData = {
  /**
   * 我的抽奖次数
   */
  my_num: number
  /**
   * 我的排名 0代表没排名
   */
  my_rank: number
  /**
   * 中奖人信息
   */
  top_accounts: {
    /**
     * 账号ID
     */
    account_id: string
    /**
     * 抽奖次数
     */
    sum: number
    /**
     * 角色id
     */
    role_id: string
    /**
     * 角色名
     */
    role_name: string
    /**
     * 平台
     */
    platform: string
    /**
     * 区服id
     */
    zone_id: string
    /**
     * 区服
     */
    server_name: string
    /**
     * 性别
     */
    sex: number
    /**
     * 门派
     */
    career: number
    /**
     * 排名
     */
    rank: number
  }[]
}

/**
 * @description 抽奖次数排行榜
 * @returns LotteryRankData
 */
export function postLotteryRank(params: LotteryRankParams) {
  return http.post<LotteryRankData>(`${VITE_ACTIVITY_URL}/lottery/rank`, params)
}


export type LotteryPrizeWinnersParams = {
  /**
   * 活动ID
   */
  act_id: number
  /**
   * 抽奖配置ID
   */
  component_config_id: number
}

export type LotteryPrizeWinnersData = {
  /**
   * 奖励名
   */
  name: string
  /**
   * 奖励类型 1: 实物 2:道具 3: 序列号 4: 积分 5:红包
   */
  type: number
  /**
   * 等级 0为常规 1为一等奖 2为二等奖
   */
  grade: number
  /**
   * 奖励icon
   */
  icon: string
  /**
   * 中奖时间
   */
  open_at: string
  /**
   * 角色ID
   */
  role_id: string
  /**
   * 角色名
   */
  role_name: string
}[]

/**
 * @description 获取中奖名单
 * @returns LotteryRankData
 */
export function postLotteryPrizeWinners(params: LotteryPrizeWinnersParams) {
  return http.post<LotteryPrizeWinnersData>(`${VITE_ACTIVITY_URL}/lottery/prize/grade_winners`, params)
}