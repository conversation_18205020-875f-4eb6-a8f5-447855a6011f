<template>
  <div
    class="popup"
    @click.stop
    @touchmove="handleStopPropagation"
    @mousedown="handleStopPropagation"
  >
    <Popup
      :z-index="99"
      :lock-scroll="false"
      :position="providedPosition"
      @open="handleOpen"
      @closed="handleClosed"
      v-bind="attrs"
    >
      <ResizableProvider>
        <Resizable
          class="popup-content"
          v-model:width="vSetting.width"
          v-model:height="vSetting.height"
          :movable="false"
          :baseline="false"
          :resizable="resizable"
          :style="{
            backgroundImage: `url(${vSetting.bgImage})`,
            ...(contentStyle || {}),
          }"
        >
          <!-- 弹窗标题 -->
          <UiText v-if="vSetting.title?.enabled" v-model:setting="vSetting.title">
            <slot name="title" />
          </UiText>
          
          <!-- 额外内容 -->
          <slot />

          <!-- 确认按钮 -->
          <slot name="ok">
            <UiImage
              v-if="vSetting.okBtn?.imgLink"
              v-model:setting="vSetting.okBtn"
              :class="{ 'popup-ok': true, 'popup-ok__disabled': nook }"
              @click="handleOk"
            />
          </slot>
          
          <!-- 关闭按钮 -->
          <UiImage
            v-if="showClose && vSetting.closeBtn?.imgLink"
            v-model:setting="vSetting.closeBtn"
            :confined="false"
            @click="handleClose"
          />
        </Resizable>
      </ResizableProvider>
      
    </Popup>
  </div>
</template>

<script lang="ts" setup>
import { withDefaults, useAttrs, watchEffect, nextTick, computed } from 'vue';
import type { CSSProperties } from 'vue';
import useAdminStore from '@bish/store/src/modules/admin';
import Resizable from '../ui/resizable/index.vue';
import ResizableProvider from '../ui/resizable/resizable-provider.vue';
import UiImage from '../ui/image/index.vue';
import UiText from '../ui/text/index.vue';
import type { ResizableProps } from '../ui/resizable/index.vue';
import type { ImageSetting } from '../ui/image/index.vue';
import type { TextSetting } from '../ui/text/index.vue';
import Popup from '@bish/ui/src/popup/Popup.vue';
import type { PopupPosition } from '@bish/ui/src/popup/Popup.vue';
import { useVModel } from '@vueuse/core';
import './popup.less'

export type PopupSetting = ResizableProps & {
  /**
   * 弹窗背景
   */
  bgImage?: string
  /**
   * 关闭按钮
   */
  closeBtn?: ImageSetting
  /**
   * 操作按钮
   */
  okBtn?: ImageSetting
  /**
   * 弹窗标题
   */
  title?: TextSetting
  /**
   * 内容定位
   */
  position?: PopupPosition
}

export interface PopupProps {
  /**
   * 配置
   */
  setting: PopupSetting
  /**
   * 是否禁用 ok 按钮，默认 false
   */
  nook?: boolean
  /**
   * 是否可改变大小的
   */
  resizable?: ResizableProps['resizable']
  /**
   * 内容样式
   */
  contentStyle?: CSSProperties
  /**
   * 是否显示关闭按钮
   */
  showClose?: boolean
  /**
   * 是否阻止冒泡，默认 true
   */
  stopPropagation?: boolean

  [key: string]: any
}

const props = withDefaults(defineProps<PopupProps>(), {
  setting: () => ({} as PopupSetting),
  nook: false,
  resizable: () => ({
    n: true,
    e: true,
    s: true,
    w: true,
    nw: true,
    ne: true,
    sw: true,
    se: true,
  }),
  showClose: true,
  stopPropagation: true,
});

const emits = defineEmits<{
  (event: 'update:setting', value: PopupSetting): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
}>();

const attrs = useAttrs();

const adminStore = useAdminStore();

const vSetting = useVModel(props, 'setting', emits);

const providedPosition = computed(() => {
  return props.setting.position || 'center';
});

const handleClose = (e: Event) => {
  e.stopPropagation();
  emits('close', e);
};

const handleOk = (e: Event) => {
  e.stopPropagation();
  if (props.nook) {
    return;
  }
  emits('ok', e);
};

const handleOpen = () => {
  adminStore.setOverlay(true);
  // setOverflowHidden();
};

const handleClosed = () => {
  adminStore.setOverlay(false);
  // restoreOriginalOverflow();
};

// 获取 HTML 和 Body 元素
let $html: HTMLElement | null = null;
let $body: HTMLElement | null = null;

// #ifdef H5
$html = document.documentElement;
$body = document.body;

// 记录原始的 overflow-y 值
let originalOverflowY = '';

watchEffect(() => {
  if (attrs.show) {
    originalOverflowY = $html.style.overflowY || $body.style.overflowY;
  }
});
// #endif

/**
 * 设置 overflow-y 为 hidden
 */
const setOverflowHidden = () => {
  // #ifdef H5
  $html.style.overflowY = 'hidden';
  $body.style.overflowY = 'hidden';
  // #endif
}

/**
 * 恢复原始的 overflow-y 值
 */
const restoreOriginalOverflow = () => {
  // #ifdef H5
  $html.style.overflowY = originalOverflowY;
  $body.style.overflowY = originalOverflowY;
  // #endif
}

const handleStopPropagation = (e: Event) => {
  if (props.stopPropagation) {
    e.stopPropagation();
  }
}
</script>

<script lang="ts">
export default {
  options: {
    virtualHost: true,
    styleIsolation: 'shared',
  },
  name: 'common-popup',
}
</script>

<style>
.popup-content {
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  margin: 0 auto;
}
</style>