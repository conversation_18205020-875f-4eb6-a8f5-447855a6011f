import type { InviteModalProSetting, CombineSetting } from './invite-modal-pro';
import { defaultConfig as defaultPosterConfig } from './invite-modal-poster';
import { defaultConfig as defaultLinkConfig } from './invite-modal-link';

export * from './invite-modal-pro';

/**
 * 分享模式-复制链接
 */
export const ACTION_MODE_LINK = 1;
/**
 * 分享模式-分享海报
 */
export const ACTION_MODE_POSTER = 2;
/**
 * 分享模式-复制链接+分享海报
 */
export const ACTION_MODE_COMBINE = 3;


export const defaultCombineConfig = (): CombineSetting => {
  const linkConfig = defaultLinkConfig();
  return {
    link: {
      ...linkConfig,
      width: 375,
      height: 226,
      bgImage: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250312/1741776148770-invite-bg.png',
      linkText: undefined,
      copyLinkBtn: undefined,
      content: undefined,
      createPosterBtn: {
        x: 33,
        y: 146,
        width: 141,
        height: 43,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250312/1741775411348-invite-poster.png',
      },
      okBtn: {
        x: 199,
        y: 146,
        width: 141,
        height: 43,
        imgLink: 'https://bish-1256453865.cos.ap-shanghai.myqcloud.com/temp/20250312/1741775409030-invite-link.png',
      },
      closeBtn: {
        x: 0,
        y: 0,
        width: 0,
        height: 0,
        imgLink: '',
      },
      position: 'bottom',
    },
    poster: defaultPosterConfig(),
  };
};

export const defaultConfig = (): InviteModalProSetting => {
  return {
    actionMode: 1,
    link: defaultLinkConfig(),
  };
};
