import type { CommonSetting } from '@bish/types/src';
import type { PopupSetting } from '../../../../common/popup.vue';
import type { TextSetting } from '../../../../ui/text/index.vue';
import type { ImageSetting } from '../../../../ui/image/index.vue';

export interface CollectCardsAskForSetting {
  /**
   * 入口按钮
   */
  actionBtn: ImageSetting
  /**
   * 索要弹窗
   */
  modal: PopupSetting & {
    /**
     * 标题
     */
    title: ImageSetting
    /**
     * 卡片盒子
     */
    box: ImageSetting
    /**
     * 卡片
     */
    card: CommonSetting
    /**
     * 卡片名称
     */
    name: TextSetting
    /**
     * 当前拥有
     */
    current: TextSetting
  }
  /**
   * 没有卡片弹窗
   */
  noCardModal: PopupSetting & {
    /**
     * 标题
     */
    title: ImageSetting
    /**
     * 卡片盒子
     */
    box: ImageSetting
    /**
     * 卡片
     */
    card: CommonSetting
    /**
     * 卡片名称
     */
    name: TextSetting
    /**
     * 提示
     */
    tip: TextSetting
  }
  /**
   * 复制模板列表
   */
  copyTemplates: {
    /**
     * 卡片id
     */
    cardId: number
    /**
     * 标题文案
     */
    text: string
    /**
     * 海报，目前只有小程序页面分享才用到
     */
    poster?: string
  }[]
} 
