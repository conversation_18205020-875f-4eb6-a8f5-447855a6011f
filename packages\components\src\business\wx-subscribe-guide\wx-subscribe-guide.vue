<!--
* @Description: 开启微信消息订阅提示弹窗
-->
<template>
  <Popup
    z-index="100"
    :show="show"
    v-model:setting="setting"
    @close="handleClose"
    @ok="handleOk"
  />
</template>

<script lang="ts">
export interface WxSubscribeGuideProps {
  /**
   * 配置
   */
  setting: WxSubscribeGuideSetting
  /**
   * 是否显示
   */
  show: boolean
}

export default {
  name: 'wx-subscribe-guide',
}
</script>

<script lang="ts" setup>
import { withDefaults } from 'vue';
import { useVModel } from '@vueuse/core';
import Popup from '../../common/popup.vue';
import type { WxSubscribeGuideSetting } from './wx-subscribe-guide';
import { defaultConfig } from './index';

const props = withDefaults(defineProps<WxSubscribeGuideProps>(), {
  setting: defaultConfig,
  show: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: WxSubscribeGuideSetting): void;
  (event: 'close'): void;
  (event: 'ok'): void;
}>();

const setting = useVModel(props, 'setting', emits);

const handleClose = () => {
  emits('close');
};

const handleOk = () => {
  emits('ok');
};
</script>

<style>
.wx-subscribe-guide-content {}
</style>