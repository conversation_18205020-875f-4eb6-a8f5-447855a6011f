import { http, type Result, type CustomAxiosConfig } from '@bish/request'
import { useEnv } from '@bish/hooks/src/useEnv'
import type { MiniGameStartGameParams, MiniGameStartGameDetailType, MiniGameAcquireScoreParams, MiniGameAcquireRankListParams } from './types'

const { VITE_ACTIVITY_URL } = useEnv()

/**
 * @description 活动小游戏开始游戏接口
 * @param {string} params.act_acc_id 活动账号数据 由getAccountInfo接口提供
 * @param {number} params.act_id 活动id
 * @returns
 */
export function postActivityMiniGameStartGame(params: MiniGameStartGameParams) {
  return http.post<MiniGameStartGameDetailType>(`${VITE_ACTIVITY_URL}/activity/miniGame/startGame`, params)
}

/**
 * @description 小游戏结束后调用接口进行积分累计
 * @param {string} params.act_acc_id 活动账号数据 由getAccountInfo接口提供
 * @param {number} params.act_id 活动id
 * @param {string} params.score 积分
 * @param {string} params.uuid uuid
 * @returns
 */
export function postActivityMiniGameAcquireScore(params: MiniGameAcquireScoreParams) {
  return http.post<any>(`${VITE_ACTIVITY_URL}/activity/miniGame/acquireScore`, params)
}

/**
 * @description 小游戏排行榜数据
 * @param {number} params.act_id 活动id
 * @param {number} params.rank_type 排行榜类型 1个人 2队伍
 * @param {number} params.page 页数
 * @param {number} params.page_rows 每页数量
 * @returns
 */
export function postActivityMiniGameAcquireRankList(params: MiniGameAcquireRankListParams, config?: Partial<CustomAxiosConfig>) {
  return http.post<any>(`${VITE_ACTIVITY_URL}/activity/miniGame/getRankList`, params, { needToken: false, ...config })
}
