<!--
* @Description: 集卡-卡片列表
-->
<template>
  <Resizable
    v-model:x="setting.x"
    v-model:y="setting.y"
    v-model:width="setting.width"
    v-model:height="setting.height"
    class="collect-cards-list"
  >
    <div 
      ref="listRef"
      class="collect-cards-list-scroll"
    >
      <div class="collect-cards-list-wrapper">
        <div
          v-for="(item, index) in list"
          :key="item.id"
          :ref="el => itemRefs[index] = el as HTMLDivElement"
          class="collect-cards-list-item"
        >
          <Resizable
            v-model:x="setting.item.x"
            v-model:y="setting.item.y"
            v-model:width="setting.item.width"
            v-model:height="setting.item.height"
            :movable="false"
            @click="handleSelect(index)"
          >
            <div class="collect-cards-list-item-content">
              <!-- 卡片 -->
              <CollectCardsItem
                :setting="setting.itemCard"
                :style="{
                  outline: current === index ? borderStyle : 'none',
                }"
                :locked="!receiveLogLength(item)"
                :show-locked-txt="false"
                :data="item"
                active
              />
      
              <!-- 卡片名称 -->
              <UiText
                v-if="setting.name.enabled"
                :setting="setting.name"
                :confined="false"
              >
                {{ item.name }}
              </UiText>
      
              <!-- 角标 -->
              <UiText
                v-if="setting.badge.enabled && showBadge(item)"
                :setting="setting.badge"
                :confined="false"
                :style="{
                  backgroundImage: `url(${setting.badge.bgImage})`,
                }"
                class="collect-cards-list-item-badge"
              >
                {{ userCardInfo(item.id)?.receive_log.length || 0 }}
              </UiText>
            </div>
          </Resizable>
        </div>
      </div>
    </div>
  </Resizable>
</template>

<script lang="ts">
export default {
  name: 'collect-cards-list',
}
</script>

<script lang="ts" setup>
import { computed, watch, ref } from 'vue';
import { useVModel } from '@vueuse/core';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { pxTransform } from '@bish/utils/src/viewport';
// import { useLog } from '@bish/hooks/src/useLog';
import type {  ActivityInfoConfigCollectDrawDetail, ComponentWithUserInfoCollectDraw } from '@bish/api/src/activity';
import Resizable from '../../../../ui/resizable/index.vue';
import UiText from '../../../../ui/text/index.vue';
import type { CollectCardsListSetting } from './collect-cards-list';
import { defaultConfig } from './index';
import CollectCardsItem from '../collect-cards-item/collect-cards-item.vue';

export interface CollectCardsProps {
  setting: CollectCardsListSetting
  /**
   * 当前激活卡片
   */
  current: number
  /**
   * 卡片列表
   */
  list: ActivityInfoConfigCollectDrawDetail
  /**
   * 用户集卡信息
   */
  userCollectDraw: ComponentWithUserInfoCollectDraw
}

const props = withDefaults(defineProps<CollectCardsProps>(), {
  setting: () => defaultConfig(),
  active: true,
  list: () => [],
});

const emits = defineEmits<{
  (event: 'update:setting', value: CollectCardsListSetting): void;
  (event: 'update:current', value: number): void;
  (event: 'change', value: number): void;
  (event: 'scratch', value: any): void;
}>();

const setting = useVModel(props, 'setting', emits);

const activityStore = useActivityStore();
const adminStore = useAdminStore();

// const { uploadLog } = useLog();

const borderStyle = computed(() => {
  return `${pxTransform(setting.value.itemCard.card.width * 0.05)} solid ${setting.value.name.color}`;
});

/**
 * 当前选中用户卡片信息
 */
const userCardInfo = (cardId: number) => {
  return props.userCollectDraw?.card_list.find(item => item.id === cardId);
};

const receiveLogLength = (item: ActivityInfoConfigCollectDrawDetail[number]) => {
  const cardInfo = userCardInfo(item.id);
  return cardInfo ? cardInfo?.receive_log?.length : 0;
};

const showBadge = (item: ActivityInfoConfigCollectDrawDetail[number]) => {
  return adminStore.editable || receiveLogLength(item) > 1;
};

const listRef = ref<HTMLDivElement>();
const itemRefs = ref<HTMLDivElement[]>([]);

const handleSelect = (index: number) => {
  emits('update:current', index);
  emits('change', index);
  
  // 滚动到选中项
  scrollToItem(index);
};

// 滚动到指定项
const scrollToItem = (index: number) => {
  const container = listRef.value;
  const item = itemRefs.value[index];
  
  if (!container || !item) return;
  
  // 获取容器和元素的尺寸信息
  const containerRect = container.getBoundingClientRect();
  const itemRect = item.getBoundingClientRect();
  
  // 计算目标滚动位置（使item居中）
  const scrollLeft = item.offsetLeft - (containerRect.width - itemRect.width) / 2;
  
  // 平滑滚动
  container.scrollTo({
    left: scrollLeft,
    behavior: 'smooth'
  });
};

// 查找第一张已获得的卡片索引
const findFirstActiveCardIndex = () => {
  if (!props.userCollectDraw?.card_list?.length) return 0;
  
  const index = props.list.findIndex(item => {
    const userCard = props.userCollectDraw.card_list.find(card => card.id === item.id);
    return userCard && userCard.receive_log?.length > 0;
  });

  return index >= 0 ? index : 0;
};

watch(
  () => props.userCollectDraw?.card_list,
  (newValue, oldValue) => {
    if (!oldValue?.length && newValue?.length) {
      const activeIndex = findFirstActiveCardIndex();
      handleSelect(activeIndex);
    }
  },
);
</script>

<style>
.collect-cards-list {
  position: relative;
}

.collect-cards-list-scroll {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  overflow-x: auto;
  scroll-behavior: smooth;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.collect-cards-list-wrapper {
  display: inline-flex;
  white-space: nowrap;
}

.collect-cards-list-item {
  display: inline-block;
  flex-shrink: 0;
}

.collect-cards-list-item-content {
  position: relative;
  min-height: 100%;
  min-width: 100%;
}

.collect-cards-list-item-card,
.collect-cards-list-item-badge {
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
</style> 
