<template>
  <Popup
    z-index="102"
    :show="open"
    :lock-scroll="false"
    v-model:setting="setting.modalSetting"
    @close="handleClose"
    @ok="handleConfirm"
  >
    <!-- 提示文案 -->
    <UiText v-model:setting="setting.modalSetting.tipText" />
    
    <!-- 表单配置 -->
    <Resizable
      v-model:x="setting.modalSetting.form.position.x"
      v-model:y="setting.modalSetting.form.position.y"
      v-model:width="setting.modalSetting.form.position.width"
      v-model:height="setting.modalSetting.form.position.height"
      class="real-name-form"
    >
      <div class="real-name-form-item">
        <div class="real-name-form-item-label" :style="labelStyle">
          姓<span class="real-name-form-item-label-slot">姓名</span>名
        </div>
        <div class="real-name-form-item-control">
          <input
            v-model="form.card_name"
            placeholder="请输入姓名"
            class="real-name-form-input"
            :style="inputStyle"
          />
        </div>
      </div>
      <div class="real-name-form-item">
        <div class="real-name-form-item-label" :style="labelStyle">
          身份证号
        </div>
        <div class="real-name-form-item-control">
          <input
            v-model="form.card_id"
            placeholder="请输入身份证号"
            class="real-name-form-input"
            :style="inputStyle"
            maxlength="18"
            @input="handleIdInput"
          />
        </div>
      </div>
    </Resizable>
  </Popup>
</template>

<script lang="ts">
import { pxTransform } from '@bish/utils/src/viewport';
import { sleep } from '@bish/utils/src/utils';
import type { PopupSetting } from '../common/popup.vue';
import type { TextSetting } from '../ui/text/index.vue';
import type { ResizableProps } from '../ui/resizable/index.vue';
import type { StatefulComponent } from '@bish/types/src/admin';

export interface RealNameAuthSetting {
  /**
   * 弹窗配置
   */
  modalSetting: PopupSetting & {
    /**
     * 提示文字
     */
    tipText: TextSetting
    /**
     * 表单配置
     */
    form: {
      /**
       * 位置
       */
      position: ResizableProps
      /**
       * 输入框配置
       */
      input: {
        /**
         * 高度
         */
        height?: number
        /**
         * 背景
         */
        background?: string
        /**
         * 颜色
         */
        color?: string
      }
      /**
       * label 配置
       */
      label: {
        /**
         * 字体颜色
         */
        color: string
      }
    }
  }
}

export interface RealNameAuthProps {
  /**
   * 配置
   */
  setting: RealNameAuthSetting
  /**
   * 状态
   */
  status?: StatefulComponent['status']
}

export const defaultConfig = (): RealNameAuthSetting => {
  return {
    modalSetting: {
      x: 0,
      y: 0,
      width: 348,
      height: 302,
      bgImage: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713155226132_.png',
      tipText: {
        x: 56,
        y: 93,
        width: 236,
        height: 20,
        fontSize: 14,
        color: '#5A8B82',
        align: 'center',
        content: '完成实名认证即可参与后续流程',
      },
      form: {
        position: {
          x: 51,
          y: 126,
          width: 240,
          height: 66,
        },
        input: {
          height: 28,
          background: 'rgba(153, 137, 127, 0.5)',
          color: '#5A8B82',
        },
        label: {
          color: '#5A8B82',
        },
      },
      okBtn: {
        x: 113,
        y: 220,
        width: 116,
        height: 33,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713155231785_.png',
      },
      closeBtn: {
        x: 302,
        y: 15,
        width: 21,
        height: 20,
        imgLink: 'https://customer-media-1256453865.cos.ap-guangzhou.myqcloud.com/temp/2024-4-15/1713155242155_.png',
      },
    },
  };
};

export default {
  name: 'real-name'
}
</script>

<script lang="ts" setup>
import { withDefaults, computed, watch, reactive } from 'vue';
import type { CSSProperties } from 'vue';
import { useVModel } from '@vueuse/core';
import { showToast } from '@bish/ui/src/toast';
import useUserStore from '@bish/store/src/modules/user';
import usePopupStore from '@bish/store/src/modules/popup';
import useActivityStore from '@bish/store/src/modules/activity';
import useAdminStore from '@bish/store/src/modules/admin';
import { cardAuth } from '@bish/api/src/user';
import { getUserInfo } from '@bish/utils/src/storage/modules/login';
import useControllableStatus from '@bish/hooks/src/business/useControllableStatus';
import Resizable from '../ui/resizable/index.vue';
import UiText from '../ui/text/index.vue';
import Popup from '../common/popup.vue';

const props = withDefaults(defineProps<RealNameAuthProps>(), {
  setting: defaultConfig,
});

const emits = defineEmits<{
  (event: 'update:setting', value: RealNameAuthSetting): void;
  (event: 'update:status', value: StatefulComponent['status']): void;
  (event: 'close', e: Event): void;
  (event: 'ok', e: Event): void;
}>();

const form = reactive({
  card_name: '',
  card_id: '',
});

const userStore = useUserStore();
const popupStore = usePopupStore();
const activityStore = useActivityStore();
const adminStore = useAdminStore();

const setting = useVModel(props, 'setting', emits);

const [open, toggle] = useControllableStatus(props, emits, { fieldName: 'showRealNameAuthModal' });

let lock = false;

const labelStyle = computed<CSSProperties>(() => {
  const { setting } = props;
  return {
    color: setting.modalSetting.form.label.color,
    fontSize: pxTransform(14),
    marginRight: `${pxTransform(14)}`,
    width: `${pxTransform(58)}`,
  };
});

const inputStyle = computed<CSSProperties>(() => {
  const { setting } = props;
  return {
    color: setting.modalSetting.form.input.color,
    backgroundColor: setting.modalSetting.form.input.background,
    height: pxTransform(setting.modalSetting.form.input.height!),
    padding: `0 ${pxTransform(12)}`,
    fontSize: pxTransform(12),
    border: 'none',
    outline: 'none',
    width: '100%',
  };
});

watch(
  () => popupStore.showRealNameAuth,
  (newVal) => {
    toggle(newVal);
  },
);

const handleClose = () => {
  if (!popupStore.showRealNameAuth) {
    toggle(false);
  }
  popupStore.setShowRealNameAuth(false);
};

const handleIdInput = () => {
  // 过滤非数字、'x' 和 'X' 字符
  form.card_id = form.card_id.replace(/[^0-9xX]/g, '').substring(0, 18);
};

const handleConfirm = async () => {
  try {
    if (!form.card_name) {
      showToast('请输入真实姓名')
      return
    }
    if (!form.card_id) {
      showToast('请输入身份证号')
      return
    }
    if (lock) {
      return;
    }
    lock = true;
    const res = await cardAuth({
      card_id: form.card_id,
      card_name: form.card_name,
    });

    if (res.code === 0) {
      const userInfo = getUserInfo();
      if (userInfo) {
        // 手动修改实名值
        userInfo.is_real = true;
      }
      userStore.setUserData(userInfo);

      // 等待 1 秒后，再请求活动账号信息，后端不是同步的
      await sleep(1000);
      activityStore.getActivityAccountInfo();
      
      showToast('实名认证成功');

      handleClose();
      
      // 执行实名认证回调
      userStore.workRealNameAuthTask();
    }
  } catch (error) {
    console.warn('实名认证失败', error);
  } finally {
    lock = false;
  }
};
</script>

<style lang="less">
.real-name {
  &-form {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-item {
      display: flex;
      align-items: center;

      &-label {
        position: relative;
        font-weight: 600;
        white-space: nowrap;

        &-slot {
          opacity: 0;
        }

        &::after {
          content: ':';
          position: absolute;
          top: 0;
          right: -6PX;
        }
      }

      &-control {
        flex: 1;
      }
    }

    &-input {}
  }
}
</style>