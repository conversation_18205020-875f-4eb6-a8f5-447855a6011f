import type { CommonSetting, CommonBackgroundSetting } from '@bish/types/src';
import type { PopupSetting } from '../../../../common/popup.vue';
import type { TextSetting } from '../../../../ui/text/index.vue';
import type { ImageSetting } from '../../../../ui/image/index.vue';
import type { CongratulationsContentSetting } from '../../../../common/congratulations-modal/congratulations-content';

export interface CollectCardsScratchSetting extends PopupSetting {
  /**
   * 标题
   */
  title: ImageSetting
  /**
   * 卡片盒子
   */
  box: ImageSetting
  /**
   * 卡片
   */
  card: CommonSetting
  /**
   * 刮刮乐
   */
  scratch: ImageSetting
  /**
   * 恭喜获得
   */
  congrats: CommonBackgroundSetting & CongratulationsContentSetting
  /**
   * 卡片名称
   */
  name: TextSetting
  /**
   * 恭喜获得蒙层
   */
  congratsMask: ImageSetting
  /**
   * 刮刮乐背景
   */
  scratchBg: string
} 
