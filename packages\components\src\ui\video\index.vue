<template>
  <Resizable
    v-bind="attrs"
    v-model:x="vSetting.x"
    v-model:y="vSetting.y"
    v-model:width="vSetting.width"
    v-model:height="vSetting.height"
    @click="(e) => emits('click', e)"
    :style="{ fontSize: 0 }"
  >
    <video
      v-if="vSetting.videoLink"
      :src="vSetting.videoLink"
      :style="videoStyle"
      :poster="vSetting.cover || ''"
      controls
    />
    <div v-else class="ui-video-empty" :style="videoStyle">
      <IconEmpty />
    </div>
  </Resizable>
</template>

<script lang="ts" setup>
import { computed, useAttrs } from 'vue';
import { useVModel } from '@vueuse/core';
import { pxTransform } from '@bish/utils/src/viewport';
import { useLog } from '@bish/hooks/src/useLog';
import Resizable from '../resizable/index.vue';
import type { ResizableProps } from '../resizable/index.vue';
import type { LinkProps } from '../link/index.vue';
import IconEmpty from '../image/icons/icon-empty.vue';

export type VideoSetting = ResizableProps & LinkProps & {
  /**
   * 视频地址
   */
  videoLink?: string
  /**
   * 视频封面
   */
  cover?: string
}

export interface VideoProps {
  /**
   * 配置
   */
  setting: VideoSetting
  /**
   * 长按图片显示发送给朋友，微信小程序属性，默认 false
   * 注意该属性准备废弃，请走 setting.showMenuByLongpress
   */
  showMenuByLongpress?: boolean

  [key: string]: any
}

const props = withDefaults(defineProps<VideoProps>(), {
  setting: () => ({} as VideoSetting),
  showMenuByLongpress: false,
});

const emits = defineEmits<{
  (event: 'update:setting', value: VideoSetting): void;
  (event: 'quickImg', value: VideoSetting, fieldName: string): void;
  (event: 'click', value: MouseEvent): void;
}>();

const attrs = useAttrs();
const vSetting = useVModel(props, 'setting', emits);

const { uploadLog } = useLog();

const videoStyle = computed(() => {
  return {
    width: `${pxTransform(props.setting.width as number)}`,
    height: `${pxTransform(props.setting.height as number)}`,
    zIndex: 1 ,
    position: 'absolute'
  }
});

</script>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ui-video',
});
</script>

<style>
.ui-video {}

.ui-video img {
  user-drag: none;
  user-select: none;
}

.ui-video-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('./icons/transparent.png');
}

.ui-video-empty > .svg-icon {
  width: 50%;
  height: auto;
}

.ui-video:hover .ui-video-quick {
  display: block;
}

.ui-video-quick {
  display: none;
  padding: 6px;
  position: absolute;
  left: -6px;
  top: -34px;
}

.ui-video-quick-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  cursor: pointer;
}

.ui-video-quick-content > .svg-icon {
  width: 60%;
  height: auto;
}

.ui-video-affix {
  position: fixed;
  z-index: 98;
}
</style>
